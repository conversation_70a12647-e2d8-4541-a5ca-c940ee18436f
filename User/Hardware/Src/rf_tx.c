//
// Created by <PERSON><PERSON> on 2024/10/21.
//

#include "rf_tx.h"
#include "rf_module.h"
#include "system.h"
#include "view_model.h"
#include "nikon_protocol.h"
#include "flash.h"

RFTXStruct RF_TX;

// void rf_send(void (*func)()) {
//     pa_tx_on();
//     func();
//     pa_tx_off();
// }

// ISO、光圈
void rf_send_ISO_zoom_0x8E() {
    // MasterRam.APISO_TxCnt++;
    // if (MasterRam.APISO_TxCnt >= 20) {
    //     MasterRam.APISO_TxCnt = 0;
    RF.tx_data_buf[1] = 0x05;
    RF.tx_data_buf[2] = 0x8E;
    // ISO
    RF.tx_data_buf[3] = Nikon.rx_data[4];
    // Aperture
    RF.tx_data_buf[4] = Nikon.rx_data[12];
    RF.tx_data_buf[5] = 0;
    RF.tx_data_buf[6] = 0;

    cc2500_transmit_data_pgr(7);
    // }
}

// 获取各组模式与闪光灯等级并整理数据协议格式到发送缓冲区
void rf_get_flash_level(const uint8_t ttl_value[GroupItemCount]) {
    RF.tx_data_buf[3] = 0;
    for (int i = 0; i < GroupItemCount; i++) {
        if (Param.group[i].mode != mode_OFF && Param.group[i].is_added_to_list) {
            // 通过左移位设置对应的位
            RF.tx_data_buf[3] |= (1 << i);
        }
    }

    RF.tx_data_buf[4] = 0;
    for (int i = 0; i < GroupItemCount; i++) {
        if (Param.group[i].mode == mode_TTL) {
            // 通过左移位设置对应的位
            RF.tx_data_buf[4] |= (0x01 << i);
            RF.tx_data_buf[5 + i] = ttl_value[i];
        } else if (Param.group[i].mode == mode_M) {
            RF.tx_data_buf[5 + i] = (Param.group[i].flash_level_M + 0xEE - set_flash_level_max());
        }
    }
}

// 获取最终的TTL能级
uint8_t rf_get_final_ttl_level(uint8_t camera_ttl_code, uint8_t level_ev) {
    uint8_t i;

    // 亮度过亮调节
    // camera_ttl_code -= 15;

    if (level_ev >= 9) {
        i = TTLFlash_EV_Table[level_ev];
        camera_ttl_code += i;
        if (camera_ttl_code >= 0x9F) {
            camera_ttl_code = 0x9F;
        }
    } else {
        i = TTLFlash_EV_Table[level_ev];
        if (camera_ttl_code > (i + 0x20)) {
            camera_ttl_code -= i;
        } else {
            camera_ttl_code = 0x20;
        }
    }

    return camera_ttl_code;
}

// 0xDA 0xDB 0xDC
// 第一次预闪
void rf_send_pre_flash1(uint8_t tData) {
    RF.tx_data_buf[1] = 0x01;
    RF.tx_data_buf[2] = tData;
    RF.tx_data_buf[3] = 0;
    RF.tx_data_buf[4] = 0;
    cc2500_transmit_data_pgr(5);

    if (tData == 0x2A) {
        RF_TX.pre_flash_count = 1;
    } else if (tData == 0x2C) {
        RF_TX.pre_flash_count = 2;
    } else if (tData == 0x2E) {
        RF_TX.pre_flash_count = 3;
    }
}

// 0xDD
// 第二次预闪
void rf_send_pre_flash2() {
    RF.tx_data_buf[1] = 0x01;

    if (RF_TX.pre_flash_count == 1) {
        RF.tx_data_buf[2] = 0x2B;
    } else if (RF_TX.pre_flash_count == 2) {
        RF.tx_data_buf[2] = 0x2D;
    } else if (RF_TX.pre_flash_count == 3) {
        RF.tx_data_buf[2] = 0x2F;
    }

    RF.tx_data_buf[3] = 0;
    RF.tx_data_buf[4] = 0;

    cc2500_transmit_data_pgr(5);
    RF_TX.pre_flash_count = 0;
}

void rf_send_rise_0x57() {
    RF.tx_data_buf[1] = 0x01;
    RF.tx_data_buf[2] = 0x57;
    RF.tx_data_buf[3] = 0;
    cc2500_transmit_data_pgr(4);
}


void rf_send_0x5C_start() {
    pa_tx_on();
    RF.tx_data_buf[1] = 0x01;
    RF.tx_data_buf[2] = 0x5C;
    RF.tx_data_buf[3] = 0;
    cc2500_write_reg_arr(CC2500_TXFIFO, RF.tx_data_buf, 4);
    cc2500_send_cmd(Cmd_STX);
}


void rf_send_0x5C_end() {
    delay_us(500);
    cc2500_send_cmd(Cmd_SFTX);
    cc2500_send_cmd(Cmd_SIDLE);
    cc2500_send_cmd(Cmd_SCAL);
    pa_tx_off();
}

// 快门保持
void rf_send_shutter_hold_0x59() {
    RF.tx_data_buf[1] = 0x01;
    RF.tx_data_buf[2] = 0x59;
    cc2500_transmit_data_pgr(3);
}

// 获取频闪参数
void rf_get_Multi_param() {
    RF.tx_data_buf[3] = 0;
    RF.tx_data_buf[4] = 0;
    RF.tx_data_buf[5] = 0;
    for (int i = 0; i < GroupItemCount; i++) {
        if (Param.group[i].is_turn_on_Multi) {
            RF.tx_data_buf[3] |= (1 << i);
        }
    }

    for (int i = 0; i < hz_arr_size; i++) {
        if (Param.multi.flash_freq <= multi_hz_arr[i]) {
            RF.tx_data_buf[4] = i;
            break;
        }
    }

    for (int i = 0; i < times_arr_size; i++) {
        if (Param.multi.flash_times <= multi_times_arr[i]) {
            RF.tx_data_buf[5] = i;
            break;
        }
    }

    RF.tx_data_buf[6] = Param.multi.flash_level * 10 + 0xEE - set_flash_level_max();
}

// 发送频闪参数
void rf_send_Multi_param_0x8D() {
    RF.tx_data_buf[1] = 0x05;
    RF.tx_data_buf[2] = 0x8D;

    rf_get_Multi_param();
    cc2500_transmit_data_pgr(7);
}

// 发送TTL参数
void rf_send_0xD4() {
    rf_send_ISO_zoom_0x8E();
    delay_us(1800);
    if (Param.is_Multi_page) {
        RF.tx_data_buf[1] = 0x05;
        RF.tx_data_buf[2] = 0x3C;

        rf_get_Multi_param();
        cc2500_transmit_data_pgr(7);
    } else {
        RF.tx_data_buf[1] = 0x08;
        if (Nikon.shut_data[0]) {
            // 高速同步
            RF.tx_data_buf[2] = 0x3B;
        } else {
            RF.tx_data_buf[2] = 0x3A;
        }

        uint8_t final_ttl_level_A = rf_get_final_ttl_level(Nikon.shut_data[2], Param.group[group_A].flash_level_TTL);
        uint8_t final_ttl_level_B = rf_get_final_ttl_level(Nikon.shut_data[3], Param.group[group_B].flash_level_TTL);
        uint8_t final_ttl_level_C = rf_get_final_ttl_level(Nikon.shut_data[4], Param.group[group_C].flash_level_TTL);
        uint8_t ttl_value[GroupItemCount - 2] = {final_ttl_level_A, final_ttl_level_B, final_ttl_level_C};
        rf_get_flash_level(ttl_value);
        cc2500_transmit_data_pgr(10);
    }
}

void rf_get_zoom() {
    for (int i = 0; i < GroupItemCount; i++) {
        // 0x00 AUTO
        // 0x01 20mm
        // 0x02 24mm
        // 0x03 28mm
        // 0x04 35mm
        // 0x05 50mm
        // 0x06 70mm
        // 0x07 80mm
        // 0x08 105mm
        // 0x09 135mm
        // 0x0A 200mm
        if (Param.group[i].is_auto_zoom) {
            RF.tx_data_buf[i + 3] = Param.group[i].auto_zoom_level + 1;
        } else {
            RF.tx_data_buf[i + 3] = Param.group[i].zoom_level + 1;
        }
    }
}

void rf_send_zoom_0x5A() {
    RF.tx_data_buf[1] = 0x06;
    RF.tx_data_buf[2] = 0x5A;
    rf_get_zoom();
    cc2500_transmit_data_pgr(RF.tx_data_buf[1] + 2);
}

void rf_get_lamp() {
    RF.tx_data_buf[3] = 0;
    // 最高位-总开关
    RF.tx_data_buf[3] = Setting.lamp_main_switch << 7;
    for (int i = 0; i < GroupItemCount; i++) {
        // 0~4-A~E组开关
        // if (Param.group[i].stylish_lamp_switch) {
        if (Param.group[i].stylish_lamp_mode == lamp_manual) {
            RF.tx_data_buf[3] |= (0x01 << i);
        }
        if (Param.group[i].stylish_lamp_mode == lamp_off || !Param.group[i].is_added_to_list) {
            RF.tx_data_buf[3] |= (0x00 << i);
        }
        RF.tx_data_buf[i + 4] = Param.group[i].stylish_lamp_level / (MAX_LAMP_LEVEL / 10);
    }
}

// 发送造型灯与蜂鸣器参数
void rf_send_beep_lamp_0x8A() {
    RF.tx_data_buf[1] = 0x08;
    RF.tx_data_buf[2] = 0x8A;
    rf_get_lamp();
    // Beep: 0x01-关闭,0x02-开启
    if (Setting.values[setting_beep] == beep_on) {
        RF.tx_data_buf[9] = 0x02;
    } else {
        RF.tx_data_buf[9] = 0x01;
    }
    cc2500_transmit_data_pgr(RF.tx_data_buf[1] + 2);
}

// 发送能级、模式、高速
void rf_send_mode_level_0x8B() {
    rf_send_zoom_0x5A();
    delay_ms(3);
    rf_send_beep_lamp_0x8A();
    delay_ms(3);

    RF.tx_data_buf[1] = 0x08;
    if (Param.status_bar_sync == sync_high_speed) {
        // 高速同步
        RF.tx_data_buf[2] = 0x8C;
    } else {
        // 前帘
        RF.tx_data_buf[2] = 0x8B;
    }
    uint8_t ttl_value[GroupItemCount] = {0x20, 0x20, 0x20};
    rf_get_flash_level(ttl_value);
    cc2500_transmit_data_pgr(10);
}

void rf_send_master_pilot() {
    pa_tx_on();
    if (!Param.is_Multi_page) {
        // cc2500_send_cmd(Cmd_SCAL);
        // delay_ms(10);
        // 单触点不发送其他参数
        if (!Setting.values[setting_shoot]) {
            rf_send_mode_level_0x8B();
            delay_ms(3);
        }
        RF.tx_data_buf[1] = 0x01;
        RF.tx_data_buf[2] = 0x6B;
        cc2500_transmit_data_pgr(3);
        // delay_us(150);
        // cc2500_transmit_data_pgr(3);
    } else {
        RF.tx_data_buf[1] = 0x05;
        RF.tx_data_buf[2] = 0x3C;
        rf_get_Multi_param();
        cc2500_transmit_data_pgr(7);
        delay_ms(10);
        RF.tx_data_buf[1] = 0x03;
        RF.tx_data_buf[2] = 0x5C;
        RF.tx_data_buf[3] = 0;
        RF.tx_data_buf[4] = 0;
        cc2500_transmit_data_pgr(5);

        uint8_t check_multi_switch = 0;
        for (int j = 0; j < GroupItemCount; ++j) {
            check_multi_switch |= (Param.group[j].is_turn_on_Multi & 1) << j;
        }
        if (check_multi_switch) {
            flash_multi_mode(pilot_key_event | rf_tx_event, Param.multi.flash_level, Param.multi.flash_times,
                             Param.multi.flash_freq);
        }
    }
    pa_tx_off();
}

// 造型闪光
void rf_send_pv_0x6A() {
    RF.tx_data_buf[1] = 0x02;
    RF.tx_data_buf[2] = 0x6A;
    for (int i = 0; i < GroupItemCount; i++) {
        // 0~4-A~E组开关
        if (Param.group[i].mode == mode_OFF || !Param.group[i].is_added_to_list) {
            RF.tx_data_buf[3] |= (0x00 << i);
        } else {
            RF.tx_data_buf[3] |= (0x01 << i);
        }
    }
    cc2500_transmit_data_pgr(4);
}

/**
 * @brief 扩展协议1 无线同步
 */
void rf_send_ch_id_0xED_0x01() {
    RF.tx_data_buf[1] = 0x04;
    RF.tx_data_buf[2] = 0xED;
    RF.tx_data_buf[3] = 0x01;
    RF.tx_data_buf[4] = Setting.roller_values[roller_setting_ch];
    RF.tx_data_buf[5] = Setting.roller_values[roller_setting_id];
    cc2500_transmit_data_pgr(RF.tx_data_buf[1] + 2);
}

/**
 * @brief 扩展协议2 TCM
 */
void rf_send_tcm_0xED_0x02() {
    // 没开启TCM不发送
    if (!Setting.values[setting_tcm]) {
        return;
    }
    RF.tx_data_buf[1] = 0x08;
    RF.tx_data_buf[2] = 0xED;
    RF.tx_data_buf[3] = 0x02;
    // 总开关
    RF.tx_data_buf[4] = Setting.values[setting_tcm] << 7;
    for (int i = 0; i < GroupItemCount; i++) {
        // 0~4-A~E组开关
        if (Param.group[i].mode == mode_OFF || !Param.group[i].is_added_to_list) {
            RF.tx_data_buf[4] |= (0x00 << i);
        } else {
            RF.tx_data_buf[4] |= (0x01 << i);
        }
        RF.tx_data_buf[i + 5] = Param.group[i].flash_level_M_tcm + 0x80;
    }
    cc2500_transmit_data_pgr(RF.tx_data_buf[1] + 2);
    delay_ms(2);
}

void rf_send_from_ui() {
    if (Param.RF_send_ref) {
        if (Param.RF_send_ref & RF_lamp_beep_0x8A) {
            rf_send_beep_lamp_0x8A();
            delay_us(20);
        }
        if (Param.RF_send_ref & RF_mode_level_0x8B) {
            rf_send_tcm_0xED_0x02();
            delay_us(20);
            rf_send_mode_level_0x8B();
            delay_us(20);
        }
        if (Param.RF_send_ref & RF_Multi_param_0x8D) {
            rf_send_Multi_param_0x8D();
            delay_us(20);
        }
        if (Param.RF_send_ref & RF_zoom_0x5A) {
            rf_send_zoom_0x5A();
            delay_us(20);
        }
        Param.RF_send_ref = RF_ref_none;
    }
}