//
// Created by <PERSON><PERSON> on 2024/5/30.
//

#include "my_main.h"
#include "system.h"

// 入口地址偏移量
#define VECT_TAB_OFFSET 0x24000U
#define NVIC_VECTTAB_FLASH ((uint32_t)0x08000000)

// 原修改处为system_stm32h5xx.c的
// #define VECT_TAB_OFFSET  0x00U /*!< Vector Table base offset field.
//                                    This value must be a multiple of 0x200. */
// 将偏移量设置为所需空间
// #define VECT_TAB_OFFSET  0x22000U /*!< Vector Table base offset field.
//                                    This value must be a multiple of 0x200. */
void vector_offset_init() {
    // SET VECT_TAB_OFFSET
    SCB->VTOR = NVIC_VECTTAB_FLASH | VECT_TAB_OFFSET;
}

void My_SystemClock_Config_DeInit() {
//    HAL_XSPI_DeInit(&hospi1);
    __HAL_RCC_GPIOA_CLK_DISABLE();
    __HAL_RCC_GPIOB_CLK_DISABLE();
    __HAL_RCC_GPIOC_CLK_DISABLE();
    __HAL_RCC_GPIOD_CLK_DISABLE();
    __HAL_RCC_GPIOE_CLK_DISABLE();
    __HAL_RCC_GPIOF_CLK_DISABLE();
    __HAL_RCC_GPIOG_CLK_DISABLE();
    __HAL_RCC_GPIOH_CLK_DISABLE();
    __HAL_RCC_GPDMA1_CLK_DISABLE();
    __HAL_RCC_GPDMA2_CLK_DISABLE();
    __HAL_RCC_OSPI1_CLK_DISABLE();
}

void my_main() {
    // HAL_SuspendTick();
    // 使能PWR时钟
    // __HAL_RCC_WAKEUPSTOP_CLK_CONFIG(RCC_STOP_WAKEUPCLOCK_HSI);
    // 清除唤醒标记
    // __HAL_PWR_CLEAR_FLAG(PWR_FLAG_WU);

    // https://blog.csdn.net/lmx11040101/article/details/85785336
    // 避免SysTick唤醒而无法进入休眠
    // 关闭定时器
    // SysTick->CTRL = 0x00;
    // 清空val,清空定时器
    // SysTick->VAL = 0x00;
    // 暂停SysTick的计时，因为这个是CubeMX配置的工程，默认是开启了SysTick1的中断，所有这里需要停止计时，避免从睡眠模式唤醒
    // HAL_SuspendTick();
    // 进入停止模式，开启低功耗模式，使用WFI指令进入
    // HAL_PWR_EnterSTOPMode(PWR_MAINREGULATOR_ON, PWR_STOPENTRY_WFI);
    // 由于停止模式会关闭时钟，所有需要重新配置时钟
    // SystemClock_Config();
    // 开启SysTick定时器
    // HAL_ResumeTick();

    // 不反初始化会导致SPI超时
    // HAL_SPI_DeInit(&hspi1);
    // do {
    //     // HAL_Delay(10);
    //     HAL_PWR_EnterSTOPMode(PWR_MAINREGULATOR_ON, PWR_STOPENTRY_WFI);
    // } while (!System.switch_status);
    // SystemClock_Config();
    // MX_GPIO_Init();
    // MX_GPDMA1_Init();
    // MX_OCTOSPI1_Init();
    // MX_ICACHE_Init();
    // MX_TIM6_Init();
    // MX_I2C1_Init();
    // MX_DCACHE1_Init();
    // MX_USART3_UART_Init();
    // MX_ADC1_Init();
    // MX_SPI1_Init();
    // SysTick->VAL = 0x00;
    // HAL_ResumeTick();
    SystemClock_Config_8M();
    // SystemClock_Config_50M();
    user_pgr();
}