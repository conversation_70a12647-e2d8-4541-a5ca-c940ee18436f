//
// Created by <PERSON><PERSON> on 2024/12/26.
//

#include <stdbool.h>
#include <stdio.h>
#include <string.h>
#include "data_storage.h"
#include "crc32_algorithm.h"
#include "user.h"
#include "view_model.h"
#include "system.h"
#include "adc_dma.h"
#include "multi_language.h"

#define MAX_FLASH_LEVEL_M 90
#define MAX_FLASH_LEVEL_MULTI 7

StorageStructType Storage = {
        .Data = {
                // 30
                {7, &Param.group[group_A].flash_level_M,            0,        MAX_FLASH_LEVEL_M,         0},
                {1, &Param.group[group_A].is_added_to_list,   false,          true,                 true},
                {2, &Param.group[group_A].mode,                     mode_MIN,       mode_MAX,            mode_TTL},
                {1, &Param.group[group_A].is_turn_on_Multi,   false,          true,                 true},
                {5, &Param.group[group_A].flash_level_TTL,          0,        MAX_FLASH_LEVEL_TTL,  ZERO_TTL},
                {2, &Param.group[group_A].stylish_lamp_mode,        lamp_off,       lamp_manual,         lamp_off},
                {7, &Param.group[group_A].stylish_lamp_level, MIN_LAMP_LEVEL, MAX_LAMP_LEVEL,       MIN_LAMP_LEVEL},
                {4, &Param.group[group_A].zoom_level,               0,        MAX_ZOOM_LEVEL,            0},
                {1, &Param.group[group_A].is_auto_zoom,       false,          true,                 false},
                // 30
                {7, &Param.group[group_B].flash_level_M,            0,        MAX_FLASH_LEVEL_M,         0},
                {1, &Param.group[group_B].is_added_to_list,   false,          true,                 true},
                {2, &Param.group[group_B].mode,                     mode_MIN,       mode_MAX,            mode_TTL},
                {1, &Param.group[group_B].is_turn_on_Multi,   false,          true,                 true},
                {5, &Param.group[group_B].flash_level_TTL,          0,        MAX_FLASH_LEVEL_TTL,  ZERO_TTL},
                {2, &Param.group[group_B].stylish_lamp_mode,        lamp_off,       lamp_manual,         lamp_off},
                {7, &Param.group[group_B].stylish_lamp_level, MIN_LAMP_LEVEL, MAX_LAMP_LEVEL,       MIN_LAMP_LEVEL},
                {4, &Param.group[group_B].zoom_level,               0,        MAX_ZOOM_LEVEL,            0},
                {1, &Param.group[group_B].is_auto_zoom,       false,          true,                 false},
                // 30
                {7, &Param.group[group_C].flash_level_M,            0,        MAX_FLASH_LEVEL_M,         0},
                {1, &Param.group[group_C].is_added_to_list,   false,          true,                 true},
                {2, &Param.group[group_C].mode,                     mode_MIN,       mode_MAX,            mode_TTL},
                {1, &Param.group[group_C].is_turn_on_Multi,   false,          true,                 true},
                {5, &Param.group[group_C].flash_level_TTL,          0,        MAX_FLASH_LEVEL_TTL,  ZERO_TTL},
                {2, &Param.group[group_C].stylish_lamp_mode,        lamp_off,       lamp_manual,         lamp_off},
                {7, &Param.group[group_C].stylish_lamp_level, MIN_LAMP_LEVEL, MAX_LAMP_LEVEL,       MIN_LAMP_LEVEL},
                {4, &Param.group[group_C].zoom_level,               0,        MAX_ZOOM_LEVEL,            0},
                {1, &Param.group[group_C].is_auto_zoom,       false,          true,                 false},
                // 30
                {7, &Param.group[group_D].flash_level_M,            0,        MAX_FLASH_LEVEL_M,         0},
                {1, &Param.group[group_D].is_added_to_list,   false,          true,                 true},
                {2, &Param.group[group_D].mode,                     mode_MIN,       mode_MAX,            mode_M},
                {1, &Param.group[group_D].is_turn_on_Multi,   false,          true,                 true},
                {5, &Param.group[group_D].flash_level_TTL,          0,        MAX_FLASH_LEVEL_TTL,  ZERO_TTL},
                {2, &Param.group[group_D].stylish_lamp_mode,        lamp_off,       lamp_manual,         lamp_off},
                {7, &Param.group[group_D].stylish_lamp_level, MIN_LAMP_LEVEL, MAX_LAMP_LEVEL,       MIN_LAMP_LEVEL},
                {4, &Param.group[group_D].zoom_level,               0,        MAX_ZOOM_LEVEL,            0},
                {1, &Param.group[group_D].is_auto_zoom,       false,          true,                 false},
                // 30
                {7, &Param.group[group_E].flash_level_M,            0,        MAX_FLASH_LEVEL_M,         0},
                {1, &Param.group[group_E].is_added_to_list,   false,          true,                 true},
                {2, &Param.group[group_E].mode,                     mode_MIN,       mode_MAX,            mode_M},
                {1, &Param.group[group_E].is_turn_on_Multi,   false,          true,                 true},
                {5, &Param.group[group_E].flash_level_TTL,          0,        MAX_FLASH_LEVEL_TTL,  ZERO_TTL},
                {2, &Param.group[group_E].stylish_lamp_mode,        lamp_off,       lamp_manual,         lamp_off},
                {7, &Param.group[group_E].stylish_lamp_level, MIN_LAMP_LEVEL, MAX_LAMP_LEVEL,       MIN_LAMP_LEVEL},
                {4, &Param.group[group_E].zoom_level,               0,        MAX_ZOOM_LEVEL,            0},
                {1, &Param.group[group_E].is_auto_zoom,       false,          true,                 false},
                // 32
                {3, &Param.multi.flash_level,                       0,        MAX_FLASH_LEVEL_MULTI,     0},
                {7, &Param.multi.flash_times,                       1,              90,                  1},
                {8, &Param.multi.flash_freq,                        1,              200,                 1},
                {1, &Param.ctrl_type,                               type_flash,     type_lamp,           type_flash},
                {7, &Param.brightness.level,                        0,        MAX_BRIGHTNESS_LEVEL, MAX_BRIGHTNESS_LEVEL *
                                                                                                    7 / 10},
                {1, &Param.is_Multi_page,                     false,          true,                 false},
                {1, &Setting.values[setting_dist],                  0,              Dist_Item_Count - 1, dist_1_100},
#if ProductModel == QZ_N
                {2, &Setting.values[setting_sync],                  0,              Sync_Item_Count - 1, sync_high_speed},
#elif ProductModel == QZ_F
                {2, &Setting.values[setting_zoom_disp],                  0,              Zoom_Disp_Item_Count - 1, zoom_aps},
#endif
                {1, &Setting.values[setting_beep],                  0,              Beep_Item_Count - 1, 0},
                {1, &Setting.values[setting_tcm],                   0,              1,                   0},
                // 31
                {1, &Setting.values[setting_shoot],                 0,              1,                   0},
#if MODULE_BT
                {1, &Setting.values[setting_bluetooth],             0,              1,                   0},
#endif
                {4, &Setting.values[setting_language],              0,              Lang_Count - 1,      lang_en},
                {1, &Setting.lamp_main_switch,                false,          true,                 false},
                {5, &Setting.roller_values[roller_setting_ch],      0,              31,                  0},
                {7, &Setting.roller_values[roller_setting_id],      0,              99,                  0},
                {3, &Setting.roller_values[roller_setting_standby], 0,              4,                   0},
                {2, &Setting.roller_values[roller_setting_off],     0,              3,                   1},
                {3, &Setting.roller_values[roller_min_power],       0,              6,                   1},
                {1, &Setting.roller_values[roller_power_step],      0,              1,                   0},
                {1, &Param.is_grp_color_on,                         0,              1,                   0},
                {2, &System.res_to,                                 res_to_default, res_to_MAX,          res_to_default},

                // {8, &ADC_DMA.vol_bat_h,                             0,          0xFF,                0},
                // {8, &ADC_DMA.vol_bat_l,                             0,          0xFF,                0},
                // {1, &System.is_mine_shutdown,                       0,          0xFF,                0},
        }
};

/**
 * @brief 获取Sector
 * @param Address: 起始地址
 * @return Sector
 */
uint32_t GetSector(uint32_t Address) {
    uint32_t sector = 0;

    if ((Address >= FLASH_BASE) && (Address < FLASH_BASE + FLASH_BANK_SIZE)) {
        sector = (Address & ~FLASH_BASE) / FLASH_SECTOR_SIZE;
    } else if ((Address >= FLASH_BASE + FLASH_BANK_SIZE) && (Address < FLASH_BASE + FLASH_SIZE)) {
        sector = ((Address & ~FLASH_BASE) - FLASH_BANK_SIZE) / FLASH_SECTOR_SIZE;
    } else {
        // ux_slave_transfer_request_data_pointer =
        sector = 0xFFFFFFFF; /* Address out of range */
    }

    return sector;
}

/**
 * @brief 获取Bank
 * @param Address: 起始地址
 * @return Bank
 */
uint32_t GetBank(uint32_t Address) {
    uint32_t bank = 0;

    if ((Address >= FLASH_BASE) && (Address < (FLASH_BASE + FLASH_BANK_SIZE))) {
        bank = FLASH_BANK_1;
    } else if ((Address >= FLASH_BASE + FLASH_BANK_SIZE) && (Address < FLASH_BASE + FLASH_SIZE)) {
        bank = FLASH_BANK_2;
    } else {
        bank = 0xFFFFFFFF; /* Address out of range */
    }

    return bank;
}

/**
 * @brief 检测参数是否超限
 */
void check_and_limit_params() {
    for (uint32_t i = 0; i < VAR_MAX_NUM; i++) {
        uint8_t min_val = Storage.Data[i].min;
        uint8_t max_val = Storage.Data[i].max;
        if (i == 0 || i == 9 || i == 18 || i == 27 || i == 36) {
            max_val = set_flash_level_max();
        }
        uint8_t default_val = Storage.Data[i].default_var;

        uint8_t *value_ptr = (uint8_t *) Storage.Data[i].ptr;
        uint8_t value = *value_ptr;

        // 检查是否超出边界
        if (value < min_val || value > max_val) {
            *value_ptr = default_val;
        }
    }
}

/**
 * @brief 计算需要多少个32位数据来存储变量
 * @param bit_widths 位数
 * @return 32位数量
 */
uint32_t calc_required_uint32_count() {
    // 已使用的位数
    uint32_t total_bits = 0;
    // 需要的uin32数量
    uint32_t uint32_count = 1;

    // 计算单一变量
    for (uint32_t i = 0; i < VAR_MAX_NUM; i++) {
        if (total_bits + Storage.Data[i].bits > 32) {
            uint32_count++;
            total_bits = Storage.Data[i].bits;
        } else {
            total_bits += Storage.Data[i].bits;
        }
    }

    return uint32_count;
}

/**
 * @brief 将数据写入到存储页
 * @param write_addr 写数据起始地址
 * @param data 写数据数组
 * @param arr_num 写数据数组大小
 */
void storage_write_page(uint32_t write_addr, uint32_t *data, uint8_t arr_num) {
    // n * 16byte
    // uint8_t i;

    // 关闭ICache、解锁Flash
    if (HAL_ICACHE_Disable() != HAL_OK) {
        Error_Handler();
    }

    HAL_FLASH_Unlock();

    uint8_t max_count_num;
    // arr_num % (128bit/32(uint32_t))
    if (arr_num % 4) {
        max_count_num = (arr_num >> 2) + 1;
    } else {
        max_count_num = arr_num >> 2;
    }

    for (uint8_t i = 0; i < max_count_num; i++) {
        if (HAL_FLASH_Program(FLASH_TYPEPROGRAM_QUADWORD, write_addr, (uint32_t) data) == HAL_OK) {
            // 4*32 = FLASH_TYPEPROGRAM_QUADWORD(128bit)
            data += 4;
            // 16*8 = FLASH_TYPEPROGRAM_QUADWORD(128bit)
            // 地址一个字节，一个字节8位
            write_addr = write_addr + 16; /* increment to next quad word*/
        } else {
            Error_Handler();
        }
    }

    // Flash上锁、打开ICache
    HAL_FLASH_Lock();
    if (HAL_ICACHE_Enable() != HAL_OK) {
        Error_Handler();
    }
}

/**
 * @brief 读取页
 * @param read_addr 读取地址
 * @param get_data 读取数据存入地址
 * @param arr_num 长度
 */
void storage_read_page(uint32_t read_addr, uint32_t *get_data, uint8_t arr_num) {
    // n * 2byte
    for (uint8_t i = 0; i < arr_num; i++) {
        *get_data++ = *(volatile uint32_t *) read_addr;
        // uint32_t 4个字节
        read_addr += 4;
    }
}

/**
 * @brief 擦除指定页数据
 * @param start_addr 擦除起始地址
 * @param end_addr 擦除结束地址
 */
void storage_erase_page(uint32_t start_addr, uint32_t end_addr) {
    // Page = 1st sector(8K)
    uint32_t FirstSector = 0, BankNumber = 0;
    uint32_t NbOfSectors = 0, SectorError = 0;
    FLASH_EraseInitTypeDef EraseInitStruct = {0};

    if (HAL_ICACHE_Disable() != HAL_OK) {
        Error_Handler();
    }

    HAL_FLASH_Unlock();

    if ((start_addr < FLASH_BASE) || (start_addr % 4) ||
        (start_addr > (FLASH_BASE + FLASH_SIZE))) {
        return; // address error
    }

    /* Get the 1st sector to erase */
    FirstSector = GetSector(start_addr);
    /* Get the number of sector to erase from 1st sector*/
    NbOfSectors = GetSector(end_addr) - FirstSector + 1;
    /* Get the bank */
    BankNumber = GetBank(end_addr);

    /* Fill EraseInit structure*/
    EraseInitStruct.TypeErase = FLASH_TYPEERASE_SECTORS;
    EraseInitStruct.Banks = BankNumber;
    EraseInitStruct.Sector = FirstSector;
    EraseInitStruct.NbSectors = NbOfSectors;

    if (HAL_FLASHEx_Erase(&EraseInitStruct, &SectorError) != HAL_OK) {
        Error_Handler();
    }

    HAL_FLASH_Lock();
    if (HAL_ICACHE_Enable() != HAL_OK) {
        Error_Handler();
    }
}

/**
 * @brief 判断指定区域是否全为0xFF
 * @param address: 起始地址(需要4字节对齐)
 * @param num: 要检查的字节数
 * @return 0:全为0xFF index:下标
 */
uint16_t find_flash_empty_index(uint32_t address, uint16_t num) {
    uint32_t *ptr = (uint32_t *) address;
    // uint32_t words = num / 4;
    // uint32_t remainder = num % 4;
    uint16_t index = 0;
    // 按32位字进行检查
    while (num--) {
        if (*ptr == 0xFFFFFFFF) {
            break;
        }
        ptr++;
        index++;
    }

    return index;
}

/**
 * @brief 打包数据到uint32数组
 * @param output 输出数组
 * @param output_size 输出数组大小
 */
void pack_params_to_uint32(uint32_t *output, uint32_t output_size) {
    // 当前处理的32位值
    uint32_t current_uint32 = 0;
    // 当前位位置（从高位开始）
    uint32_t current_bit_pos = 32;
    // 输出数组索引
    uint32_t output_index = 0;

    // 清零输出数组
    memset(output, 0, output_size * sizeof(uint32_t));

    for (uint32_t i = 0; i < VAR_MAX_NUM; i++) {
        // 当前变量需要的位数
        uint8_t bits = Storage.Data[i].bits;

        // 检查是否需要切换到下一个uint32
        if (current_bit_pos < bits) {
            // 保存当前uint32并切换到下一个
            output[output_index++] = current_uint32;
            current_uint32 = 0;
            current_bit_pos = 32;

            if (output_index >= output_size) {
                // 防止缓冲区溢出
                return;
            }
        }

        // 计算当前变量在uint32中的起始位置
        current_bit_pos -= bits;

        // 从void指针读取值并确保不超过允许的位数
        uint32_t value;
        uint32_t mask = (1U << bits) - 1;

        // 根据位数决定如何读取值
        if (bits <= 8) {
            value = *((uint8_t *) Storage.Data[i].ptr) & mask;
        } else {
            value = *((uint32_t *) Storage.Data[i].ptr) & mask;
        }

        // 将值放入正确的位置
        current_uint32 |= (value << current_bit_pos);
    }

    // 保存最后一个uint32（如果有数据的话）
    if (current_bit_pos < 32) {
        output[output_index] = current_uint32;
    }
}

/**
 * @brief 解包数据到uint32数组
 * @param input 解包到此数组
 * @param input_size 数组大小
 */
void unpack_uint32_to_params(const uint32_t *input, uint32_t input_size) {
    // 当前处理的32位值
    uint32_t current_uint32 = input[0];
    // 当前位位置（从高位开始）
    uint32_t current_bit_pos = 32;
    // 输入数组索引
    uint32_t input_index = 0;

    for (uint32_t i = 0; i < VAR_MAX_NUM; i++) {
        // 当前变量需要的位数
        uint8_t bits = Storage.Data[i].bits;

        // 检查是否需要切换到下一个uint32
        if (current_bit_pos < bits) {
            if (++input_index >= input_size) {
                // 防止缓冲区溢出
                return;
            }
            current_uint32 = input[input_index];
            current_bit_pos = 32;
        }

        // 计算当前变量在uint32中的起始位置
        current_bit_pos -= bits;

        // 提取值
        uint32_t mask = (1U << bits) - 1;
        uint32_t value = (current_uint32 >> current_bit_pos) & mask;

        // 将值写入目标变量
        if (bits <= 8) {
            *((uint8_t *) Storage.Data[i].ptr) = (uint8_t) value;
        } else {
            *((uint32_t *) Storage.Data[i].ptr) = value;
        }
    }
}

// 填充默认数据
void fill_default_data() {
    for (uint32_t i = 0; i < VAR_MAX_NUM; i++) {
        uint8_t default_val = Storage.Data[i].default_var;
        uint8_t *value_ptr = (uint8_t *) Storage.Data[i].ptr;
        *value_ptr = default_val;
    }
}

// 第0位为魔数，第1位为CRC32
uint32_t magic_num_crc32[2];
uint32_t data_arr[20];

void storage_read_data() {
    // find_flash_empty_index - 1 即将index指向空闲位的前一位，即最后有数据的一位
    uint16_t flash_idle_index = find_flash_empty_index(STORAGE_START_ADDR, DATA_PTN_SIZE);
    if (flash_idle_index) {
        flash_idle_index -= 1;
    }
    // 没数据
    if (flash_idle_index == 0) {
        // 填充默认数据
        fill_default_data();
        return;
    }
    uint8_t num_bytes = 32 >> 3;
    // 满了
    if (flash_idle_index > DATA_PTN_SIZE / num_bytes) {
        flash_idle_index = DATA_PTN_SIZE / num_bytes;
    }
    uint32_t temp_arr[1] = {0};

    do {
        storage_read_page((STORAGE_START_ADDR + flash_idle_index * num_bytes), temp_arr, 1);
        if (!temp_arr[0]) {
            flash_idle_index--;
        }
    } while (!temp_arr[0]);
    // 第0位为魔数，第1位为CRC32
    // uint32_t magic_num_crc32[2];
    // 从有值的前一位开始读
    storage_read_page((STORAGE_START_ADDR + (flash_idle_index - 1) * num_bytes), magic_num_crc32, 2);
    // 校验魔数
    if ((magic_num_crc32[0] & 0x00FFFFFF) != MAGIC_NUM) {
        // 填充默认数据
        fill_default_data();
        return;
    }
    // 变量数
    uint8_t var_num = magic_num_crc32[0] >> 24;
    // 读数据缓冲区
    // uint32_t data_arr[var_num];
    storage_read_page((STORAGE_START_ADDR + (flash_idle_index - 1 - var_num) * num_bytes), data_arr, var_num + 1);
    // 校验CRC
    uint32_t crc32_check = crc32_fast(data_arr, (var_num + 1) * sizeof(uint32_t));
    if (crc32_check != magic_num_crc32[1]) {
        // 填充默认数据
        fill_default_data();
        return;
    }
    // 保存数据区内的crc32
    Storage.last_crc32 = magic_num_crc32[1];
    // 解包数据
    unpack_uint32_to_params(data_arr, var_num);
}

uint32_t output_arr[20];

// 保存数据
void storage_save_data() {
    // for (int i = 0; i < GroupItemCount; ++i) {
    //     // Param.group[i].flash_level_M = Param.group[i].flash_level_M + (i + 1) * 10;
    //     Param.group[i].flash_level_M = (i + 1) * 10;
    // }
    // storage_erase_page(STORAGE_START_ADDR, STORAGE_END_ADDR);

    // 检查变量是否超限,超限设为默认值
    check_and_limit_params();
    // // 注册变量
    // for (int i = 0; i < var_count; ++i) {
    //     Storage_RegisterVariable(&StorageManager, var_bits_arr[i], var_p_arr[i]);
    // }
    // 计算需要多少个uin32_t的变量去存储
    uint32_t uint32_count = calc_required_uint32_count();
    // 输出缓冲区大小+2(魔数+CRC)
    uint8_t final_size = uint32_count + 2;
    // 分配输出缓冲区
    // uint32_t output_arr[final_size];
    // 打包数据
    pack_params_to_uint32(output_arr, uint32_count);

    // 变量数作为高8位，后面的为魔数
    uint32_t magic_num = (uint32_count << 24) | MAGIC_NUM;
    output_arr[final_size - 2] = magic_num;
    // CRC32值
    uint32_t crc32 = crc32_fast(output_arr, (final_size - 1) * sizeof(uint32_t));
    output_arr[final_size - 1] = crc32;
    // 数据相同,不保存
    if (Storage.last_crc32 == crc32) {
        return;
    }
    // flash 可写索引
    uint16_t flash_idle_index = find_flash_empty_index(STORAGE_START_ADDR, DATA_PTN_SIZE);

    // 查找写入位置,如果超出数据分区大小，则擦除后再写入
    // 一个32位所占用的字节数：32 / 8 = 4
    uint8_t num_bytes = 32 >> 3;
    uint16_t need_index = flash_idle_index + final_size;
    // 一个index为32位，对4取余数则知道是否满128位
    if (need_index % 4) {
        need_index = need_index + (4 - (need_index % 4));
    }
    // 判断是否超出数据分区的空间，超出则擦除后再写入
    if (need_index > DATA_PTN_SIZE / num_bytes) {
        storage_erase_page(STORAGE_START_ADDR, STORAGE_END_ADDR);
        flash_idle_index = 0;
    }
    storage_write_page(STORAGE_START_ADDR + flash_idle_index * num_bytes, output_arr,
                       final_size);
    // 数据不同，保存，并更新 Storage.last_crc32
    Storage.last_crc32 = crc32;
}