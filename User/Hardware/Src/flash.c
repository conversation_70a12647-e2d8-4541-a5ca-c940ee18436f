//
// Created by <PERSON><PERSON> on 2024/12/3.
//

#include "flash.h"
#include "scan_key.h"
#include "nikon_protocol.h"
#include "system.h"
#include "rf_tx.h"
#include "rf_module.h"

MultiStructType Multi;

// TODO 10t 3hz 开始漏一次
void flash_multi_mode(uint8_t event, uint8_t flash_level, uint8_t times, uint8_t freq) {
    SysTick->VAL = 0;
    Multi.flash_level = flash_level;
    Multi.times = times + 1;
    Multi.freq = freq;
    Multi.event = event;

    // flash_M_curtain_sync(Multi.flash_level);

    if (Multi.times) {
        Multi.times--;
        Multi.send_0x59_countdown = 0;
        Multi.wireless_data_time = 0;
        Multi.RatesCnt = 1000 / Multi.freq;
        Multi.RatesMs = Multi.RatesCnt;
    }
}

void flash_multi_mode_int() {
    // 避免flash_multi_mode_event中将times置0后多闪一次
    // 故单独提出来
    if (Multi.times) {
        pa_tx_on();
        flash_multi_mode_event();
        pa_tx_off();
    }

    if (Multi.times) {
        // 有值说明还没到下次闪光
        if (Multi.RatesMs) {
            Multi.RatesMs--;
        } else {
            Multi.times--;
            Multi.RatesMs = Multi.RatesCnt;
            // if ((Multi.event & wireless_event) == 0) {
            // 频闪模式下需关闭最小闪光间隔
            // Flash.min_flash_interval = 0;
            // ProtectRam.FlashEn = 1;
            // flash_M_curtain_sync(Multi.flash_level);
            // }
        }
    } else if (Multi.RatesCnt) {
        Multi.flash_level = 0;
        Multi.freq = 0;
        Multi.event = 0;
        Multi.RatesMs = 0;
        Multi.RatesCnt = 0;
        Multi.wireless_data_time = 0;
        Multi.send_0x59_countdown = 0;
        // Bluetooth.flash = 0;
    }
}

void flash_multi_mode_event() {
    // 试闪键触发
    if (Multi.event & pilot_key_event) {
        // if (!Bluetooth.flash) {
        if (Key_Flash_Read) {
            Multi.times = 0;
            // flash_set_min_interval();
        }
        // }
    }

    // 无线触发
    if (Multi.event & rf_rx_event) {
        Multi.wireless_data_time++;
        if (Multi.wireless_data_time >= 50) {
            Multi.times = 0;
            // flash_set_min_interval();
        }
    }

    // 快门触发
    if (Multi.event & shutter_event) {
        // Test_1_WriteH;
        // bool check = (!Nikon_SDA_Read) || (!Nikon_SCS_Read);
        // Test_1_WriteL;
        // if ((check) && FLASH_TRI_Read) {
        if (System.nikon_sda_is_falling && FLASH_TRI_Read) {
            Multi.times = 0;
            // Test_1_WriteH;
            // Test_1_WriteL;

            // TMRSFlashDelay = FlashDelayTime;
        }

    }

    // 定时发送
    if (Multi.event & rf_tx_event) {
        Multi.send_0x59_countdown++;
        if (Multi.send_0x59_countdown >= 15) {
            rf_send_shutter_hold_0x59();
            Multi.send_0x59_countdown = 0;
        }
    }
}