//
// Created by <PERSON><PERSON> on 2024/10/27.
//

#include "scan_key.h"
#include "system.h"
#include "rf_tx.h"
#include "amoled_qspi.h"
#include "view_model.h"
#include "adc_dma.h"
#include "data_storage.h"
#include "encoder.h"
#include "touch.h"

// 将按下的按键赋值进按键扫描结构体
// void get_Key() {
//     ScanKey.value = 0;
//     if (!Key_Flash_Read) {
//         ScanKey.value |= KEY_FLASH;
//         System.defocused_countdown = ENCODER_NO_ACTION_TIME;
//     }
//     if (!Key_Mode_Read) {
//         ScanKey.value |= KEY_MODE;
//         amoled_keep_alive();
//         System.defocused_countdown = ENCODER_NO_ACTION_TIME;
//     }
//     if (!Key_Enter_Read) {
//         ScanKey.value |= KEY_ENTER;
//         amoled_keep_alive();
//         System.defocused_countdown = ENCODER_NO_ACTION_TIME;
//     }
// }

bool key_gpio_read(KeyEnum key_id) {
    switch (key_id) {
        case KEY_FLASH:
            return Key_Flash_Read;
        case KEY_ENTER:
            return Key_Enter_Read;
        case KEY_MODE:
            return Key_Mode_Read;
        case KEY_MAIN_SWITCH:
            return MAIN_SWITCH_Read;
        default:
            return false;
    }
}

// Flash键事件
void key_flash_event(KeyEventEnum key_event) {
    if (key_event != KEY_RELEASED && Param.is_wireless_sync) {
        return;
    }
    ScanKey.buttons[KEY_FLASH].key_event = key_event;
    switch (key_event) {
        case KEY_RELEASED:
            if (!Param.is_charging_page) {
                LED_RED_WriteL;
            }
            send_lock_maks_released_event();
            break;
        case KEY_PRESSED:
            if (!Param.is_charging_page && !Setting.lock) {
                rf_send_master_pilot();
            }
#if STATIC_BAT
            ADC_DMA.vol_bat += 100;
#endif
            break;
        case KEY_PRESSING:
            if (!Param.is_charging_page) {
                if (Setting.lock) {
                    return;
                }
                LED_RED_WriteH;
                // 清除快门亮灯状态
                System.shut_led_countdown = 0;
                System.shut_led_status = false;
            } else {
                amoled_keep_alive();
            }
            break;
        case KEY_LONG_PRESSED:
            if (Setting.lock) {
                send_lock_maks_long_pressed_event();
            }
            break;
        case KEY_CLICKED:
            break;
        default:
            break;
    }
}

// Enter键事件
void key_enter_event(KeyEventEnum key_event) {
    // 触摸屏按下时忽略enter键
    if (Touch.is_pressed || Setting.is_loading_mask_valid) {
        return;
    }
    // 避免充电页死机
    if (Param.is_charging_page) {
        return;
    }
    if (key_event != KEY_PRESSING && ScanKey.buttons[KEY_ENTER].key_event != KEY_CLICKED) {
        ScanKey.buttons[KEY_ENTER].key_event = key_event;
    }
    switch (key_event) {
        case KEY_RELEASED:
            printf("KEY_RELEASED\n");
            break;
        case KEY_PRESSED:
            break;
        case KEY_PRESSING:
            amoled_keep_alive();
            break;
        case KEY_LONG_PRESSED:
            printf("KEY_LONG_PRESSED\n");
            break;
        case KEY_CLICKED:
            printf("KEY_CLICKED\n");
            break;
        default:
            break;
    }
}

// Mode键事件
void key_mode_event(KeyEventEnum key_event) {
    if (key_event != KEY_PRESSING && key_event != KEY_RELEASED) {
        ScanKey.buttons[KEY_MODE].key_event = key_event;
    }
    switch (key_event) {
        case KEY_RELEASED:
            printf("KEY_RELEASED\n");
            break;
        case KEY_PRESSED:
#if STATIC_BAT
            ADC_DMA.vol_bat -= 100;
#endif
            printf("KEY_PRESSED\n");
            break;
        case KEY_PRESSING:
            amoled_keep_alive();
            break;
        case KEY_LONG_PRESSED:
            break;
        case KEY_CLICKED:
            printf("KEY_CLICKED\n");
            break;
        default:
            break;
    }
}

void key_main_switch_event(KeyEventEnum key_event) {
    if (key_event != KEY_PRESSING && key_event != KEY_RELEASED) {
        ScanKey.buttons[KEY_MAIN_SWITCH].key_event = key_event;
    }
    switch (key_event) {
        case KEY_RELEASED:
            break;
        case KEY_PRESSED:
            break;
        case KEY_PRESSING:
            amoled_keep_alive();
            break;
        case KEY_LONG_PRESSED:
            if (!System.from_MAIN_SWITCH_INT) {
                return;
            }
            if (Param.is_charging_page && Param.battery_status < battery_low) {
                System.res_to = res_to_main_page;
                // 保存数据
                storage_save_data();
                device_sw_reset();
            } else {
                // 不在欢迎页才可关机
                if (PageManager.main_page != &Pages.page[Page_Welcome]) {
                    if (BatteryCharge.usb_is_plugged) {
                        // 插电非充电页长按开关键则置起强制跳充电页标志，并存储数据
                        if (!Param.is_charging_page) {
                            System.res_to = res_to_charging_page;
                            // 保存数据
                            storage_save_data();
                        }
                        device_sw_reset();
                        // amoled_enter_sleep();
                        // delay_us(1000);
                        // System.do_not_refresh_WWDG = true;
                    } else {
                        // System.is_mine_shutdown = false;
                        device_deinit();
                    }
                }
            }
            break;
        case KEY_CLICKED:
            break;
        default:
            break;
    }
}

// 每次扫描前执行
bool before_scan_key() {
    return true;
}

// 每次扫描后执行
void after_scan_key() {

}

// 扫描按键初始化配置
ScanKeyStructType ScanKey = {
        {
                {KEY_FLASH, key_flash_event},
                {KEY_ENTER, key_enter_event},
                {KEY_MODE, key_mode_event},
                {KEY_MAIN_SWITCH, key_main_switch_event},
        },
        before_scan_key,
        after_scan_key,
};

uint16_t long_pressed_time_check = LONG_PRESSED_TIME_LIMIT;

// 按键扫描
void key_event_get() {
    if (check_timer_is_run(ChargingPage.charging_timer) || check_timer_is_run(ChargingPage.blink_timer)) {
        long_pressed_time_check = LONG_PRESSED_TIME_LIMIT / 10;
    } else if (Setting.lock) {
        long_pressed_time_check = 1900;
    } else {
        long_pressed_time_check = LONG_PRESSED_TIME_LIMIT;
    }
    for (int i = 0; i < KeyItemCount; ++i) {
        KeyStructType *Key = &ScanKey.buttons[i];
        // 按下
        if (!key_gpio_read(Key->key_id)) {
            System.defocused_countdown = ENCODER_NO_ACTION_TIME;
            Key->cur_press_time++;
            Key->released_time = 0;
            // 消抖，拉低超过30ms视为按下了
            if (Key->cur_press_time > PRESSED_TIME_LIMIT) {
                if (!Key->is_pressing) {
                    Key->key_event_prg(KEY_PRESSED);
                }
                Key->is_pressing = true;
                Key->key_event_prg(KEY_PRESSING);
                // 如果第一次触发则触发一次
                if (Key->cur_press_time > long_pressed_time_check && !Key->long_pr_sent) {
                    // 触发一次key_long_pressed
                    Key->key_event_prg(KEY_LONG_PRESSED);
                    Key->long_pr_sent = true;
                }
            }
        } else {
            // Key->last_press_time 没有值才赋值
            // 避免因为 Key->released_time 防抖导致 Key->last_press_time 清空
            if (Key->cur_press_time && !Key->last_press_time) {
                Key->last_press_time = Key->cur_press_time;
            }
            // if (Key_F->last_press_time) {
            //     printf("123\n");
            // }
            if (Key->released_time > PRESSED_TIME_LIMIT) {
                // if (Key.long_pr_sent) {
                //     Key.key_event_prg(key_short_clicked);
                // }
                // 触发一次key_clicked
                if (Key->is_pressing) {
                    // 松手
                    // lvgl内obj拖动后松手可触发released
                    // 不拖动松手才可触发key_clicked
                    // 此处均按released处理
                    // Key.key_event_prg(KEY_CLICKED);
                    // 若松手前按压时间不超过CLICKED_TIME_LIMIT才执行key_clicked
                    if (Key->last_press_time < CLICKED_TIME_LIMIT) {
                        Key->key_event_prg(KEY_CLICKED);
                    }
                    Key->key_event_prg(KEY_RELEASED);
                }
                Key->is_pressing = false;
                Key->last_press_time = 0;
                Key->long_pr_sent = false;
            }
            Key->released_time++;
            Key->cur_press_time = 0;
        }
    }
}

// 按键扫描
void scan_key_pgr() {
    if (!ScanKey.before_scan_pgr) {
        return;
    }
    key_event_get();
    ScanKey.after_scan_pgr();
}
