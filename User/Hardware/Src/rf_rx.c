//
// Created by <PERSON><PERSON><PERSON> on 2024/11/4.
//

#include "rf_rx.h"
#include "rf_module.h"

// 获取数据包长度数据位
uint8_t wireless_get_packet_len_bits() {
    uint8_t tx_buf[2], rx_buf[2];

    tx_buf[0] = (Access_Read_Single | CC2500_3B_RXBYTES | 0x04);
    tx_buf[1] = 0xFF;
    cc2500_spi_transmit_receive(tx_buf, rx_buf, 2);

    return rx_buf[0];
}

// 获取数据包
void wireless_get_data_pack(uint8_t addr, uint8_t *rx_buf, uint8_t len) {
    uint16_t timeout = RF_Wait_TimeOut;
    uint8_t tx_buf[20];

    while (RF_MISO_Read & timeout) {
        timeout--;
    }

    tx_buf[0] = (Access_Read_Burst | addr);
    for (uint8_t i = 1; i < len; i++) {
        tx_buf[i] = 0xFF;
    }

    cc2500_spi_transmit_receive(tx_buf, rx_buf, len);
}

lv_obj_t *test_obj;

// 数据包类型处理
void wireless_handle_packet_type() {
    switch (RF.rx_data_buf[1]) {
        case 0x6B:
            if (lv_obj_is_valid(test_obj)) {
                lv_obj_del(test_obj);
                test_obj = NULL;
            } else {
                test_obj = lv_obj_create(lv_scr_act());
            }
            break;
        default:
            break;
    }
}

// 获取数据包长度数据位，根据长度数据位获取数据包有效数据
void wireless_interrupt_pgr() {
    if (RF.mode == RF_rx_mode) {
        RF.data_pack_len = wireless_get_packet_len_bits();
        if ((RF.data_pack_len >= 3) && (RF.data_pack_len <= 20)) {
            wireless_get_data_pack(CC2500_RXFIFO, RF.rx_data_buf, RF.data_pack_len);
            wireless_handle_packet_type();
        }

        RF.data_pack_len = 0;
        RF.idle_time = 0;
        cc2500_flush_rx_buffer();
    }
}