//
// Created by <PERSON><PERSON> on 2024/8/19.
//

#include <stdbool.h>
#include <stdio.h>
#include "touch.h"
#include "page_manager.h"
#include "system.h"
#include "amoled_qspi.h"
#include "encoder.h"

TouchStructType Touch;

void cst820_write_byte(uint8_t Cmd, uint8_t value) {
    HAL_I2C_Mem_Write(&hi2c1, CST820_IIC_ADDR, Cmd, I2C_MEMADD_SIZE_8BIT, &value, 1, 0X20);
}

uint8_t cst820_read_byte(uint8_t Cmd) {
    uint8_t value;
    HAL_I2C_Mem_Read(&hi2c1, CST820_IIC_ADDR + 1, Cmd, I2C_MEMADD_SIZE_8BIT, &value, 1, 0X20);
    return value;
}

void cst820_read_nbytes(uint8_t Cmd, uint8_t *buf, uint8_t num) {
    HAL_I2C_Mem_Read(&hi2c1, CST820_IIC_ADDR + 1, Cmd, I2C_MEMADD_SIZE_8BIT, buf, num, 0X20);
}

void cst820_reset() {
    HAL_GPIO_WritePin(TP_RST_GPIO_Port, TP_RST_Pin, GPIO_PIN_RESET);
    HAL_Delay(10);
    HAL_GPIO_WritePin(TP_RST_GPIO_Port, TP_RST_Pin, GPIO_PIN_SET);
    HAL_Delay(50);
}

bool cst820_check_id() {
    if (cst820_read_byte(0xA7) == 0xB7) {
        return true;
    } else {
        return false;
    }
}

//禁止触摸屏自动睡眠
void cst820_disable_sleep() {
    cst820_write_byte(CST820_CMD_SET_AUTO_SLEEP, 0X01);
}

uint8_t cst820_read_finger_num() {
    return cst820_read_byte(CST820_CMD_GET_FINGERNUM);
}

//获取按下点的坐标
void cst820_point_scan() {
    // if (cst820_read_finger_num()) {
    //     Touch.is_pressed = true;
    // } else {
    //     Touch.is_pressed = false;
    // }
    uint8_t data[4];
    cst820_read_nbytes(0x03, data, 4);
    // uint8_t data[5];
    // cst820_read_nbytes(0x02, data, 5);

    Touch.x = (uint16_t) ((data[0] & 0x0F) << 8) | data[1];
    Touch.y = (uint16_t) ((data[2] & 0x0F) << 8) | data[3];
    // printf("Touch.x: %d, Touch.y: %d\n", Touch.x, Touch.y);
    // 当上次的点击左标都不为0时，即不是首次点击
    // 此时判断两次点击的差值是否大于100，大于则过滤掉，以避免异常数据
    if (Touch.last_x != 0 && Touch.last_y != 0) {
        if (Touch.x > Screen_Width || Touch.y > Screen_Height) {
            // if (LV_ABS(Touch.x - Touch.last_x) > 100 || LV_ABS(Touch.y - Touch.last_y) > 100) {
            Touch.x = Touch.last_x;
            Touch.y = Touch.last_y;
        }
    }
    Touch.last_x = Touch.x;
    Touch.last_y = Touch.y;

    // 触控翻转
    // Touch.x = Screen_Width - Touch.x;
    // Touch.y = Screen_Height - Touch.y;
    // Touch.x = data[2] | ((data[1] & 0xF) << 8);
    // Touch.y = data[4] | ((data[3] & 0xF) << 8);
    // Touch.x = ((data[0] & 0x0f) << 8) + data[1];
    // Touch.y = ((data[2] & 0x0f) << 8) + data[3];
}

uint8_t cst820_init() {
    cst820_reset();
    if (cst820_check_id()) {
        cst820_disable_sleep();
        HAL_Delay(3);
        // cst820_write_byte(CST820_CMD_SLEEP, 0X03);
        return 0;
    } else {
        return 1;
    }
}

void cst820_is_pressed() {
    if (cst820_read_finger_num()) {
        Touch.is_pressed = true;
    } else {
        Touch.is_pressed = false;
    }
}

void cst820_sleep() {
    cst820_write_byte(CST820_CMD_SLEEP, 0X03);
}

void cst820_wakeup() {
    cst820_reset();
}
