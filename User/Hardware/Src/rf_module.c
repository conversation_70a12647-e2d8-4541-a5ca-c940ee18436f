//
// Created by <PERSON><PERSON> on 2024/9/9.
//

#include "rf_module.h"
#include "system.h"
#include "user.h"
#include "view_model.h"
#include "stm32h5xx_ll_gpio.h"

RFModuleStruct RF;

void pa_tx_on() {
    // 置高
    LL_GPIO_SetOutputPin(RF_PA_TXEN_GPIO_Port, RF_PA_TXEN_Pin);
}

void pa_tx_off() {
    // 置低
    LL_GPIO_ResetOutputPin(RF_PA_TXEN_GPIO_Port, RF_PA_TXEN_Pin);
}

// SPI通讯
void cc2500_spi_transmit_receive(uint8_t *tx_data, uint8_t *rx_data, uint8_t len) {
    RF_CSN_WriteL;
    // for (int i = 0; i < len; ++i) {
    //     // delay_us(1);
    //     HAL_SPI_TransmitReceive(&hspi1, &tx_data[i], rx_data, 1, 100);
    // }
    HAL_SPI_TransmitReceive(&hspi1, tx_data, rx_data, len, 100);
    RF_CSN_WriteH;
}

// 写寄存器
void cc2500_write_reg(uint8_t reg, uint8_t value) {
    uint16_t timeout;
    uint8_t tx_buf[2], rx_buf[2];

    timeout = RF_Wait_TimeOut;
    while (RF_MISO_Read & timeout) {
        timeout--;
    }

    tx_buf[0] = (Access_Write_Single | reg);
    tx_buf[1] = value;

    cc2500_spi_transmit_receive(tx_buf, rx_buf, 2);
}

void cc2500_write_reg_arr(uint8_t reg, uint8_t *buf, uint8_t buf_size) {
    uint16_t timeout;
    uint8_t rx_buf[20];

    timeout = RF_Wait_TimeOut;
    while (RF_MISO_Read & timeout) {
        timeout--;
    }

    *buf = (Access_Write_Burst | reg);

    cc2500_spi_transmit_receive(buf, rx_buf, buf_size);
}

// 发送指令
void cc2500_send_cmd(uint8_t cmd) {
    uint16_t timeout;
    uint8_t rx_data;

    timeout = RF_Wait_TimeOut;
    while (RF_MISO_Read & timeout) {
        timeout--;
    }

    cc2500_spi_transmit_receive(&cmd, &rx_data, 1);
}

void cc2500_transmit_data_pgr(uint8_t size) {
    uint16_t timeout;
    // pa_tx_on();
    (RF_PA_TXEN_GPIO_Port->BSRR) = (RF_PA_TXEN_Pin);
    cc2500_write_reg_arr(CC2500_TXFIFO, RF.tx_data_buf, size);
    cc2500_send_cmd(Cmd_STX);
    timeout = RF_Wait_TimeOut;
    while (!RF_IRQ_Read && timeout) {
        timeout--;
    }
    timeout = RF_Wait_TimeOut;
    while (RF_IRQ_Read && timeout) {
        timeout--;
    }
    cc2500_send_cmd(Cmd_SFTX);
    // cc2500_send_cmd(Cmd_SIDLE);
    // cc2500_send_cmd(Cmd_SCAL);
    // pa_tx_off();
    (RF_PA_TXEN_GPIO_Port->BRR) = (RF_PA_TXEN_Pin);
}

// 设置频道频率
void cc2500_set_channel(uint8_t ch) {
    uint32_t final_freq;

    // CH01-CH32
    const uint32_t rf_ch_freq[32] = {
            0x5CCEC4, 0x5CDD89, 0x5CEC4E, 0x5D0000, 0x5D0EC4, 0x5D1D89, 0x5D313B, 0x5D4000, 0x5D5627, 0x5D6276,
            0x5D713B, 0x5D8762, 0x5D93B1, 0x5DA276, 0x5DB3B1, 0x5DC4EC, 0x5DD3B1, 0x5DE9D8, 0x5DF627, 0x5E04EC,
            0x5E1B13, 0x5E2762, 0x5E3627, 0x5E4C4E, 0x5E589D, 0x5E6762, 0x5E7D89, 0x5E89D8, 0x5E989D, 0x5EAEC4,
            0x5EBB13, 0x5EC9D8
            // 0x5EBB13, 0x5F0000
    };

    // 不减600为神牛频率
    final_freq = rf_ch_freq[ch] - 600;
    cc2500_write_reg(CC2500_0D_FREQ2, final_freq >> 16);
    cc2500_write_reg(CC2500_0E_FREQ1, final_freq >> 8);
    cc2500_write_reg(CC2500_0F_FREQ0, final_freq & 0x0000FF);
    delay_us(30);
    cc2500_send_cmd(Cmd_SCAL);
}

// 切换IO口配置，Slave才进中断，正常模式下不进中断
void cc2500_switch_GPIO_config(RFGPIOEnum config) {
    GPIO_InitTypeDef GPIO_InitStruct = {0};
    GPIO_InitStruct.Pin = RF_IRQ_Pin;
    if (config == gpio_input) {
        GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
    } else if (config == gpio_exti) {
        GPIO_InitStruct.Mode = GPIO_MODE_IT_RISING_FALLING;
    } else if (config == gpio_output) {
        GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
    }
    GPIO_InitStruct.Pull = GPIO_PULLUP;
    HAL_GPIO_Init(RF_IRQ_GPIO_Port, &GPIO_InitStruct);
}

// 冲洗RX FIFO缓冲
void cc2500_flush_rx_buffer() {
    cc2500_send_cmd(Cmd_SFRX);
    // Z2-S清缓存多了两条指令
    cc2500_send_cmd(Cmd_SIDLE);
    cc2500_send_cmd(Cmd_SCAL);
    cc2500_send_cmd(Cmd_SRX);
}

// 设置CC2500模式
void cc2500_set_mode(uint8_t mode) {
    cc2500_send_cmd(Cmd_SIDLE);
    if (mode == RF_sleep_mode) {
        cc2500_send_cmd(Cmd_SPWD);
        PA_TX_EN_WriteL;
        PA_RX_EN_WriteL;
        cc2500_switch_GPIO_config(gpio_output);
        return;
    }
    delay_us(60);
    cc2500_send_cmd(Cmd_SCAL);
    cc2500_send_cmd(Cmd_SIDLE);
    delay_us(10);
    cc2500_send_cmd(Cmd_SCAL);
    delay_us(10);
    switch (mode) {
        case RF_rx_mode:
            // 进入接受模式
            cc2500_flush_rx_buffer();
            PA_TX_EN_WriteL;
            PA_RX_EN_WriteH;
            cc2500_switch_GPIO_config(gpio_exti);
            break;
        case RF_stby_mode:
            // 进入待机模式
            PA_TX_EN_WriteL;
            PA_RX_EN_WriteL;
            cc2500_switch_GPIO_config(gpio_input);
            break;
        case RF_master_rx:
            PA_RX_EN_WriteL;
            cc2500_send_cmd(Cmd_SFRX);
            cc2500_send_cmd(Cmd_SRX);
        default:
            break;
    }
}

// 设置id
void cc2500_set_id(uint8_t id) {
    const uint16_t rf_id[100] = {
            0xC368, 0x61B4, 0x4122, 0x30DA, 0x2714, 0x2091, 0x1BEA, 0x186D, 0x15B6, 0x138A,            // ID 0-9
            0x11C3, 0x1048, 0x0F08, 0x0DF5, 0x0D06, 0x0C36, 0x087E, 0x0ADB, 0x0A48, 0x09C5,    // ID 10-19
            0x094E, 0x08E1, 0x087E, 0x0824, 0x07D0, 0x0784, 0x073C, 0x06FA, 0x06BC, 0x0683,   // ID 20-29
            0x0640, 0x061B, 0x05EB, 0x05BF, 0x0595, 0x056D, 0x0548, 0x0524, 0x0502, 0x04E2,  // ID 30-39
            0x04C4, 0x04A7, 0x048B, 0x0470, 0x0457, 0x043F, 0x0428, 0x0412, 0x03FC, 0x03E8,  // ID 40-49
            0x03D4, 0x03C2, 0x03AF, 0x039E, 0x038D, 0x037D, 0x036D, 0x035E, 0x034F, 0x0341,   // ID 50-59
            0x0334, 0x0326, 0x031A, 0x030D, 0x0301, 0x02F5, 0x02EA, 0x02DF, 0x02D4, 0x02CA,   // ID 60-69
            0x02C0, 0x02B6, 0x02AD, 0x02A4, 0x029A, 0x0292, 0x0289, 0x0281, 0x0279, 0x0271,   // ID 70-79
            0x0269, 0x0262, 0x025A, 0x0253, 0x024C, 0x0245, 0x023E, 0x0238, 0x0232, 0x022B,   // ID 80-89
            0x0225, 0x021F, 0x0219, 0x0214, 0x020E, 0x0209, 0x0203, 0x01FE, 0x01F9, 0x01F4    // ID 90-99
    };

    cc2500_write_reg(CC2500_04_SYNC1, rf_id[id] >> 8);
    cc2500_write_reg(CC2500_05_SYNC0, rf_id[id] & 0x00FF);
    delay_us(30);
    cc2500_send_cmd(Cmd_SCAL);
}

// 设置无线功率
void cc2500_set_power(uint8_t power) {
    const uint8_t tx_PA_power_table[CC2500_POWER_MAX + 1] = {
            // -14 -12 -10 -8 -6dbm
            0x8D, 0xC6, 0x97, 0x6E, 0x7F,
            // -4 -2 0 1dbm
            0xA9, 0xBB, 0xFE, 0xFF
    };

    cc2500_write_reg(CC2500_3E_PATABLE, tx_PA_power_table[power]);
}

#if 0
void cc2500_init_pgr() {
    // 初始化未完成IRQ会不断振荡，导致不断进中断，故初始化完成前须关闭中断
    Disable_Interrupts;
    RF_CSN_WriteL;
    delay_us(10);
    RF_CSN_WriteH;
    delay_us(20);
    cc2500_send_cmd(Cmd_SRES);
    delay_us(36);

    // GDO2-GDO0 Output Pin Configuration
    cc2500_write_reg(CC2500_00_IOCFG2, Val_00_IOCFG2);
    // cc2500_write_reg(CC2500_01_IOCFG1, Val_01_IOCFG1);
    cc2500_write_reg(CC2500_02_IOCFG0, Val_02_IOCFG0);

    // Frequency Control(Set 2413MHz)
    cc2500_write_reg(CC2500_0B_FSCTRL1, Val_0B_FSCTRL1);
    cc2500_write_reg(CC2500_0C_FSCTRL0, Val_0C_FSCTRL0);
    cc2500_write_reg(CC2500_0D_FREQ2, Val_0D_FREQ2);
    cc2500_write_reg(CC2500_0E_FREQ1, Val_0E_FREQ1);
    cc2500_write_reg(CC2500_0F_FREQ0, Val_0F_FREQ0);


    // Modem Configuration(MSK)
    cc2500_write_reg(CC2500_10_MDMCFG4, Val_10_MDMCFG4);
    cc2500_write_reg(CC2500_11_MDMCFG3, Val_11_MDMCFG3);
    cc2500_write_reg(CC2500_12_MDMCFG2, Val_12_MDMCFG2);
    cc2500_write_reg(CC2500_13_MDMCFG1, Val_13_MDMCFG1);
    cc2500_write_reg(CC2500_14_MDMCFG0, Val_14_MDMCFG0);

    // Channel Number
    cc2500_write_reg(CC2500_0A_CHANNR, Val_0A_CHANNR);

    cc2500_write_reg(CC2500_15_DEVIATN, Val_15_DEVIATN);

    cc2500_write_reg(CC2500_21_FREND1, Val_21_FREND1);
    cc2500_write_reg(CC2500_22_FREND0, Val_22_FREND0);

    cc2500_write_reg(CC2500_18_MCSM0, Val_18_MCSM0);
    cc2500_write_reg(CC2500_19_FOCCFG, Val_19_FOCCFG);


    cc2500_write_reg(CC2500_16_MCSM2, Val_16_MCSM2);
    cc2500_write_reg(CC2500_17_MCSM1, Val_17_MCSM1);

    cc2500_write_reg(CC2500_1A_BSCFG, Val_1A_BSCFG);
    // AGC Control
    cc2500_write_reg(CC2500_1B_AGCCTRL2, Val_1B_AGCCTRL2);
    cc2500_write_reg(CC2500_1C_AGCCTRL1, Val_1C_AGCCTRL1);
    cc2500_write_reg(CC2500_1D_AGCCTRL0, Val_1D_AGCCTRL0);

    // Frequency Synthesizer Calibration
    cc2500_write_reg(CC2500_23_FSCAL3, Val_23_FSCAL3);
    cc2500_write_reg(CC2500_24_FSCAL2, Val_24_FSCAL2);
    cc2500_write_reg(CC2500_25_FSCAL1, Val_25_FSCAL1);
    cc2500_write_reg(CC2500_26_FSCAL0, Val_26_FSCAL0);

    cc2500_write_reg(CC2500_29_FSTEST, Val_29_FSTEST);

    cc2500_write_reg(CC2500_2C_TEST2, Val_2C_TEST2);
    cc2500_write_reg(CC2500_2D_TEST1, Val_2D_TEST1);
    cc2500_write_reg(CC2500_2E_TEST0, Val_2E_TEST0);

    // TX FIFO = 33byte  RX FIFO = 32byte
    cc2500_write_reg(CC2500_03_FIFOTHR, Val_03_FIFOTHR);
    // User ID
    cc2500_write_reg(CC2500_04_SYNC1, Val_04_SYNC1);
    cc2500_write_reg(CC2500_05_SYNC0, Val_05_SYNC0);
    // Packet Automation Control
    cc2500_write_reg(CC2500_07_PKTCTRL1, Val_07_PKTCTRL1);
    // Variable packet length mode
    // Packet length configured by the first byte after sync word
    cc2500_write_reg(CC2500_08_PKTCTRL0, Val_08_PKTCTRL0);
    // Address
    cc2500_write_reg(CC2500_09_ADDR, Val_09_ADDR);


    // Maximum length packets
    cc2500_write_reg(CC2500_06_PKTLEN, Val_06_PKTLEN);

    cc2500_send_cmd(Cmd_SIDLE);

    uint8_t data[2] = {0x7E,0xFE};
    cc2500_spi_transmit_receive(data,RF.rx_data_buf,9);

    cc2500_send_cmd(0x33);
    cc2500_send_cmd(0x3A);
    cc2500_send_cmd(0x3B);
    cc2500_send_cmd(0x36);



    Enable_Interrupts;
}
#endif

// 无线模块初始化
void cc2500_init_pgr() {
    // 初始化未完成IRQ会不断振荡，导致不断进中断，故初始化完成前须关闭中断
    Disable_Interrupts;
    RF_CSN_WriteL;
    delay_us(30);
    RF_CSN_WriteH;
    delay_us(50);
    cc2500_send_cmd(Cmd_SRES);
    delay_us(50);

    // GDO2-GDO0 Output Pin Configuration
    cc2500_write_reg(CC2500_00_IOCFG2, Val_00_IOCFG2);
    // cc2500_write_reg(CC2500_01_IOCFG1, Val_01_IOCFG1);
    cc2500_write_reg(CC2500_02_IOCFG0, Val_02_IOCFG0);
    // TX FIFO = 33byte  RX FIFO = 32byte
    cc2500_write_reg(CC2500_03_FIFOTHR, Val_03_FIFOTHR);
    // User ID
    cc2500_write_reg(CC2500_04_SYNC1, Val_04_SYNC1);
    cc2500_write_reg(CC2500_05_SYNC0, Val_05_SYNC0);
    // Maximum length packets
    cc2500_write_reg(CC2500_06_PKTLEN, Val_06_PKTLEN);
    // Packet Automation Control
    cc2500_write_reg(CC2500_07_PKTCTRL1, Val_07_PKTCTRL1);
    // Variable packet length mode
    // Packet length configured by the first byte after sync word
    cc2500_write_reg(CC2500_08_PKTCTRL0, Val_08_PKTCTRL0);
    // Address
    cc2500_write_reg(CC2500_09_ADDR, Val_09_ADDR);
    // Channel Number
    cc2500_write_reg(CC2500_0A_CHANNR, Val_0A_CHANNR);
    // Frequency Control(Set 2413MHz)
    cc2500_write_reg(CC2500_0B_FSCTRL1, Val_0B_FSCTRL1);
    cc2500_write_reg(CC2500_0C_FSCTRL0, Val_0C_FSCTRL0);
    cc2500_write_reg(CC2500_0D_FREQ2, Val_0D_FREQ2);
    cc2500_write_reg(CC2500_0E_FREQ1, Val_0E_FREQ1);
    cc2500_write_reg(CC2500_0F_FREQ0, Val_0F_FREQ0);
    // Modem Configuration(MSK)
    cc2500_write_reg(CC2500_10_MDMCFG4, Val_10_MDMCFG4);
    cc2500_write_reg(CC2500_11_MDMCFG3, Val_11_MDMCFG3);
    cc2500_write_reg(CC2500_12_MDMCFG2, Val_12_MDMCFG2);
    cc2500_write_reg(CC2500_13_MDMCFG1, Val_13_MDMCFG1);
    cc2500_write_reg(CC2500_14_MDMCFG0, Val_14_MDMCFG0);
    cc2500_write_reg(CC2500_15_DEVIATN, Val_15_DEVIATN);

    cc2500_write_reg(CC2500_16_MCSM2, Val_16_MCSM2);
    cc2500_write_reg(CC2500_17_MCSM1, Val_17_MCSM1);
    cc2500_write_reg(CC2500_18_MCSM0, Val_18_MCSM0);
    cc2500_write_reg(CC2500_19_FOCCFG, Val_19_FOCCFG);
    cc2500_write_reg(CC2500_1A_BSCFG, Val_1A_BSCFG);
    // AGC Control
    cc2500_write_reg(CC2500_1B_AGCCTRL2, Val_1B_AGCCTRL2);
    cc2500_write_reg(CC2500_1C_AGCCTRL1, Val_1C_AGCCTRL1);
    cc2500_write_reg(CC2500_1D_AGCCTRL0, Val_1D_AGCCTRL0);
    cc2500_write_reg(CC2500_1F_WOREVT0, Val_1E_WOREVT1);
    cc2500_write_reg(CC2500_1E_WOREVT1, Val_1F_WOREVT0);
    cc2500_write_reg(CC2500_20_WORCTRL, Val_20_WORCTRL);

    cc2500_write_reg(CC2500_21_FREND1, Val_21_FREND1);
    cc2500_write_reg(CC2500_22_FREND0, Val_22_FREND0);

    // Frequency Synthesizer Calibration
    cc2500_write_reg(CC2500_23_FSCAL3, Val_23_FSCAL3);
    cc2500_write_reg(CC2500_24_FSCAL2, Val_24_FSCAL2);
    cc2500_write_reg(CC2500_25_FSCAL1, Val_25_FSCAL1);
    cc2500_write_reg(CC2500_26_FSCAL0, Val_26_FSCAL0);

    // RC Oscillator Configuration
    cc2500_write_reg(CC2500_27_RCCTRL1, Val_27_RCCTRL1);
    cc2500_write_reg(CC2500_28_RCCTRL0, Val_28_RCCTRL0);
    cc2500_write_reg(CC2500_29_FSTEST, Val_29_FSTEST);
    cc2500_write_reg(CC2500_2A_PTEST, Val_2A_PTEST);
    cc2500_write_reg(CC2500_2B_AGCTEST, Val_2B_AGCTEST);

    // Various Test Settings
    cc2500_write_reg(CC2500_2C_TEST2, Val_2C_TEST2);
    cc2500_write_reg(CC2500_2D_TEST1, Val_2D_TEST1);
    cc2500_write_reg(CC2500_2E_TEST0, Val_2E_TEST0);

    cc2500_send_cmd(Cmd_SCAL);
    cc2500_send_cmd(Cmd_SFRX);
    cc2500_send_cmd(Cmd_SFTX);
    cc2500_send_cmd(Cmd_SIDLE);

    Enable_Interrupts;
}

uint8_t cc2500_read_rssi() {
    uint8_t tx_buf[2], rx_buf[2];

    tx_buf[0] = (Access_Read_Single | CC2500_34_RSSI | 0x40);
    tx_buf[1] = 0xFF;
    cc2500_spi_transmit_receive(tx_buf, rx_buf, 2);

    return rx_buf[1];
}

// 扫描出来的频道冒泡排序
// is_ascending: true 为从小到大排序，false 为从大到小排序
void cc2500_rssi_bubble_sort(uint16_t rssi_arr[], uint8_t arr_size, bool is_ascending) {
    uint8_t i, j;
    uint16_t temp;
    bool swap_flag;

    const uint8_t ch_arr[CH_MAX] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10,
                                    11, 12, 13, 14, 15, 16, 17, 18, 19, 20,
                                    21, 22, 23, 24, 25, 26, 27, 28, 29, 30,
                                    31, 32};
    memcpy(RF.scan_ch, ch_arr, sizeof(RF.scan_ch));

    for (i = 1; i < (arr_size + 1); i++) {
        swap_flag = 0;

        for (j = 0; j < (arr_size - i); j++) {
            // 根据 is_ascending 参数决定排序方向
            if ((is_ascending && rssi_arr[j] > rssi_arr[j + 1]) ||
                (!is_ascending && rssi_arr[j] < rssi_arr[j + 1])) {
                swap_flag = true;
                temp = rssi_arr[j];
                rssi_arr[j] = rssi_arr[j + 1];
                rssi_arr[j + 1] = temp;

                temp = RF.scan_ch[j];
                RF.scan_ch[j] = RF.scan_ch[j + 1];
                RF.scan_ch[j + 1] = temp;
            }
        }

        if (!swap_flag)
            break;
    }
}

uint16_t rssi_arr[CH_MAX];

void cc2500_scan_ch_rssi() {
    if (RF.access_scan_ch) {
        uint8_t i, ch;
        Disable_Interrupts;
        if (RF.RSSIScanCnt < 160) {
            ch = (RF.RSSIScanCnt / 5);
            cc2500_send_cmd(Cmd_SIDLE);
            cc2500_write_reg(CC2500_18_MCSM0, 0x18);
            cc2500_set_channel(ch);
            delay_us(20);
            cc2500_send_cmd(Cmd_SRX);
            delay_us(20);

            for (i = 0; i < 15; i++) {
                // 避免溢出
                rssi_arr[ch] += cc2500_read_rssi();
                delay_us(600);
            }

            RF.RSSIScanCnt++;
        } else if (RF.RSSIScanCnt == 160) {
            cc2500_rssi_bubble_sort(rssi_arr, CH_MAX, true);
            RF.RSSIScanCnt++;
        } else {
            if (RF.RSSIScanCnt < 200) {
                send_scan_ch_finished();
                RF.access_scan_ch = false;
                RF.RSSIScanCnt = 0;
                // memset(rssi_arr, 0, sizeof(rssi_arr));
            }
        }
        Enable_Interrupts;
    }
}

void cc2500_scan_status() {
    if (Param.is_wireless_sync) {
        // Channel修改RF频率，ID修改RF的校验码
        if ((RF.last_channel != WL_SYNC_CH) ||
            (RF.last_id_num != WL_SYNC_ID)) {
            // 复位
            RF.reset_time = 0;
            RF.last_channel = WL_SYNC_CH;
            RF.last_id_num = WL_SYNC_ID;
        }
    } else {
        // Channel修改RF频率，ID修改RF的校验码
        if ((RF.last_channel != Setting.roller_values[roller_setting_ch]) ||
            (RF.last_id_num != Setting.roller_values[roller_setting_id])) {
            // 复位
            RF.reset_time = 0;
            RF.last_channel = Setting.roller_values[roller_setting_ch];
            RF.last_id_num = Setting.roller_values[roller_setting_id];
        }
    }

    // 清空接收Buffer
    // 避免cc2500收到数据后但单片机没有把内容读走，导致cc2500不再收到数据
    // cc2500收到数据后只会通知一次单片机，若单片机关闭了中断没有接受到通知信号，则后续都接不到
    RF.idle_time++;
    // 3s without Rx data, Reset RF
    if (RF.idle_time >= 3000) {
        RF.idle_time = 0;
        // RF.reset_time = 0;
        // cc2500_flush_rx_buffer();
        // WirelessProtocol.delay_flash_time = 0;
    }

    if (RF.reset_time < 500) {
        RF.reset_time++;
    }

    if (RF.reset_time == 498) {
        cc2500_init_pgr();
        delay_ms(1);
        if (Param.is_wireless_sync) {
            cc2500_set_channel(WL_SYNC_CH);
            delay_us(10);
            cc2500_set_id(WL_SYNC_ID);
            delay_us(10);
        } else {
            cc2500_set_channel(Setting.roller_values[roller_setting_ch]);
            delay_us(10);
            cc2500_set_id(Setting.roller_values[roller_setting_id]);
            delay_us(10);
        }

        cc2500_set_mode(RF_stby_mode);
        delay_ms(1);

        if (Setting.values[setting_dist] == dist_0_10) {
            cc2500_set_power(1);
        } else {
            cc2500_set_power(7);
        }
        delay_ms(1);

        //HAL_NVIC_DisableIRQ(RF_IRQ_EXTI5_EXTI_IRQn);
        // if (Normal.RF == switch_on) {
        // RF.mode = RF_rx_mode;
        // cc2500_set_mode(RF_rx_mode);
        //     HAL_NVIC_SetPriority(RF_IRQ_EXTI5_EXTI_IRQn, 0, 1);
        // } else {
//        cc2500_set_mode(RF_stby_mode);
//        RF.mode = RF_stby_mode;
//        RF.mode = RF_rx_mode;
//        cc2500_set_mode(RF_rx_mode);
//        HAL_NVIC_SetPriority(RF_IRQ_EXTI5_EXTI_IRQn, 2, 0);
        // }
        // else if (Normal.FunMode == Master_Fun)
        // {
        //     CC2500_Enter_StbyMode();
        //     RF.mode = RF_stby_mode;
        //     HAL_NVIC_SetPriority(RF_IRQ_EXT2_EXTI_IRQn, 2, 0);
        // }
        // else
        // {
        //     CC2500_Enter_SleepMode();
        //     RF.mode = RF_SleepMode;
        //     HAL_NVIC_SetPriority(RF_IRQ_EXT2_EXTI_IRQn, 2, 0);
        // }
        //HAL_NVIC_EnableIRQ(RF_IRQ_EXTI5_EXTI_IRQn);
    }
}
