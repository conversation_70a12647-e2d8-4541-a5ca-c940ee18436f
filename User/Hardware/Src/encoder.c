//
// Created by <PERSON><PERSON> on 2024/11/22.
//

#include <stdbool.h>
#include "encoder.h"
#include "system.h"
#include "amoled_qspi.h"
#include "user.h"

EncoderStruct Encoder;

int flag = 0;  //标志位
bool CW_1 = 0;
bool CW_2 = 0;

void scan_encoder_pgr() {
    // 只要处理一个脚的外部中断--上升沿&下降沿
    int alv = ENCODER_A_READ;
    int blv = ENCODER_B_READ;
    if (flag == 0 && !alv) {
        CW_1 = blv;
        flag = 1;
    }
    if (flag && alv) {
        CW_2 = !blv;  //取反是因为 alv,blv必然异步，一高一低。
        if (CW_1 && CW_2) {
            Encoder.encoder_times++;
        }
        if (CW_1 == false && CW_2 == false) {
            Encoder.encoder_times--;
        }
        flag = 0;
        amoled_keep_alive();
        System.defocused_countdown = ENCODER_NO_ACTION_TIME;
    }
}

void encoder_set_flag() {
    if (Encoder.encoder_add_times > Encoder.encoder_sub_times) {
        Encoder.encoder_flag = Encoder.encoder_add_times;
    } else if (Encoder.encoder_add_times < Encoder.encoder_sub_times) {
        Encoder.encoder_flag = -Encoder.encoder_sub_times;
    } else {
        Encoder.encoder_flag = 0;
    }
    if (Encoder.encoder_flag) {
        amoled_keep_alive();
        System.defocused_countdown = ENCODER_NO_ACTION_TIME;
    }
    Encoder.encoder_add_times = 0;
    Encoder.encoder_sub_times = 0;
}

int16_t encoder_value_read() {
    // 只读取双双高电平后的第一个波形
    static bool _is_AB_all_HIGH = true;
    uint8_t _ch_a = ENCODER_A_READ; // 先存再判断,避免刚好两次读的不一样
    uint8_t _ch_b = ENCODER_B_READ;   // 因为这些值要多次使用

    // 只读取双双高电平后的第一个波形
    if (_is_AB_all_HIGH) {
        if (_ch_a == 0 && _ch_b == 1) {
            _is_AB_all_HIGH = false; // 一格旋转只读取一次就够了
            return 1;              // 直接返回,编码器顺时针
        }
        if (_ch_a == 1 && _ch_b == 0) {
            _is_AB_all_HIGH = false;
            return -1; // 直接返回,编码器逆时针
        }
    } else {
        if (_ch_a == 1 && _ch_b == 1) {
            _is_AB_all_HIGH = true; // 都是高电平后,无旋转,进入可读状态
        }
    }
    return 0;
}