//
// Created by <PERSON><PERSON> on 2024/10/23.
//

#include <stdbool.h>
#include "nikon_protocol.h"
#include "system.h"
#include "rf_tx.h"
#include "rf_module.h"
#include "view_model.h"
#include "flash.h"

#define WaitSCSTime 8000
// 48000 29ms
// 10000 6.07ms
#define WaitSDATime 6000
#define WaitD2CSTime    128000

#define ISOLevel    56
#define ApertureLevel   46
#define TTL_Code_Number 127

uint8_t SPIRelaxTime;
uint8_t NikonCTLFlash;
// uint8_t NikonWaitFocus;

NikonProtocolStruct Nikon;
NormalFlagStructType NormalFlag;
MasterRamStructType MasterRam;
MasterFlagStructType MasterFlag;

const uint8_t TTLFlash_EV_Table[] = {30, 27, 23, 20, 17, 13, 10, 7, 3,
                                     0,
                                     3, 7, 10, 13, 17, 20, 23, 27, 30};

const uint8_t ISO_Data_Table[] = {0x00, 0x0a, 0x0c, 0x0e, 0x10, 0x12, 0x14, 0x16, 0x18, 0x1a, 0x1c,
                                  0x1e, 0x20, 0x21, 0x22, 0x24, 0x26, 0x27, 0x28, 0x2a, 0x2c,
                                  0x2d, 0x2e, 0x30, 0x32, 0x33, 0x34, 0x36, 0x38, 0x39, 0x3a,
                                  0x3c, 0x3e, 0x3f, 0x40, 0x42, 0x44, 0x45, 0x46, 0x48, 0x4a,
                                  0x4c, 0x4e, 0x50, 0x52, 0x54, 0x56, 0x58, 0x5A, 0x5C, 0x5E,
                                  0x60, 0x66, 0x6c, 0x72, 0x78, 0xff
};

const uint8_t Aperture_Data_Table[] = {0x00, 0x04, 0x06, 0x08, 0x0a, 0x0c, 0x0e, 0x10, 0x12, 0x14, 0x16,
                                       0x17, 0x18, 0x19, 0x1a, 0x1b, 0x1c, 0x1d, 0x1e, 0x1f, 0x20,
                                       0x21, 0x22, 0x24, 0x26, 0x27, 0x28, 0x2a, 0x2c, 0x2e, 0x30,
                                       0x32, 0x33, 0x34, 0x36, 0x38, 0x39, 0x3a, 0x3c, 0x3e, 0x3f,
                                       0x40, 0x42, 0x44, 0x46, 0x48, 0xff
};


// 0xA1 18byte SB910
const uint8_t A1_Data_Table[] = {0x01, 0x01, 0x05, 0x02, 0x08, 0x07, 0x81, 0x78, 0xB4, 0x64, 0x0A,
                                 0x4E, 0xFF, 0x3E, 0x90, 0x3E, 0x90, 0x1D};

// 0xA2 46byte
const uint8_t A2_Data_Table[] = {
        0x03, 0x05, 0x6D, 0x9B, 0x90, 0x86, 0x80,
        0x7A, 0x72, 0x6A, 0x65, 0x5E, 0x06, 0x75, 0x9E, 0x94, 0x8A, 0x80,
        0x77, 0x6F, 0x66, 0x60, 0x58, 0x06, 0x74, 0x9E, 0x95, 0x8B, 0x80,
        0x76, 0x6F, 0x66, 0x60, 0x59, 0x04, 0x5F, 0x96, 0x8A, 0x82, 0x80,
        0x7E, 0x79, 0x71, 0x6E, 0x6A, 0xFC
};

// 0xD3 TTL Level
const uint8_t D3_Data_Table[] = {0xFF, 0xFF, 0xD3};

// 0xD4
const uint8_t D4_Data_Table[] = {0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xD4};

// 0xC0 Shutter Finish
const uint8_t C0_Data_Table[] = {0xFF, 0xFF, 0x0A, 0x5E, 0x69};

// 0xE0 TTL Stop Code
const uint8_t E0_Data_Table[] = {0x00, 0xE0};

void set_nikon_SCK_input() {
    GPIO_InitTypeDef GPIO_InitStruct = {0};

    GPIO_InitStruct.Pin = NIKON_SCK_Pin;
    GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
    GPIO_InitStruct.Pull = GPIO_PULLUP;
    HAL_GPIO_Init(NIKON_SCK_GPIO_Port, &GPIO_InitStruct);
}

void set_nikon_SCK_output() {
    GPIO_InitTypeDef GPIO_InitStruct = {0};

    GPIO_InitStruct.Pin = NIKON_SCK_Pin;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
    HAL_GPIO_Init(NIKON_SCK_GPIO_Port, &GPIO_InitStruct);
}

void set_nikon_MOSI_EXTI_input() {
    GPIO_InitTypeDef GPIO_InitStruct = {0};

    GPIO_InitStruct.Pin = NIKON_MOSI_Pin;
    GPIO_InitStruct.Mode = GPIO_MODE_IT_FALLING;
    GPIO_InitStruct.Pull = GPIO_PULLUP;
    HAL_GPIO_Init(NIKON_MOSI_GPIO_Port, &GPIO_InitStruct);
}

void Nikon_SCS_Input() {
    GPIO_InitTypeDef GPIO_InitStruct = {0};

    GPIO_InitStruct.Pin = NIKON_SCS_Pin;
    GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
    GPIO_InitStruct.Pull = GPIO_PULLDOWN;
    HAL_GPIO_Init(NIKON_SCS_GPIO_Port, &GPIO_InitStruct);
}

void Nikon_SCS_Output() {
    GPIO_InitTypeDef GPIO_InitStruct = {0};

    GPIO_InitStruct.Pin = NIKON_SCS_Pin;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
    HAL_GPIO_Init(NIKON_SCS_GPIO_Port, &GPIO_InitStruct);
}

void Nikon_SDA_Drain_Output() {
    GPIO_InitTypeDef GPIO_InitStruct = {0};

    GPIO_InitStruct.Pin = NIKON_MOSI_Pin;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_OD;
    GPIO_InitStruct.Pull = GPIO_PULLUP;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
    HAL_GPIO_Init(NIKON_MOSI_GPIO_Port, &GPIO_InitStruct);
}

uint8_t SPI_Master_RxData() {
    uint8_t i, tWait, RxData = 0;

    Nikon_SCK_WriteH;
    for (i = 0; i < 8; i++) {
        // SCK频率需为60-65k
        delay_us_sw(8);
        Nikon_SCK_WriteL;

        delay_us_sw(7);
        Nikon_SCK_WriteH;

        RxData >>= 1;
        Nikon_SDA_WriteH;
        if (Nikon_SDA_Read) {
            RxData |= 0x80;
        } else {
            RxData |= 0x00;
        }
    }

    Nikon_SDA_WriteH;
    return RxData;
}

// D780 ZFC ZF系列相机
bool wait_D2_pre_flash() {
    uint32_t tWait;
    bool tFlag = false;

    tWait = WaitD2CSTime;
    while (tWait && Nikon_SCS_Read) { // Z30 wait 8.5ms
        tWait--;
    }
    if (tWait == 0) {
        tFlag = true;
    }

    tWait = WaitD2CSTime;
    set_nikon_SCK_input();
    while (tWait && !Nikon_SCS_Read) {
        tWait--;
        if (!Nikon_SCK_Read) { // Nikon.rx_data[0] == 0, 0x18
            delay_us(50);
            Nikon.pre_flash_times = 3;
            // Flash_IGBT_Pref(PreFlash2 - 10);
            delay_us(120);
            break;
        }
    }
    if (tWait == 0) {
        tFlag = true;
    }

    set_nikon_SCK_output();
    return tFlag;
}

uint8_t Get_TTL_Flash_EV(uint8_t tLevel) {
    uint8_t tLV;

    // if (Nikon.FunMode == Master_Fun)
    tLV = MasterRam.MttlLV;
    // else
    // tLV = Nikon.TTL_flash_level;

    if (tLV > 9) {
        tLevel += TTLFlash_EV_Table[tLV];
    } else {
        if (tLevel > TTLFlash_EV_Table[tLV]) {
            tLevel -= TTLFlash_EV_Table[tLV];
        } else {
            tLevel = 0;
        }
    }

    if (tLevel >= TTL_Code_Number) {
        tLevel = TTL_Code_Number;
    }

    return tLevel;
}

void Get_TTL_Flash_Prog() {
    uint16_t i, ttl_code;

    // 0x20-0x9F
    if (Nikon.shut_data[1] > 0x20) {
        ttl_code = (Nikon.shut_data[1] - 0x20);
    } else {
        ttl_code = 1;
    }

    if (NikonCTLFlash >= 24) {
        i = (NikonCTLFlash - 24) << 1;
        ttl_code += i;
    } else {
        i = (24 - NikonCTLFlash) << 1;
        if (ttl_code > i) {
            ttl_code -= i;
        } else {
            ttl_code = 0;
        }
    }

    // ISO 100
    if (Nikon.iso_sensor >= 11) {
        i = (Nikon.iso_sensor - 11) * 3;
        if (ttl_code > i) {
            ttl_code -= i;
        } else {
            ttl_code = 0;
        }
    }

    if (ttl_code > TTL_Code_Number) {
        ttl_code = TTL_Code_Number;
    }

    if (Nikon.pre_flash_times == 3) {
        Nikon.pre_flash_times = 2;
    }

    // High Speed Sync
    if (Nikon.shut_data[0] & 0x0f) {
        // Nikon.hss_flag = 1;
        Nikon.hss_flash_level = Get_TTL_Flash_EV(ttl_code);
    } else {
        // Low Speed
        // Nikon.hss_flag = 0;
        Nikon.cs_flash_level = Get_TTL_Flash_EV(ttl_code);
    }

    if (NormalFlag.bit.NoPref) {
        Nikon.cs_flash_level = 0;
        Nikon.hss_flash_level = 0;
        NormalFlag.bit.NoPref = 0;
    }
}

void SPI_Get_Shutter_Data() {
    // FOCUS_LED_WriteL;
    // NikonWaitFocus = 0;

    // if (MasterFlag.bit.Mmode == TTL_Mode) {
    // if ((Nikon.Mode == TTL_Mode) || (MasterFlag.bit.Mmode == TTL_Mode)) {
    // Get_TTL_Flash_Prog();
    // }
}

// 疑似兼容某相机，在主闪时才会出现，QZ不使用
void SPI_Get_Shutter_Master() {
    // FOCUS_LED_WriteL;
    // NikonWaitFocus = 0;

    // if (MasterFlag.bit.Mmode == TTL_Mode) {
    //     if (Nikon.pre_flash_times == 3) {
    //         if (Nikon.shut_data[1] >= 0x27) {
    //             Nikon.shut_data[1] -= 0x07;
    //         } else {
    //             Nikon.shut_data[1] = 0x20;
    //         }
    //     } else {
    //         if (Nikon.shut_data[1] >= 0x23) {
    //             Nikon.shut_data[1] -= 0x03;
    //         } else {
    //             Nikon.shut_data[1] = 0x20;
    //         }
    //     }
    //
    //     Get_TTL_Flash_Prog();
    // }
}


void Get_B0_Aperture_Data() {
    uint8_t i;
    static uint8_t tAperture;
    // Nikon.rx_data[6]
    if (Nikon.rx_data[12] == tAperture) {
        for (i = 1; i < ApertureLevel; i++) {
            if (tAperture <= Aperture_Data_Table[i])
                break;
        }

        if (i < ApertureLevel) {
            Nikon.Aperture = i;
        }
    } else {
        tAperture = Nikon.rx_data[12];
    }
}


void get_B0_zoom_data() {
    // const uint8_t StepMotor_Data_Table[] = {20, 24, 28, 35, 50, 70, 80, 105, 135, 200, 200};
    const uint8_t Zoom_Data_Table[] = {0x31, 0x37, 0x3c, 0x44, 0x50, 0x5c, 0x60, 0x6a, 0x73, 0x80, 0xff};

    int i;
    uint8_t check_zoom_auto = 0;

    for (int j = 0; j < GroupItemCount; ++j) {
        // (Param.group[j].is_auto_zoom & 1)：确保 is_auto_zoom 的值只有 0 或 1
        // 0b00011111(0b000EDCBA)
        check_zoom_auto |= (Param.group[j].is_auto_zoom & 1) << j;
    }

    if (check_zoom_auto) {
        if (Nikon.rx_data[7] != Nikon.last_zoom_code) {
            Nikon.last_zoom_code = Nikon.rx_data[7];
            for (i = 0; i < sizeof(Zoom_Data_Table); i++) {
                if (Nikon.last_zoom_code <= Zoom_Data_Table[i])
                    break;
            }

            for (int j = 0; j < GroupItemCount; ++j) {
                if (Param.group[j].is_auto_zoom) {
                    Param.group[j].auto_zoom_level = i;
                }
            }

            Nikon.last_zoom = i;
            // 发送自动zoom档位
            rf_send_zoom_0x5A();
        }
    }
}

void Get_B0_Shutter_Data() {
    // 补偿 EV Data
    // -5.0 ~ +5.0
    const uint8_t CTLFlash_Code_Table[] = {
            0x30, 0x2e, 0x2c, 0x2a, 0x28, 0x26, 0x24, 0x22,
            0x20, 0x1e, 0x1c, 0x1a, 0x18, 0x16, 0x14, 0x12,
            0x10, 0x0e, 0x0c, 0x0a, 0x08, 0x06, 0x04, 0x02,
            0x00, 0xfe, 0xfc, 0xfa, 0xf8, 0xf6, 0xf4, 0xf2,
            0xf0, 0xee, 0xec, 0xea, 0xe8, 0xe6, 0xe4, 0xe2,
            0xe0, 0xde, 0xdc, 0xda, 0xd8, 0xd6, 0xd4, 0xd2,
            0xd0};

    uint8_t i;
    // 闪光模式
    // 关闭闪光（此时不会给快门信号）
    // 考虑建议显示关闭了闪光的图标
    if (Nikon.rx_data[2] & (0x01 << 6)) {
        NormalFlag.bit.FlashOFF = 0;
    } else {
        NormalFlag.bit.FlashOFF = 1;
    }

    // 高速同步标志
    if ((Nikon.rx_data[2] & 0x30) == 0x30) {
        Param.status_bar_sync = sync_high_speed;
    }

    // 后帘同步标志
    if (Nikon.rx_data[2] & (0x01)) {
        Param.status_bar_sync = sync_rear;
    }

    if (((Nikon.rx_data[2] & 0x30) != 0x30) && !(Nikon.rx_data[2] & (0x01))) {
        Param.status_bar_sync = sync_front;
    }

    for (i = 0; i < 48; i++) {
        if (Nikon.rx_data[8] == CTLFlash_Code_Table[i]) {
            break;
        }
    }
    NikonCTLFlash = i;

    send_sync_status();
}

void Get_B0_ISO_Data() {
    uint8_t i;

    if (Nikon.rx_data[13] == Nikon.rx_data[14]) {
        NormalFlag.bit.ISOAuto = 0;
    } else {
        NormalFlag.bit.ISOAuto = 1;
    }

    for (i = 1; i < ISOLevel; i++) {
        if (Nikon.rx_data[4] <= ISO_Data_Table[i]) {
            break;
        }
    }

    if (i < ISOLevel) {
        Nikon.iso_sensor = i;
    }
}

void Nikon_SPI_Flag_Prog() {
    // if (NikonWaitFocus && (MenuFlag.bit.Afst == AFON)) {
    // FOCUS_LED_WriteH;
    // NikonWaitFocus--;
    // } else
    // FOCUS_LED_WriteL;

    if (NormalFlag.bit.CameraData) {
        Get_B0_ISO_Data();
        Get_B0_Shutter_Data();
        Get_B0_Aperture_Data();
        get_B0_zoom_data();
        NormalFlag.bit.CameraData = 0;

        if (Nikon.Mode >= MS1_Mode) {
            // LCD_RenewSet_Prog();
            Nikon.Mode = MUL_Mode;
        }

        // Remote_Mode Return Normal TTL
        // if (Nikon.LFunMode == Remote_Fun) {
        //     Nikon.Mode = TTL_Mode;
        //     Nikon.FunMode = Normal_Fun;
        //     Nikon.LFunMode = Normal_Fun;
        // }
    }

    if ((Nikon.has_link_flag) && (!Nikon_SCS_Read)) {
        SPIRelaxTime++;
        if (SPIRelaxTime >= 200) {
            SPIRelaxTime = 0;
            // Nikon.hss_flag = 0;
            // Nikon.APISO_TxCnt = 20;
            Nikon.has_link_flag = false;
            Param.status_bar_sync = sync_front;

            Nikon_SCS_Output();
            Nikon_SCS_WriteL;
            send_camera_link_status();
        }
    }

    // if (Nikon.FPLVRenew != Nikon.hss_flag) {
    //     Nikon.FPLVRenew = Nikon.hss_flag;
    // }
    //
    // if (NormalFlag.bit.CLinkRenew != NormalFlag.bit.CameraLink) {
    //     NormalFlag.bit.CLinkRenew = NormalFlag.bit.CameraLink;
    // }
}

void SPI_Master_TxData(uint8_t TxData) {
    uint8_t i, tWait;

    Nikon_SCK_WriteH;
    for (i = 0; i < 8; i++) {
        if (System.is_sys_clock_50M) {
            delay_us(6);
        } else {
            delay_us(7);
        }
        Nikon_SCK_WriteL;

        if (System.is_sys_clock_50M) {
            delay_us(2);
        } else {
            delay_us(3);
        }

        if (TxData & 0x01) {
            Nikon_SDA_WriteH;
        } else {
            Nikon_SDA_WriteL;
        }
        TxData >>= 1;

        if (System.is_sys_clock_50M) {
            delay_us(2);
        } else {
            delay_us(4);
        }
        Nikon_SCK_WriteH;
    }

    tWait = 180;
    while (tWait)
        tWait--;
    Nikon_SDA_WriteL;
}

void SPI_Master_Disable(uint16_t tSDA) {
    // Test_1_WriteH;
    uint32_t tWait;

    tWait = WaitSCSTime;
    while (tWait && !Nikon_SCS_Read) {
        tWait--;
    }

    delay_us(45);
    Nikon_SDA_WriteL;
    if (tSDA) {
        delay_us(315);
        Nikon_SDA_WriteH;
    }

    Nikon.has_link_flag = true;
    send_camera_link_status();
    // Test_1_WriteL;
}

bool SPI_Wait_CS_Prog() {
    uint32_t tWait;
    bool tFlag = false;

    tWait = WaitSCSTime;
    while (tWait && !Nikon_SCS_Read) {
        tWait--;
    }
    if (tWait == 0) {
        tFlag = true;
    }

    tWait = WaitSCSTime;
    while (tWait && Nikon_SCS_Read) {
        tWait--;
    }
    if (tWait == 0) {
        tFlag = true;
    }

    return tFlag;
}

// 校验和
uint8_t nikon_check_sum_pgr(uint8_t *pData, uint8_t tLen) {
    uint8_t i, tCRC = 1;

    for (i = 0; i < tLen; i++) {
        tCRC += *pData++;
    }

    return tCRC;
}

// 0xA0 22byte TTL Mode SB910
void SPI_Get_A0_Data_Prog() {
    const uint8_t A0_Normal_Table[] = {
            0x01, 0x02, 0xB6, 0x04, 0x00, 0x00, 0x88, 0x54, 0x30, 0x23, 0x01,
            0x01, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xEE};
    // 0xA0 Master
    const uint8_t A0_Master_Table[] = {
            0x01, 0x00, 0xB6, 0x04, 0x00, 0x00, 0x88, 0x54, 0x30, 0x23, 0x00,
            0xFF, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00};
    // 0xA0 Byte[5]
    const uint8_t A0_TTL_Level_Table[] = {
            0x12, 0x10, 0x0E, 0x0C, 0x0A, 0x08, 0x06, 0x04, 0x02,
            0x00, 0xFE, 0xFC, 0xFA, 0xF8, 0xF6, 0xF4, 0xF0, 0xEE};
    // 0xA0 Byte[5]
    const uint8_t A0_MUL_Level_Table[] = {
            // 0x30为1/256，0x2E为1/256+0.3以此类推
            0x30, 0x30, 0x30, 0x2E, 0x2E, 0x2E, 0x2E, 0x2C, 0x2C, 0x2C,
            0x2A, 0x2A, 0x2A, 0x28, 0x28, 0x28, 0x28, 0x26, 0x26, 0x26,
            0x24, 0x24, 0x24, 0x22, 0x22, 0x22, 0x22, 0x20, 0x20, 0x20,
            0x1E, 0x1E, 0x1E, 0x1C, 0x1C, 0x1C, 0x1C, 0x1A, 0x1A, 0x1A,
            0x18, 0x18, 0x18, 0x16, 0x16, 0x16, 0x16, 0x14, 0x14, 0x14,
            0x12, 0x12, 0x12, 0x10, 0x10, 0x10, 0x10, 0x0E, 0x0E, 0x0E,
            0x0C, 0x0C, 0x0C, 0x0A, 0x0A, 0x0A, 0x0A, 0x08, 0x08, 0x08,
            0x06, 0x06, 0x06, 0x04, 0x04, 0x04, 0x04, 0x02, 0x02, 0x02,
            0x00};

    uint8_t i;

    // if (Nikon.FunMode == Normal_Fun) {
    //     Nikon.SwitchFunCnt = 0;
    //     for (i = 0; i < 21; i++) {
    //         Nikon.A0_data[i] = A0_Normal_Table[i];
    //     }
    //
    //     if (Nikon.Mode == TTL_Mode) {
    //         Nikon.A0_data[1] = 0x02;
    //         Nikon.A0_data[5] = A0_TTL_Level_Table[Nikon.TTL_flash_level];
    //     } else if (Nikon.Mode == MUL_Mode) {
    //         Nikon.A0_data[1] = 0x06;
    //         Nikon.A0_data[5] = A0_MUL_Level_Table[Nikon.MULFlash];
    //     } else if (Nikon.Mode == RPT_Mode) {
    //         Nikon.A0_data[1] = 0x07;
    //         Nikon.A0_data[5] = A0_MUL_Level_Table[Nikon.RPTFlash];
    //     }
    // } else
    // if (Nikon.FunMode == Master_Fun) {
    if (Nikon.SwitchFunCnt < 6) {
        Nikon.SwitchFunCnt++;
        for (i = 0; i < 21; i++) {
            Nikon.A0_data[i] = A0_Normal_Table[i];
        }
    } else {
        for (i = 0; i < 21; i++) {
            Nikon.A0_data[i] = A0_Master_Table[i];
        }

        if (Param.is_Multi_page) {
            Nikon.A0_data[1] |= 0x80;
            // 将第4位置1，不允许相机调到高速
            Nikon.A0_data[3] |= (1 << 3);
            if (MasterFlag.bit.MrptEn) {
                Nikon.A0_data[1] |= 0x07;
                Nikon.A0_data[5] = A0_MUL_Level_Table[Param.multi.flash_level * 10];
            }
            // 不回传以避免ABC组关闭后DE组无法闪光（没有D4指令）
            // if (Param.group[group_A].is_turn_on_Multi) {
            Nikon.A0_data[16] |= 0x07;
            Nikon.A0_data[18] = A0_MUL_Level_Table[Param.multi.flash_level * 10];
            // }
            // if (Param.group[group_B].is_turn_on_Multi) {
            Nikon.A0_data[17] |= 0x70;
            Nikon.A0_data[19] = A0_MUL_Level_Table[Param.multi.flash_level * 10];
            // }
            // if (Param.group[group_C].is_turn_on_Multi) {
            Nikon.A0_data[17] |= 0x07;
            Nikon.A0_data[20] = A0_MUL_Level_Table[Param.multi.flash_level * 10];
            // }

        } else {
            // mode_OFF不进，不在列表也不进
            if (MasterFlag.bit.Mmode | Param.group[group_A].mode | Param.group[group_B].mode |
                Param.group[group_C].mode) {
#if ProductModel == QZ_N
                if (Setting.values[setting_sync] == sync_high_speed) {
                    // 将第4位置0，允许相机调到高速
                    Nikon.A0_data[3] &= ~(1 << 3);
                } else {
                    // 将第4位置1，不允许相机调到高速
                    Nikon.A0_data[3] |= (1 << 3);
                }
#endif

                if (MasterFlag.bit.Mmode == TTL_Mode) {
                    Nikon.A0_data[1] = 0x02;
                    Nikon.A0_data[5] = A0_TTL_Level_Table[MasterRam.MttlLV];
                } else if (MasterFlag.bit.Mmode == MUL_Mode) {
                    Nikon.A0_data[1] = 0x06;
                    Nikon.A0_data[5] = A0_MUL_Level_Table[MasterRam.MmulLV];
                }

                if (Param.group[group_A].mode | Param.group[group_B].mode | Param.group[group_C].mode |
                    Param.group[group_A].is_added_to_list | Param.group[group_B].is_added_to_list |
                    Param.group[group_C].is_added_to_list) {
                    Nikon.A0_data[1] |= 0x80;
                }

                if (Param.group[group_A].is_added_to_list) {
                    if (Param.group[group_A].mode == mode_TTL) {
                        Nikon.A0_data[16] |= 0x01;
                        Nikon.A0_data[18] = A0_TTL_Level_Table[Param.group[group_A].flash_level_TTL];
                    } else if (Param.group[group_A].mode == mode_M) {
                        Nikon.A0_data[16] |= 0x06;
                        Nikon.A0_data[18] = A0_MUL_Level_Table[Param.group[group_A].flash_level_M];
                    }
                }

                if (Param.group[group_B].is_added_to_list) {
                    if (Param.group[group_B].mode == mode_TTL) {
                        Nikon.A0_data[17] |= 0x10;
                        Nikon.A0_data[19] = A0_TTL_Level_Table[Param.group[group_B].flash_level_TTL];
                    } else if (Param.group[group_B].mode == mode_M) {
                        Nikon.A0_data[17] |= 0x60;
                        Nikon.A0_data[19] = A0_MUL_Level_Table[Param.group[group_B].flash_level_M];
                    }
                }

                if (Param.group[group_C].is_added_to_list) {
                    if (Param.group[group_C].mode == mode_TTL) {
                        Nikon.A0_data[17] |= 0x01;
                        Nikon.A0_data[20] = A0_TTL_Level_Table[Param.group[group_C].flash_level_TTL];
                    } else if (Param.group[group_C].mode == mode_M) {
                        Nikon.A0_data[17] |= 0x06;
                        Nikon.A0_data[20] = A0_MUL_Level_Table[Param.group[group_C].flash_level_M];
                    }
                }
            } else {
                Nikon.A0_data[1] = 0x06;
                Nikon.A0_data[5] = 0x30;
            }
        }
    }
    // }

    Nikon.A0_data[21] = nikon_check_sum_pgr(Nikon.A0_data, 21);
}

void SPI_Master_TTL_Disable() {
    uint32_t tWait;

    // tWait = WaitSCSTime;
    // while (tWait && !Nikon_SCS_Read) {
    //     tWait--;
    // }

    // delay_us(25);
    Nikon_SDA_WriteL;
    delay_ms(11);
    if (System.is_sys_clock_50M) {
        delay_us(520);
        // delay_us(550);
    } else {
        delay_us(545);
        // delay_us(570);
    }
    Nikon_SCK_WriteL;
    delay_us(565);
    Nikon_SCK_WriteH;
    Nikon_SDA_WriteH;

    Nikon.has_link_flag = true;
    send_camera_link_status();
}


void Nikon_SPI_TTL_Prog() {
    if (Setting.values[setting_shoot]) {
        return;
    }
    uint16_t i;
    uint32_t tWait;
    uint8_t RxData = 0;

    Nikon_SCS_Input();
    delay_us(10);
    SPIRelaxTime = 0;
    if (Nikon_SCS_Read && !Nikon_SDA_Read) {
        Nikon_SCK_WriteH;
        set_nikon_SCK_output();
        Nikon_SDA_WriteL;
        Nikon_SDA_Drain_Output();

        if (System.is_sys_clock_50M) {
            // 500 300us
            tWait = WaitSDATime;
        } else {
            tWait = 30000;
        }
        // Test_1_WriteH;
        while (tWait && Nikon_SCS_Read) {
            tWait--;
            // if (tWait == 500/2){
            //     Nikon_SDA_WriteH;
            //     Nikon_SDA_WriteL;
            // }
        }
        // Test_1_WriteL;

        // if (!tWait) {
        //     Nikon_SDA_WriteH;
        //     if (System.is_sys_clock_50M) {
        //         tWait = WaitSDATime / 2;
        //     } else {
        //         tWait = 4000 / 2;
        //     }
        //     Nikon_SDA_WriteL;
        //     while (tWait && Nikon_SCS_Read) {
        //         tWait--;
        //     }
        // }

        Nikon_SDA_WriteH;
        if (tWait) {
            RxData = SPI_Master_RxData();
        } else {
            RxData = 0;
        }

        switch (RxData) {
            case 0xA1:
                for (i = 0; i < 18; i++) {
                    SPI_Wait_CS_Prog();
                    SPI_Master_TxData(A1_Data_Table[i]);
                }

                SPI_Master_Disable(1);
                break;
            case 0xB1:
                for (i = 0; i < 10; i++) {
                    SPI_Wait_CS_Prog();
                    Nikon.rx_data[i] = SPI_Master_RxData();
                }

                SPI_Master_Disable(1);
                break;
            case 0xA2:
                for (i = 0; i < 46; i++) {
                    SPI_Wait_CS_Prog();
                    SPI_Master_TxData(A2_Data_Table[i]);
                }

                SPI_Master_Disable(1);
                break;
            case 0xA0:
                SPI_Get_A0_Data_Prog();
                for (i = 0; i < 22; i++) {
                    if (SPI_Wait_CS_Prog()) {
                        break;
                    }
                    SPI_Master_TxData(Nikon.A0_data[i]);
                }

                SPI_Master_Disable(1);
                // rf_send_ISO_zoom_0x8E();
                break;
            case 0xB0:
                Nikon.rx_data[0] = 0xB0;
                for (i = 1; i < 16; i++) {
                    if (SPI_Wait_CS_Prog()) {
                        break;
                    }
                    Nikon.rx_data[i] = SPI_Master_RxData();
                }

                if (Nikon.rx_data[15] == nikon_check_sum_pgr(Nikon.rx_data, 15)) {
                    NormalFlag.bit.CameraData = 1;
                }

                SPI_Master_Disable(1);
                break;
            case 0xC0:
                for (i = 0; i < 5; i++) {
                    SPI_Wait_CS_Prog();
                    SPI_Master_TxData(C0_Data_Table[i]);
                }

                SPI_Master_Disable(1);
                break;
            case 0xC1:
                SPI_Master_Disable(1);
                break;
            case 0xD0:
                for (i = 0; i < 2; i++) {
                    SPI_Wait_CS_Prog();
                    Nikon.rx_data[i] = SPI_Master_RxData();
                }

                // if (Nikon.rx_data[0])
                //   NikonWaitFocus = 64;
                // else
                //   NikonWaitFocus = 0;

                // NikonWaitFocus = 64;
                SPI_Master_Disable(1);
                break;
            case 0xD1:
                SPI_Wait_CS_Prog();
                if (SPI_Master_RxData() == 0x80) {
                    flash_multi_mode(0, 29, 3, 4);
                }
                SPI_Master_Disable(1);
                break;
            case 0xD2:
                SPI_Wait_CS_Prog();
                Nikon.rx_data[0] = SPI_Master_RxData();
                SPI_Master_Disable(0);
                delay_ms(3);
                Nikon_SDA_WriteH;

                wait_D2_pre_flash();
                break;
            case 0xD3:
                // TTL Flash Level
                for (i = 0; i < 2; i++) {
                    SPI_Wait_CS_Prog();
                    Nikon.shut_data[i] = SPI_Master_RxData();
                }
                SPI_Wait_CS_Prog();
                SPI_Master_TxData(0xD3);
                SPI_Master_Disable(0);

                delay_ms(2);
                // SPI_Get_Shutter_Data();
                Nikon_SDA_WriteH;
                break;
            case 0xD4:
                // Master Flash Level
                for (i = 0; i < 6; i++) {
                    SPI_Wait_CS_Prog();
                    Nikon.shut_data[i] = SPI_Master_RxData();
                }
                SPI_Wait_CS_Prog();
                SPI_Master_TxData(0xD4);
                SPI_Master_Disable(0);

                // Nikon.APISO_TxCnt = 20;
                // rf_send(rf_send_ISO_zoom_0x8E);
                // rf_send_ISO_zoom_0x8E();
                // delay_ms(2);
                rf_send_tcm_0xED_0x02();
                // SPI_Get_Shutter_Master();
                rf_send_0xD4();
                Nikon_SDA_WriteH;
                delay_ms(2);
                rf_send_rise_0x57();
                // delay_ms(2);
                // cc2500_set_mode(RF_master_rx);
                Nikon.has_shutter_flag = true;
                break;
            case 0xD5:
                // 机顶造影
                SPI_Wait_CS_Prog();
                SPI_Master_TxData(0xD9);
                // flash_multi_mode(0, 0, 70, 70);
                SPI_Master_Disable(1);
                break;
            case 0xD6:
                // 主控造影
                for (i = 0; i < 4; i++) {
                    SPI_Wait_CS_Prog();
                    Nikon.rx_data[i] = SPI_Master_RxData();
                }
                SPI_Wait_CS_Prog();
                SPI_Master_TxData(0xD6);
                SPI_Master_Disable(1);
                rf_send_pv_0x6A();
                break;
            case 0xD7: // First PreFlash
                SPI_Master_Disable(0);
                delay_us(1000);

                Nikon_SCK_WriteL;
                delay_us(50);
                // Flash_IGBT_Pref(PreFlash1);
                delay_us(120);
                Nikon_SCK_WriteH;

                delay_us(938);
                Nikon.pre_flash_times = 1;
                Nikon_SDA_WriteH;
                break;
            case 0xD8: // Second PreFlash
                SPI_Wait_CS_Prog();
                Nikon.rx_data[0] = SPI_Master_RxData();

                SPI_Master_Disable(0);
                delay_us(1000);

                Nikon_SCK_WriteL;
                delay_us(50);
                // Flash_IGBT_Pref(PreFlash2);
                delay_us(120);
                Nikon_SCK_WriteH;

                delay_us(938);
                Nikon.pre_flash_times = 2;
                Nikon_SDA_WriteH;
                break;
            case 0xD9:
                // 曝光锁定预闪
                SPI_Wait_CS_Prog();
                SPI_Master_TxData(0xD9);
                SPI_Master_Disable(1);
                break;
            case 0xDA:
                // Master A Group
                rf_send_pre_flash1(0x2A);
                SPI_Master_TTL_Disable();
                break;
            case 0xDB:
                // Master B Group
                rf_send_pre_flash1(0x2C);
                SPI_Master_TTL_Disable();
                break;
            case 0xDC:
                // Master C Group
                rf_send_pre_flash1(0x2E);
                SPI_Master_TTL_Disable();
                break;
            case 0xDD:
                // Master All Preflash2
                rf_send_pre_flash2();
                SPI_Master_TTL_Disable();
                break;
            case 0xE0: // SPI Finish
                for (i = 0; i < 2; i++) {
                    SPI_Wait_CS_Prog();
                    SPI_Master_TxData(E0_Data_Table[i]);
                }

                set_nikon_SCK_input();
                SPI_Master_Disable(1);
                break;
            case 0:
                Test_1_WriteH;
                Test_1_WriteL;
            default:
                // SPI_Master_Disable(1);
                break;
        }
    }

    Nikon_SDA_WriteH;
    set_nikon_MOSI_EXTI_input();
}


uint8_t SPI_Master_Prog(uint8_t TxData) {
    uint8_t i, tWait, RxData = 0;

    Nikon_SCK_WriteH;
    for (i = 0; i < 8; i++) {
        tWait = 168;
        while (tWait) {
            tWait--;
        }
        Nikon_SCK_WriteL;

        tWait = 12;
        while (tWait) {
            tWait--;
        }

        if (TxData & 0x01) {
            Nikon_SDA_WriteH;
        } else {
            Nikon_SDA_WriteL;
        }
        TxData >>= 1;

        tWait = 176;
        while (tWait) {
            tWait--;
        }
        Nikon_SCK_WriteH;

        tWait = 12;
        while (tWait) {
            tWait--;
        }

        RxData >>= 1;
        Nikon_SDA_WriteH;
        if (Nikon_SDA_Read) {
            RxData |= 0x80;
        } else {
            RxData |= 0x00;
        }
    }

    Nikon_SDA_WriteH;
    return RxData;
}


