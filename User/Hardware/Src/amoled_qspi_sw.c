//
// Created by <PERSON><PERSON> on 2024/7/7.
//

#include "amoled_qspi_sw.h"

#if 0


#define COL    410
#define ROW    502

void qspi_send_data_1l(unsigned int dat) {
    unsigned char i;

    for (i = 0; i < 8; i++) {
        if ((dat & 0x80))
            QSPI_IO0_WriteH;
        else
            QSPI_IO0_WriteL;

        dat <<= 1;

        QSPI_CLK_WriteL;
        // __NOP();
        // __NOP();
        // __NOP();
        // __NOP();
        QSPI_CLK_WriteH;
    }
}

void qspi_write_data_1l(unsigned int val) {
    // unsigned char n;
    // for (n = 0; n < 8; n++) {
    //     if (val & 0x80)
    //         QSPI_IO0_WriteH; /*SPI_SDA 为写进去的值*/
    //     else
    //         QSPI_IO0_WriteL;
    //
    //     val <<= 1;
    //
    //     QSPI_CLK_WriteL;
    //     __NOP();
    //     __NOP();
    //     __NOP();
    //     __NOP();
    //     QSPI_CLK_WriteH;
    // }
    qspi_send_data_1l(val);
}

void qspi_write_cmd(unsigned int cmd) {
    qspi_send_data_1l(0x02);
    qspi_send_data_1l(0x00);
    qspi_send_data_1l(cmd);
    qspi_send_data_1l(0x00);
    // __NOP();
    // __NOP();
    // HAL_Delay(2);
}

void qspi_read_cmd(unsigned int cmd) {
    qspi_send_data_1l(0x03);
    qspi_send_data_1l(0x00);
    qspi_send_data_1l(cmd);
    qspi_send_data_1l(0x00); // PAM
    __NOP();
    __NOP();
}

void qspi_write_data_4l(unsigned int val) {
    // QSPI_CS_WriteL;
    if (val & 0x80)
        QSPI_IO3_WriteH;
    else
        QSPI_IO3_WriteL;

    if (val & 0x40)
        QSPI_IO2_WriteH;
    else
        QSPI_IO2_WriteL;

    if (val & 0x20)
        QSPI_IO1_WriteH;
    else
        QSPI_IO1_WriteL;

    if (val & 0x10)
        QSPI_IO0_WriteH;
    else
        QSPI_IO0_WriteL;

    QSPI_CLK_WriteL; // delay_us(2);
    QSPI_CLK_WriteH; // delay_us(2);

    if (val & 0x08)
        QSPI_IO3_WriteH;
    else
        QSPI_IO3_WriteL;

    if (val & 0x04)
        QSPI_IO2_WriteH;
    else
        QSPI_IO2_WriteL;

    if (val & 0x02)
        QSPI_IO1_WriteH;
    else
        QSPI_IO1_WriteL;

    if (val & 0x01)
        QSPI_IO0_WriteH;
    else
        QSPI_IO0_WriteL;

    QSPI_CLK_WriteL; // delay_us(2);
    QSPI_CLK_WriteH; // delay_us(1);
}

void write_disp_data(unsigned int val) {
    qspi_write_data_4l(val >> 8);
    qspi_write_data_4l(val & 0xFF);
}

void AMOLED_Block_Write(unsigned int Xstart, unsigned int Xend, unsigned int Ystart, unsigned int Yend) {
    Xstart = Xstart + 0x16;
    Xend = Xend + 0x16;

    QSPI_CS_WriteL;
    qspi_write_cmd(0x2a); // Set Column Start Address
    qspi_write_data_1l(Xstart >> 8);
    qspi_write_data_1l(Xstart & 0xff);
    qspi_write_data_1l(Xend >> 8);
    qspi_write_data_1l(Xend & 0xff);
    QSPI_CS_WriteH;

    QSPI_CS_WriteL;
    qspi_write_cmd(0x2b); // Set Row Start Address
    qspi_write_data_1l(Ystart >> 8);
    qspi_write_data_1l(Ystart & 0xff);
    qspi_write_data_1l(Yend >> 8);
    qspi_write_data_1l(Yend & 0xff);
    QSPI_CS_WriteH;

    // QSPI_CS_WriteL;
    // qspi_write_cmd(0x2c); // Memory Write
    // QSPI_CS_WriteH;
}

// 画点-----PEN_COLOR:此点的颜色
void amoled_draw_point(int x, int y, long int color) {
    if (x >= COL)
        x = COL - 1;
    if (y >= ROW)
        y = ROW - 1;

    // AMOLED_Block_Write(x,x,y,y);
    write_disp_data(color);
}


void SPI_4wire_data_1wire_Addr(unsigned int First_Byte, unsigned int Addr) {
    qspi_send_data_1l(First_Byte); //
    qspi_send_data_1l(0x00);
    qspi_send_data_1l(Addr);
    qspi_send_data_1l(0x00); // PA
}

void SPI_4W_DATA_1W_ADDR_START(void) {
    QSPI_CS_WriteL;
    SPI_4wire_data_1wire_Addr(0x32, 0x2C);
}

void SPI_4W_DATA_1W_ADDR_END(void) {
    QSPI_CS_WriteH;
    QSPI_CS_WriteL;
    SPI_4wire_data_1wire_Addr(0x32, 0x00);
    QSPI_CS_WriteH;
}


void AMOLED_Clear(unsigned int color) {
    unsigned int i, j;
    AMOLED_Block_Write(0, COL - 1, 0, ROW - 1); // set AMOLED GRAM

    // SPI_4W_DATA_1W_ADDR_START();
    QSPI_CS_WriteL;
    SPI_4wire_data_1wire_Addr(0x32, 0x2C);

    for (i = 0; i < ROW; i++)
        for (j = 0; j < COL; j++) {
            write_disp_data(color);
        }
    QSPI_CS_WriteH;
    SPI_4W_DATA_1W_ADDR_END();
}

void DispColor(unsigned int color) {
    unsigned int i, j;

    AMOLED_Block_Write(0, COL - 1, 0, ROW - 1);

    QSPI_CS_WriteL;
    qspi_write_data_1l(0x12);
    qspi_write_data_4l(0x00);
    qspi_write_data_4l(0x2c);
    qspi_write_data_4l(0x00);

    for (i = 0; i < COL; i++)
        for (j = 0; j < ROW; j++) {
            write_disp_data(color);
            // qspi_write_data_4l(color >> 8);
            // qspi_write_data_4l(color);
        }
    QSPI_CS_WriteH;
}

void amoled_fill_color(int xsta, int ysta, int xend, int yend, long int color) {
    unsigned int i, j;

    AMOLED_Block_Write(xsta, xend, ysta, yend);

    SPI_4W_DATA_1W_ADDR_START();
    // QSPI_CS_WriteL;
    // qspi_write_data_1l(0x12);
    // qspi_write_data_4l(0x00);
    // qspi_write_data_4l(0x2c);
    // qspi_write_data_4l(0x00);

    for (i = xsta; i < xend; i++)
        for (j = ysta; j < yend; j++) {
            write_disp_data(color);
        }

    SPI_4W_DATA_1W_ADDR_END();
    // QSPI_CS_WriteH;
}

void amoled_init() {
    AMOLED_RST_WriteH;
    delay_ms(20);
    AMOLED_RST_WriteL;
    delay_ms(80);
    AMOLED_RST_WriteH;
    delay_ms(80);

    QSPI_CS_WriteL;
    qspi_write_cmd(0x3A);
    qspi_write_data_1l(0x55);
    QSPI_CS_WriteH;

    QSPI_CS_WriteL;
    qspi_write_cmd(0xFE); // QSPI setting, MIPI remove
    qspi_write_data_1l(0x00);
    QSPI_CS_WriteH;

    QSPI_CS_WriteL;
    qspi_write_cmd(0xC4);
    qspi_write_data_1l(0x80); // Interface Pixel Format	16bit/pixel
    // qspi_write_data_1l(0x66); //Interface Pixel Format	18bit/pixel
    // qspi_write_data_1l(0x77); //Interface Pixel Format	24bit/pixel
    QSPI_CS_WriteH;

    QSPI_CS_WriteL;
    qspi_write_cmd(0x3A);
    qspi_write_data_1l(0x55);
    QSPI_CS_WriteH;

    QSPI_CS_WriteL;
    qspi_write_cmd(0x35);
    qspi_write_data_1l(0x00);
    QSPI_CS_WriteH;

    QSPI_CS_WriteL;
    qspi_write_cmd(0x53); // Write Display Brightness	MAX_VAL=0XFF
    qspi_write_data_1l(0x20);
    QSPI_CS_WriteH;

    QSPI_CS_WriteL;
    qspi_write_cmd(0x51);
    qspi_write_data_1l(0xFF);
    QSPI_CS_WriteH;

    QSPI_CS_WriteL;
    qspi_write_cmd(0x63);
    qspi_write_data_1l(0xFF);
    QSPI_CS_WriteH;

    QSPI_CS_WriteL;
    qspi_write_cmd(0x2A);
    qspi_write_data_1l(0x00);
    qspi_write_data_1l(0x16);
    qspi_write_data_1l(0x01);
    qspi_write_data_1l(0xAF);
    QSPI_CS_WriteH;

    QSPI_CS_WriteL;
    qspi_write_cmd(0x2B);
    qspi_write_data_1l(0x00);
    qspi_write_data_1l(0x00);
    qspi_write_data_1l(0x01);
    qspi_write_data_1l(0xF5);
    QSPI_CS_WriteH;

    QSPI_CS_WriteL;
    qspi_write_cmd(0x11); // Sleep out
    QSPI_CS_WriteH;
    // HAL_Delay(80);

    QSPI_CS_WriteL;
    qspi_write_cmd(0x29); // Display on
    QSPI_CS_WriteH;
    // HAL_Delay(20);

    // QSPI_CS_WriteL;
    // qspi_write_cmd(0x51); // Write Display Brightness	MAX_VAL=0XFF
    // qspi_write_data_1l(0xC8);
    // QSPI_CS_WriteH;

    DispColor(0x0000);
    // 蓝色
    DispColor(0x64BD);
}

#endif