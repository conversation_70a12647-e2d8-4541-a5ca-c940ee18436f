//
// Created by <PERSON><PERSON> on 2025/4/18.
//

#include "bt_module.h"
#ifdef BT_ON
#include <string.h>
#include "lvgl.h"

UartStructType Uart;
BluetoothStructType Bluetooth;

// 蓝牙串口接收数据
void bt_receive_data() {
    HAL_UARTEx_ReceiveToIdle_DMA(&huart3, Uart.bt_rx_buffer, sizeof(Uart.bt_rx_buffer));
}

// 蓝牙串口发送指令
void bt_send_cmd(uint8_t *str, uint8_t len) {
    HAL_UART_Transmit_DMA(&huart3, str, len);
    // (&huart2) -> gState = HAL_UART_STATE_READY;
    if (Bluetooth.send_data) {
        Bluetooth.timeout = 0;
    } else {
        Bluetooth.timeout++;
    }
}

// 蓝牙模块AT指令处理
void bt_at_cmd_receive_handle() {
    if (Uart.bt_at_finished) {
        Bluetooth.timeout = 0;

        // 接收到"OK+NAME\n"即设备名称修改成功
        // if (strcmp((char *) Uart.bt_final_buffer, "OK+SLEEP\n") == 0) {
            lv_obj_create(lv_layer_sys());
        // }

        // 清空数组
        memset(Uart.bt_final_buffer, 0, sizeof(Uart.bt_final_buffer));
        Uart.bt_at_finished = 0;
    }
}

// 获取bt_final_buffer中空闲的数组位置索引
uint8_t get_buffer_idle(const uint8_t arr[], uint8_t max) {
    for (int i = 0; i < max; ++i) {
        if (arr[i] == 0) {
            return i;
        }
    }
    return 0;
}

void HAL_UARTEx_RxEventCallback(UART_HandleTypeDef *huart, uint16_t Size) {
    // 中断没处理完出去不可再操作同一串口
    // 不可在此处再次打开串口接收
    if (huart == &huart3) {
        // HAL_UART_Transmit_DMA(&huart3, Uart.bt_rx_buffer, Size);
        uint8_t final_buffer_index = get_buffer_idle(Uart.bt_final_buffer, BT_Buffer_Size);
        // 将Uart.bt_rx_buffer的内容复制到Uart.bt_final_buffer内
        for (int i = 0; i < Size; ++i) {
            Uart.bt_final_buffer[final_buffer_index + i] = Uart.bt_rx_buffer[i];
            // "\n"为指针，需用'\n'
            if (Uart.bt_final_buffer[final_buffer_index + i] == '\n') {
                Uart.bt_at_finished = 1;
            }
        }
        if (Uart.bt_final_buffer[0] == 0x78) {
            // 过滤led协议头数据
            // memset(Uart.bt_final_buffer, 0, sizeof(Uart.bt_final_buffer));
        } else if (Uart.bt_final_buffer[0] == 0x85) {
            // 0x85为app发送的指令
            // bt_app_cmd_receive_handle(Size);
        } else {
            // 蓝牙AT指令处理
            bt_at_cmd_receive_handle();
        }
        // 清空数组
        memset(Uart.bt_rx_buffer, 0, sizeof(Uart.bt_rx_buffer));
    }
}

void HAL_UART_TxCpltCallback(UART_HandleTypeDef *huart) {
    if (huart->Instance == USART3) {
        __NOP();
    }
}
#endif