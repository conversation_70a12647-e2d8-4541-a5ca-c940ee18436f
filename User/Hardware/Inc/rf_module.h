//
// Created by <PERSON><PERSON> on 2024/9/9.
//

#ifndef QZ_N_RF_MODULE_H
#define QZ_N_RF_MODULE_H

#include "main.h"
#include "user.h"
#include "stm32h5xx_ll_gpio.h"
#include "system.h"

#define WL_SYNC_CH (10-1)
#define WL_SYNC_ID 93

#define CC2500_POWER_MAX 8

#define RF_SPI_TimeOut 3000
#define RF_Wait_TimeOut 6000

#define RF_IRQ_Read HAL_GPIO_ReadPin(RF_IRQ_GPIO_Port, RF_IRQ_Pin)

#define RF_CSN_WriteH HAL_GPIO_WritePin(RF_CSN_GPIO_Port, RF_CSN_Pin,GPIO_PIN_SET)
#define RF_CSN_WriteL HAL_GPIO_WritePin(RF_CSN_GPIO_Port, RF_CSN_Pin,GPIO_PIN_RESET)

#define RF_SCK_WriteH HAL_GPIO_WritePin(RF_SCK_GPIO_Port, RF_SCK_Pi<PERSON>, GP<PERSON>_PIN_SET)
#define RF_SCK_WriteL HAL_GPIO_WritePin(RF_SCK_GPIO_Port, RF_SCK_Pin, GPIO_PIN_RESET)

#define RF_MISO_Read HAL_GPIO_ReadPin(RF_MISO_GPIO_Port, RF_MISO_Pin)
#define RF_MOSI_WriteH HAL_GPIO_WritePin(RF_MISO_GPIO_Port, RF_MISO_Pin, GPIO_PIN_SET)
#define RF_MOSI_WriteL HAL_GPIO_WritePin(RF_MISO_GPIO_Port, RF_MISO_Pin, GPIO_PIN_RESET)

#define PA_TX_EN_WriteH HAL_GPIO_WritePin(RF_PA_TXEN_GPIO_Port, RF_PA_TXEN_Pin, GPIO_PIN_SET)
#define PA_TX_EN_WriteL HAL_GPIO_WritePin(RF_PA_TXEN_GPIO_Port, RF_PA_TXEN_Pin, GPIO_PIN_RESET)

#define PA_RX_EN_WriteH HAL_GPIO_WritePin(RF_PA_RXEN_GPIO_Port, RF_PA_RXEN_Pin, GPIO_PIN_SET)
#define PA_RX_EN_WriteL HAL_GPIO_WritePin(RF_PA_RXEN_GPIO_Port, RF_PA_RXEN_Pin, GPIO_PIN_RESET)

#define CC2500_00_IOCFG2 0x0000   // GDO2 Output Pin Configuration
#define CC2500_01_IOCFG1 0x0001   // GDO1 Output Pin Configuration
#define CC2500_02_IOCFG0 0x0002   // GDO0 Output Pin Configuration
#define CC2500_03_FIFOTHR 0x0003  // RX FIFO and TX FIFO Thresholds
#define CC2500_04_SYNC1 0x0004    // Sync Word, High Byte(User ID)
#define CC2500_05_SYNC0 0x0005    // Sync Word, Low Byte(User ID)
#define CC2500_06_PKTLEN 0x0006   // Packet Length
#define CC2500_07_PKTCTRL1 0x0007 // Packet Automation Control
#define CC2500_08_PKTCTRL0 0x0008 // Packet Automation Control
#define CC2500_09_ADDR 0x0009     // Device Address
#define CC2500_0A_CHANNR 0x000A   // Channel Number
#define CC2500_0B_FSCTRL1 0x000B  // Frequency Synthesizer Control
#define CC2500_0C_FSCTRL0 0x000C  // Frequency Synthesizer Control
#define CC2500_0D_FREQ2 0x000D    // Frequency Control Word, High Byte
#define CC2500_0E_FREQ1 0x000E    // Frequency Control Word, Middle Byte
#define CC2500_0F_FREQ0 0x000F    // Frequency Control Word, Low Byte
#define CC2500_10_MDMCFG4 0x0010  // Modem Configuration
#define CC2500_11_MDMCFG3 0x0011  // Modem Configuration
#define CC2500_12_MDMCFG2 0x0012  // Modem Configuration
#define CC2500_13_MDMCFG1 0x0013  // Modem Configuration
#define CC2500_14_MDMCFG0 0x0014  // Modem Configuration
#define CC2500_15_DEVIATN 0x0015  // Modem Deviation Setting
#define CC2500_16_MCSM2 0x0016    // Main Radio Control State Machine Configuration
#define CC2500_17_MCSM1 0x0017    // Main Radio Control State Machine Configuration
#define CC2500_18_MCSM0 0x0018    // Main Radio Control State Machine Configuration
#define CC2500_19_FOCCFG 0x0019   // Frequency Offset Compensation Configuration
#define CC2500_1A_BSCFG 0x001A    // Bit Synchronization Configuration
#define CC2500_1B_AGCCTRL2 0x001B // AGC Control
#define CC2500_1C_AGCCTRL1 0x001C // AGC Control
#define CC2500_1D_AGCCTRL0 0x001D // AGC Control
#define CC2500_1E_WOREVT1 0x001E  // High Byte Event0 Timeout
#define CC2500_1F_WOREVT0 0x001F  // Low Byte Event0 Timeout
#define CC2500_20_WORCTRL 0x0020  // Wake On Radio Control
#define CC2500_21_FREND1 0x0021   // Front End RX Configuration
#define CC2500_22_FREND0 0x0022   // Front End TX configuration
#define CC2500_23_FSCAL3 0x0023   // Frequency Synthesizer Calibration
#define CC2500_24_FSCAL2 0x0024   // Frequency Synthesizer Calibration
#define CC2500_25_FSCAL1 0x0025   // Frequency Synthesizer Calibration
#define CC2500_26_FSCAL0 0x0026   // Frequency Synthesizer Calibration
#define CC2500_27_RCCTRL1 0x0027  // RC Oscillator Configuration
#define CC2500_28_RCCTRL0 0x0028  // RC Oscillator Configuration
#define CC2500_29_FSTEST 0x0029   // Frequency Synthesizer Calibration Control
#define CC2500_2A_PTEST 0x002A    // Production Test
#define CC2500_2B_AGCTEST 0x002B  // AGC Test
#define CC2500_2C_TEST2 0x002C    // Various Test Settings
#define CC2500_2D_TEST1 0x002D    // Various Test Settings
#define CC2500_2E_TEST0 0x002E    // Various Test Settings

#define CC2500_30_PARTNUM 0x0030        // Chip ID
#define CC2500_31_VERSION 0x0031        // Chip ID
#define CC2500_32_FREQEST 0x0032        // Frequency Offset Estimate from Demodulator
#define CC2500_33_LQI 0x0033            // Demodulator Estimate for Link Quality
#define CC2500_34_RSSI 0x0034           // Received Signal Strength Indication
#define CC2500_35_MARCSTATE 0x0035      // Main Radio Control State Machine State
#define CC2500_36_WORTIME1 0x0036       // High Byte of WOR Time
#define CC2500_37_WORTIME0 0x0037       // Low Byte of WOR Time
#define CC2500_38_PKTSTATUS 0x0038      // Current GDOxStatus and Packet Status
#define CC2500_39_VCO_VC_DAC 0x0039     // Current Setting from PLL Calibration Module
#define CC2500_3A_TXBYTES 0x003A        // Underflow and Number of Bytes
#define CC2500_3B_RXBYTES 0x003B        // Underflow and Number of Bytes
#define CC2500_3C_RCCTRL1_STATUS 0x003C // Last RC Oscillator Calibration Result
#define CC2500_3D_RCCTRL0_STATUS 0x003D // Last RC Oscillator Calibration Result

#define CC2500_3E_PATABLE 0x003E
#define CC2500_TXFIFO 0x003F
#define CC2500_RXFIFO 0x003F

#define Val_00_IOCFG2 0x29
#define Val_01_IOCFG1 0x2E
#define Val_02_IOCFG0 0x06
#define Val_03_FIFOTHR 0x07
#define Val_04_SYNC1 0xC3
#define Val_05_SYNC0 0x68
#define Val_06_PKTLEN 0x10
#define Val_07_PKTCTRL1 0x04
#define Val_08_PKTCTRL0 0x05
#define Val_09_ADDR 0x00
#define Val_0A_CHANNR 0x00
#define Val_0B_FSCTRL1 0x07
#define Val_0C_FSCTRL0 0x00
#define Val_0D_FREQ2 0x5C
#define Val_0E_FREQ1 0xCE
#define Val_0F_FREQ0 0xC4
#define Val_10_MDMCFG4 0x1D
#define Val_11_MDMCFG3 0x3B
#define Val_12_MDMCFG2 0x73
#define Val_13_MDMCFG1 0x22
#define Val_14_MDMCFG0 0xF8
#define Val_15_DEVIATN 0x00
#define Val_16_MCSM2 0x07
#define Val_17_MCSM1 0x30
#define Val_18_MCSM0 0x08
#define Val_19_FOCCFG 0x1D
#define Val_1A_BSCFG 0x1C
#define Val_1B_AGCCTRL2 0xC7
#define Val_1C_AGCCTRL1 0x00
#define Val_1D_AGCCTRL0 0xB2
#define Val_1E_WOREVT1 0x87
#define Val_1F_WOREVT0 0x6B
#define Val_20_WORCTRL 0xF8
#define Val_21_FREND1 0xB6
#define Val_22_FREND0 0x10
#define Val_23_FSCAL3 0xEA
#define Val_24_FSCAL2 0x0A
#define Val_25_FSCAL1 0x00
#define Val_26_FSCAL0 0x11
#define Val_27_RCCTRL1 0x41
#define Val_28_RCCTRL0 0x00
#define Val_29_FSTEST 0x59
#define Val_2A_PTEST 0x7F
#define Val_2B_AGCTEST 0x3F
#define Val_2C_TEST2 0x88
#define Val_2D_TEST1 0x31
#define Val_2E_TEST0 0x0B

#define Val_30_PARTNUM 0x80
#define Val_31_VERSION 0x03
#define Val_32_FREQEST 0x00
#define Val_33_LQI 0x00
#define Val_34_RSSI 0x00
#define Val_35_MARCSTATE 0x00
#define Val_36_WORTIME1 0x00
#define Val_37_WORTIME0 0x00
#define Val_38_PKTSTATUS 0x00
#define Val_39_VCO_VC_DAC 0x00
#define Val_3A_TXBYTES 0x00
#define Val_3B_RXBYTES 0x00
#define Val_3C_RCCTRL1_STATUS 0x00
#define Val_3D_RCCTRL0_STATUS 0x00

// Definitions for burst/single access to registers
#define Access_Write_Single 0x00
#define Access_Write_Burst 0x40
#define Access_Read_Single 0x80
#define Access_Read_Burst 0xC0

// Strobe commands
#define Cmd_SRES 0x30    // Reset chip
#define Cmd_SFSTXON 0x31 // Enable and calibrate frequency synthesizer (if MCSM0.FS_AUTOCAL=1)
// If in RX/TX: Go to a wait state where only the synthesizer is running (for quick RX / TX turnaround)
#define Cmd_SXOFF 0x32   // Turn off crystal oscillator
#define Cmd_SCAL 0x33    // Calibrate frequency synthesizer and turn it off(enables quick start)
#define Cmd_SRX 0x34     // Enable RX, Perform calibration first if coming from IDLE and  MCSM0.FS_AUTOCAL=1
#define Cmd_STX 0x35     // In IDLE state: Enable TX, Perform calibration first if
// MCSM0.FS_AUTOCAL=1, If in RX state and CCA is enabled:
// Only go to TX if channel is clear
#define Cmd_SIDLE 0x36   // Exit RX / TX, turn off frequency synthesizer and exit
#define Cmd_SAFC 0x37    // Perform AFC adjustment of the frequency synthesizer
#define Cmd_SWOR 0x38    // Start automatic RX polling sequence (Wake-on-Radio)
#define Cmd_SPWD 0x39    // Enter power down mode when CSn goes high
#define Cmd_SFRX 0x3A    // Flush the RX FIFO buffer
#define Cmd_SFTX 0x3B    // Flush the TX FIFO buffer
#define Cmd_SWORRST 0x3C // Reset real time clock
#define Cmd_SNOP 0x3D    // No operation

typedef enum {
    gpio_exti,
    gpio_input,
    gpio_output
} RFGPIOEnum;

typedef enum {
    RF_stby_mode,
    RF_rx_mode,
    RF_master_rx,
    RF_sleep_mode
} RFModeEnum;

typedef struct {
    uint8_t IRQCnt;
    uint8_t data_pack_len;
    uint8_t mode;
    // uint8_t LQIValue;
    // uint8_t channel;
    // 更改channel
    uint8_t last_channel;
    // uint8_t id_num;
    uint8_t last_id_num;
    uint8_t last_switch;
    uint16_t idle_time;
    uint16_t reset_time;
    uint8_t RSSIScanCnt;
    uint16_t RSSIPercent;

    uint8_t rise_init_time;

    uint8_t tx_data_buf[20];
    uint8_t rx_data_buf[20];
    uint8_t scan_ch[CH_MAX];

    bool access_scan_ch;
} RFModuleStruct;
extern RFModuleStruct RF;

void cc2500_scan_status();

void cc2500_init_pgr(void);

void cc2500_send_cmd(uint8_t cmd);

void cc2500_set_channel(uint8_t ch);

void cc2500_set_id(uint8_t id);

void cc2500_set_power(uint8_t power);

void cc2500_set_mode(uint8_t mode);

void cc2500_spi_transmit_receive(uint8_t *tx_data, uint8_t *rx_data, uint8_t len);

void cc2500_flush_rx_buffer();

void cc2500_scan_ch_rssi();

void cc2500_transmit_data_pgr(uint8_t size);

void cc2500_write_reg_arr(uint8_t reg, uint8_t *buf, uint8_t buf_size);

void pa_tx_on();

void pa_tx_off();

#endif //QZ_N_RF_MODULE_H
