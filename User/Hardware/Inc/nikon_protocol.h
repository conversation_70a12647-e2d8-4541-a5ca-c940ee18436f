//
// Created by <PERSON><PERSON> on 2024/10/23.
//

#ifndef QZ_N_NIKON_PROTOCOL_H
#define QZ_N_NIKON_PROTOCOL_H

#include "main.h"
#include "stdbool.h"

// V1N:56us 142us MK910:48us 129us
#define PreFlash1 60
#define PreFlash2 100

#define Nikon_SCK_Read HAL_GPIO_ReadPin(NIKON_SCK_GPIO_Port, NIKON_SCK_Pin)
#define Nikon_SDA_Read HAL_GPIO_ReadPin(NIKON_MOSI_GPIO_Port, NIKON_MOSI_Pin)
#define Nikon_SCS_Read HAL_GPIO_ReadPin(NIKON_SCS_GPIO_Port, NIKON_SCS_Pin)

#define Nikon_SCS_WriteH HAL_GPIO_WritePin(NIKON_SCS_GPIO_Port, NIKON_SCS_Pin, GPIO_PIN_SET)
#define Nikon_SCS_WriteL HAL_GPIO_WritePin(NIKON_SCS_GPIO_Port, NIKON_SCS_Pin, GPIO_PIN_RESET)

#define Nikon_SDA_WriteH HAL_GPIO_WritePin(NIKON_MOSI_GPIO_Port, NIKON_MOSI_Pin, GPIO_PIN_SET)
#define Nikon_SDA_WriteL HAL_GPIO_WritePin(NIKON_MOSI_GPIO_Port, NIKON_MOSI_Pin, GPIO_PIN_RESET)

#define Nikon_SCK_WriteH HAL_GPIO_WritePin(NIKON_SCK_GPIO_Port, NIKON_SCK_Pin, GPIO_PIN_SET)
#define Nikon_SCK_WriteL HAL_GPIO_WritePin(NIKON_SCK_GPIO_Port, NIKON_SCK_Pin, GPIO_PIN_RESET)

// #define FOCUS_LED_WriteH gpio_bit_set(FOCUS_LED_GPIO_Port, FOCUS_LED_Pin)
// #define FOCUS_LED_WriteL gpio_bit_reset(FOCUS_LED_GPIO_Port, FOCUS_LED_Pin)

extern const uint8_t TTLFlash_EV_Table[];

typedef enum {
    OFF_Mode,
    TTL_Mode,
    MUL_Mode,
    RPT_Mode,
    MS1_Mode,
    MS2_Mode
} NormalModeEnum;

typedef union {
    uint16_t All;
    struct {
        uint16_t NoPref: 1;
        uint16_t ISOAuto: 1;
        uint16_t FlashOFF: 1;
        uint16_t RearSync: 1;
        // u16 CameraFP : 1;
        uint16_t CameraBL: 1;
        uint16_t CameraData: 1;
        uint16_t CameraLink: 1;
    } bit;
} NormalFlagStructType;
extern NormalFlagStructType NormalFlag;

typedef struct {
    uint8_t MttlLV;

    uint8_t MmulLV;

    uint8_t APISO_TxCnt;
    uint8_t PreFlash_TxCnt;
} MasterRamStructType;
extern MasterRamStructType MasterRam;

// typedef enum {
//     mOFF_Mode,
//     mGRP_Mode,
//     mRPT_Mode,
// } MasterPmodeTypeDef;

typedef union {
    uint16_t All;
    struct {
        uint16_t Mmode: 2;

        uint16_t MrptEn: 1;

        uint16_t ShuttBusy: 1;
    } bit;
} MasterFlagStructType;
extern MasterFlagStructType MasterFlag;

typedef struct {
    uint8_t shut_data[7];
    uint8_t rx_data[16];
    uint8_t A0_data[22];

    uint8_t iso_sensor;
    // uint8_t hss_flag;
    uint8_t TTL_flash_level;
    uint8_t hss_flash_level;
    uint8_t cs_flash_level;
    uint8_t pre_flash_times;
    NormalModeEnum Mode;

    uint8_t Aperture;
    uint8_t SwitchFunCnt;
    uint8_t StepMotor;
    uint8_t last_zoom_code;
    uint8_t last_zoom;
    // uint8_t APISO_TxCnt;

    bool has_shutter_flag;
    bool has_link_flag;
} NikonProtocolStruct;
extern NikonProtocolStruct Nikon;

void Nikon_SPI_TTL_Prog();

void Nikon_SPI_Flag_Prog();

void set_nikon_SCK_output();

void set_nikon_MOSI_EXTI_input();

void delay_us_sw(uint32_t count);

#endif //QZ_N_NIKON_PROTOCOL_H
