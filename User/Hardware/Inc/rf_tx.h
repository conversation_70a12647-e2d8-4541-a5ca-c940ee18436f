//
// Created by <PERSON><PERSON> on 2024/10/21.
//

#ifndef QZ_N_RF_TX_H
#define QZ_N_RF_TX_H

#include <stdbool.h>
#include "main.h"

typedef enum {
    normal_mode,
    multi_mode
} MainModeEnum;

typedef struct {
    uint8_t cmd_code;
    uint8_t pre_flash_count;
    MainModeEnum main_mode;
} RFTXStruct;
extern RFTXStruct RF_TX;

void rf_send_ISO_zoom_0x8E();

void rf_send_master_pilot();

void rf_send_pre_flash1(uint8_t tData);

void rf_send_pre_flash2();

void rf_send_0xD4();

void rf_send_rise_0x57();

void rf_send_0x5C_start();

void rf_send_0x5C_end();

void rf_send_beep_lamp_0x8A();

void rf_send_mode_level_0x8B();

void rf_send_zoom_0x5A();

void rf_send_Multi_param_0x8D();

void rf_send_shutter_hold_0x59();

void rf_send_pv_0x6A();

void rf_send_ch_id_0xED_0x01();

void rf_send_tcm_0xED_0x02();

void rf_send_from_ui();

#endif //QZ_N_RF_TX_H
