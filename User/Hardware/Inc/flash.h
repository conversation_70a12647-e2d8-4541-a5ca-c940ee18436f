//
// Created by <PERSON><PERSON> on 2024/12/3.
//

#ifndef QZ_N_FLASH_H
#define QZ_N_FLASH_H

#include "main.h"

typedef enum {
    pilot_key_event = 0x01,
    rf_rx_event = 0x02,
    shutter_event = 0x04,
    rf_tx_event = 0x08,
} TriggerEventEnumType;

typedef struct {
    uint8_t flash_level;
    uint8_t times;
    uint8_t freq;

    uint8_t event;
    uint16_t RatesMs;
    // 闪光间隔时长
    uint16_t RatesCnt;

    uint8_t wireless_data_time;
    uint8_t send_0x59_countdown;
} MultiStructType;
extern MultiStructType Multi;

void flash_multi_mode_int();

void flash_multi_mode_event();

void flash_multi_mode(uint8_t event, uint8_t flash_level, uint8_t times, uint8_t freq);

#endif //QZ_N_FLASH_H
