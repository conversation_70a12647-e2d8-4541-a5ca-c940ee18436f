//
// Created by <PERSON><PERSON> on 2025/4/18.
//

#ifndef QZ_N_BT_MODULE_H
#define QZ_N_BT_MODULE_H
#define BT_O
#ifdef BT_ON

#include "main.h"

#define BT_Buffer_Size 25
typedef struct {
    // 串口DMA接收存储数组
    uint8_t bt_rx_buffer[BT_Buffer_Size];
    // 最终完整指令存储数组
    // 避免蓝牙发送间隔时间过长导致误空闲中断，进而导致bt_rx_buffer接到的数据不正确
    uint8_t bt_final_buffer[BT_Buffer_Size];
    // 已接收到完整命令flag
    uint8_t bt_at_finished;
    // 串口DMA发送存储数组
    uint8_t bt_tx_buffer[BT_Buffer_Size];
    // 有数据等待发送
    uint8_t tx_stand_by;
} UartStructType;
extern UartStructType Uart;

typedef struct {
    // 模块初始化完成标志
    uint8_t init_finished;
    // 停止广播完成标志
    uint8_t stop_broadcasting_finished;
    // 波特率调整完成标志
    uint8_t set_baud_finished;
    // 设备名修改完成标志
    uint8_t set_name_finished;
    // 开始广播完成标志
    uint8_t start_broadcasting_finished;
    // AT命令超时，收到数据清零
    uint8_t timeout;
    // 进入闪光程序，用于打断频闪闪光
    uint8_t flash;

    // 发送数据标志
    uint8_t send_data;
    // 当前数据编号
    uint8_t data_num;
} BluetoothStructType;
extern BluetoothStructType Bluetooth;

void bt_receive_data();

void bt_send_cmd(uint8_t *str, uint8_t len);

#endif
#endif //QZ_N_BT_MODULE_H
