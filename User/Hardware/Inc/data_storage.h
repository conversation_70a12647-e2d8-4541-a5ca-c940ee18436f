//
// Created by <PERSON><PERSON> on 2024/12/26.
//

#ifndef QS600_DATA_STORAGE_H
#define QS600_DATA_STORAGE_H

#include "main.h"

// 配置参数
#define FLASH_WRITE_ARR_MAX 20
#define Addr_Flash_Sector_17    0x8022000U
#define Addr_Flash_Sector_18    0x8024000U
// 数据存储起始地址
#define STORAGE_START_ADDR  Addr_Flash_Sector_17
// 数据存储结束地址
#define STORAGE_END_ADDR (Addr_Flash_Sector_18 - 1)
// SECTOR数量
#define SECTOR_NUM     1
// 数据分区大小
/*!< Flash Sector Size: 8 KB */
#define DATA_PTN_SIZE   (FLASH_SECTOR_SIZE * SECTOR_NUM)
// 最大变量数量
#define VAR_MAX_NUM   66
// 魔数
#define MAGIC_NUM   0x00250428

typedef struct {
    uint8_t bits;
    void *ptr;
    uint8_t min;
    uint8_t max;
    uint8_t default_var;
} DataStructType;

typedef struct {
    DataStructType Data[VAR_MAX_NUM];
    uint32_t last_crc32;
} StorageStructType;

void storage_save_data();

void storage_read_data();

void storage_erase_page(uint32_t start_addr, uint32_t end_addr);

void fill_default_data();

#endif //QS600_DATA_STORAGE_H
