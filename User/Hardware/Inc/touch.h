//
// Created by <PERSON><PERSON> on 2024/8/19.
//

#ifndef QZ_N_TOUCH_H
#define QZ_N_TOUCH_H

#include "main.h"

#define CST820_IIC_ADDR            (0X15 << 1)

#define CST820_CMD_GET_FINGERNUM       0X02
#define CST820_CMD_SET_AUTO_SLEEP      0XFE
#define CST820_CMD_SLEEP               0XE5

typedef struct {
    uint16_t x;
    uint16_t last_x;
    uint16_t y;
    uint16_t last_y;
    bool is_pressed;
} TouchStructType;
extern TouchStructType Touch;

uint8_t cst820_read_finger_num(void);

void cst820_point_scan();

uint8_t cst820_init(void);

void cst820_is_pressed(void);

void cst820_sleep(void);

void cst820_wakeup(void);

#endif //QZ_N_TOUCH_H
