//
// Created by <PERSON><PERSON> on 2024/7/7.
//

#ifndef H5_AMOLED_AMOLED_QSPI_SW_H
#define H5_AMOLED_AMOLED_QSPI_SW_H

#if 0

#include "main.h"
#include "system.h"

#define AMOLED_RST_WriteH HAL_GPIO_WritePin(AMOLED_RST_GPIO_Port, AMOLED_RST_Pin, GPIO_PIN_SET)
#define AMOLED_RST_WriteL HAL_GPIO_WritePin(AMOLED_RST_GPIO_Port, AMOLED_RST_Pin, GPIO_PIN_RESET)

#define QSPI_CS_WriteH HAL_GPIO_WritePin(QSPI_CS_GPIO_Port, QSPI_CS_Pin, GPIO_PIN_SET)
#define QSPI_CS_WriteL HAL_GPIO_WritePin(QSPI_CS_GPIO_Port, QSPI_CS_Pin, GPIO_PIN_RESET)
#define QSPI_CLK_WriteH HAL_GPIO_WritePin(QSPI_CLK_GPIO_Port, QSPI_CLK_Pin, GPIO_PIN_SET)
#define QSPI_CLK_WriteL HAL_GPIO_WritePin(QSPI_CLK_GPIO_Port, QSPI_CLK_Pin, GPIO_PIN_RESET)
#define QSPI_IO0_WriteH HAL_GPIO_WritePin(QSPI_IO0_GPIO_Port, QSPI_IO0_Pin, GPIO_PIN_SET)
#define QSPI_IO0_WriteL HAL_GPIO_WritePin(QSPI_IO0_GPIO_Port, QSPI_IO0_Pin, GPIO_PIN_RESET)
#define QSPI_IO1_WriteH HAL_GPIO_WritePin(QSPI_IO1_GPIO_Port, QSPI_IO1_Pin, GPIO_PIN_SET)
#define QSPI_IO1_WriteL HAL_GPIO_WritePin(QSPI_IO1_GPIO_Port, QSPI_IO1_Pin, GPIO_PIN_RESET)
#define QSPI_IO2_WriteH HAL_GPIO_WritePin(QSPI_IO2_GPIO_Port, QSPI_IO2_Pin, GPIO_PIN_SET)
#define QSPI_IO2_WriteL HAL_GPIO_WritePin(QSPI_IO2_GPIO_Port, QSPI_IO2_Pin, GPIO_PIN_RESET)
#define QSPI_IO3_WriteH HAL_GPIO_WritePin(QSPI_IO3_GPIO_Port, QSPI_IO3_Pin, GPIO_PIN_SET)
#define QSPI_IO3_WriteL HAL_GPIO_WritePin(QSPI_IO3_GPIO_Port, QSPI_IO3_Pin, GPIO_PIN_RESET)


#define Test_2_WriteH HAL_GPIO_WritePin(TEST_2_GPIO_Port, TEST_2_Pin, GPIO_PIN_SET)
#define Test_2_WriteL HAL_GPIO_WritePin(TEST_2_GPIO_Port, TEST_2_Pin, GPIO_PIN_RESET)

#define RGB565(R, G, B)    (((R >> 3) << 11) | ((G >> 2) << 5) | (B >> 3))

#define RGB RGB565
enum {
    WHITE = RGB(255, 255, 255), /*  */
    BLACK = RGB(0, 0, 0), /*  */
    RED = RGB(255, 0, 0), /*  */
    GREEN = RGB(0, 255, 0), /*  */
    BLUE = RGB(0, 0, 255), /*  */
    YELLOW = RGB(255, 255, 0), /*  */

    GREY = RGB(98, 98, 98), /*  */
    GREY1 = RGB(150, 150, 150), /*  */
    GREY2 = RGB(180, 180, 180), /*  */
    GREY3 = RGB(200, 200, 200), /*  */
    GREY4 = RGB(230, 230, 230), /*  */

    BUTTON_GREY = RGB(220, 220, 220), /*  */

    BLUE1 = RGB(0, 0, 240), /*  */
    BLUE2 = RGB(0, 0, 128), /*  */
    BLUE3 = RGB(68, 68, 255), /*  */
    BLUE4 = RGB(0, 64, 128), /*  */

    BTN_FACE = RGB(236, 233, 216), /*  */
    BTN_FONT = BLACK, /*  */
    BOX_BORDER1 = RGB(172, 168, 153), /*  */
    BOX_BORDER2 = RGB(255, 255, 255), /*  */
};

void amoled_init();

void amoled_draw_point(int x, int y, long int color);

void amoled_fill_color(int xsta, int ysta, int xend, int yend, long int color);

void AMOLED_Clear(unsigned int color);

void DispColor(unsigned int color);

#endif

#endif //H5_AMOLED_AMOLED_QSPI_SW_H
