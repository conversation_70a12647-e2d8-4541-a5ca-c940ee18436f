//
// Created by <PERSON><PERSON> on 2024/10/27.
//

#ifndef QZ_N_SCAN_KEY_H
#define QZ_N_SCAN_KEY_H

#include <stdbool.h>
#include "main.h"

#define Key_Flash_Read HAL_GPIO_ReadPin(KEY_FLASH_GPIO_Port, KEY_FLASH_Pin)
#define Key_Mode_Read HAL_GPIO_ReadPin(KEY_ENTER_GPIO_Port, KEY_MODE_Pin)
#define Key_Enter_Read HAL_GPIO_ReadPin(KEY_ENTER_GPIO_Port, KEY_ENTER_Pin)

#define encoder_a_key_read HAL_GPIO_ReadPin(ENCODE_L_GPIO_Port, ENCODE_L_Pin)
#define encoder_b_key_read HAL_GPIO_ReadPin(ENCODE_R_GPIO_Port, ENCODE_R_Pin)

#define Key_Long_Press_Time 3

typedef enum {
    // 松手触发一次
    KEY_RELEASED,
    // 按下触发一次
    KEY_PRESSED,
    // 按下时每 SCAN_KEY_TIME_SLICES 触发一次
    KEY_PRESSING,
    // 长按触发一次
    KEY_LONG_PRESSED,
    // 松手触发一次
    KEY_CLICKED
} KeyEventEnum;

typedef enum {
    KEY_FLASH,
    KEY_ENTER,
    KEY_MODE,
    KEY_MAIN_SWITCH,
    KeyItemCount
} KeyEnum;

// 时间片 ms
#define SCAN_KEY_TIME_SLICES 1
// 按下防抖 30ms
#define PRESSED_TIME_LIMIT (30 / SCAN_KEY_TIME_SLICES)
// 长按 1000ms
#define LONG_PRESSED_TIME_LIMIT (800 / SCAN_KEY_TIME_SLICES)
// 点击 400ms
#define CLICKED_TIME_LIMIT (400 / SCAN_KEY_TIME_SLICES)

typedef struct {
    // 必传* id标识
    KeyEnum key_id;
    //必传* 是否有效按压，里边要读io口，如果有些情况不允许按也可以在里面加条件
    // bool (*is_Valid_Press)(KeyEnum key_id);

    // 按键事件
    // 松手只触发一次，可以通过lastPressTime拿到前一次按压了多久
    void (*key_event_prg)(KeyEventEnum key_event);

    // 是否按压着，一个标识
    bool is_pressing;
    bool long_pr_sent;
    // 单位10ms
    uint16_t cur_press_time;
    uint16_t released_time;
    // 松开后，上次按压的时间,单位10ms，如为5则表示按了50ms
    uint16_t last_press_time;
    KeyEventEnum key_event;
} KeyStructType;

typedef struct {
    // 按键列表
    KeyStructType buttons[KeyItemCount];
    // 每10ms扫描按键前调用，返回值为true则继续下边的扫描，为false中止后续扫描
    bool (*before_scan_pgr)(void);

    // 每10ms扫描完所有按键后调用，比如可以看是否有按键按了然后解除睡眠等等
    void (*after_scan_pgr)(void);
} ScanKeyStructType;
extern ScanKeyStructType ScanKey;

void scan_key_pgr();

void key_event_get();

void key_flash_event(KeyEventEnum key_event);

#endif //QZ_N_SCAN_KEY_H
