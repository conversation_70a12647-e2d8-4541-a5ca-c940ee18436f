//
// Created by <PERSON><PERSON> on 2024/11/22.
//

#ifndef QZ_N_ENCODER_H
#define QZ_N_ENCODER_H

#include "main.h"
#include "stdbool.h"

typedef struct {
    // -1左旋，0不旋，1右旋
    int8_t encoder_flag;

    uint8_t encoder_add_times;
    uint8_t encoder_sub_times;
    int32_t encoder_times;
    uint16_t flag_add_times;
    uint16_t flag_sub_times;

    // 上次输入设备是否不为编码器
    bool defocused_flag;
} EncoderStruct;
extern EncoderStruct Encoder;

#define ENCODER_A_READ HAL_GPIO_ReadPin(ENCODE_A_GPIO_Port, ENCODE_A_Pin)
#define ENCODER_B_READ HAL_GPIO_ReadPin(ENCODE_B_GPIO_Port, ENCODE_B_Pin)

void scan_encoder_pgr();

void encoder_set_flag();

int16_t encoder_value_read();

#endif //QZ_N_ENCODER_H
