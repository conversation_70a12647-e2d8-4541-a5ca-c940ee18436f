/*******************************************************************************
 * Size: 32 px
 * Bpp: 4
 * Opts: --bpp 4 --size 32 --no-compress --font SourceHanSansCN-Bold.ttf --range 32-127,20851,20998,21069,21160,21516,21518,24088,27493,28966,31186,33258,35328,35821,36317,36895,38047,38381,39640 --format lvgl -o font_source_han_sans_bold_32.c
 ******************************************************************************/

#ifdef LV_LVGL_H_INCLUDE_SIMPLE
#include "lvgl.h"
#else
#include "lvgl.h"
#endif

#ifndef FONT_SOURCE_HAN_SANS_BOLD_32
#define FONT_SOURCE_HAN_SANS_BOLD_32 1
#endif

#if FONT_SOURCE_HAN_SANS_BOLD_32

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+0020 " " */

    /* U+0021 "!" */
    0x5d, 0xdd, 0xd3, 0x6f, 0xff, 0xf3, 0x5f, 0xff,
    0xf3, 0x5f, 0xff, 0xf2, 0x4f, 0xff, 0xf2, 0x3f,
    0xff, 0xf1, 0x3f, 0xff, 0xf0, 0x2f, 0xff, 0xf0,
    0x1f, 0xff, 0xe0, 0xf, 0xff, 0xd0, 0xf, 0xff,
    0xc0, 0xe, 0xff, 0xc0, 0xd, 0xff, 0xb0, 0xc,
    0xff, 0xa0, 0xc, 0xff, 0x90, 0xb, 0xff, 0x80,
    0xa, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x43, 0x0, 0x2d, 0xff, 0xc0, 0xbf,
    0xff, 0xf8, 0xef, 0xff, 0xfc, 0xdf, 0xff, 0xfb,
    0x8f, 0xff, 0xf5, 0x8, 0xee, 0x70,

    /* U+0022 "\"" */
    0x2b, 0xbb, 0xb6, 0x0, 0x2b, 0xbb, 0xb6, 0x2f,
    0xff, 0xf8, 0x0, 0x2f, 0xff, 0xf8, 0x2f, 0xff,
    0xf8, 0x0, 0x2f, 0xff, 0xf8, 0x1f, 0xff, 0xf7,
    0x0, 0x1f, 0xff, 0xf7, 0x1f, 0xff, 0xf6, 0x0,
    0x1f, 0xff, 0xf6, 0xf, 0xff, 0xf5, 0x0, 0xf,
    0xff, 0xf5, 0xd, 0xff, 0xf3, 0x0, 0xd, 0xff,
    0xf3, 0xb, 0xff, 0xf1, 0x0, 0xb, 0xff, 0xf1,
    0x9, 0xff, 0xf0, 0x0, 0x9, 0xff, 0xf0, 0x7,
    0xff, 0xd0, 0x0, 0x7, 0xff, 0xd0, 0x5, 0xff,
    0xb0, 0x0, 0x5, 0xff, 0xb0, 0x3, 0xff, 0x90,
    0x0, 0x3, 0xff, 0x90,

    /* U+0023 "#" */
    0x0, 0x0, 0xf, 0xfb, 0x0, 0x2, 0xff, 0x90,
    0x0, 0x0, 0x1, 0xff, 0xa0, 0x0, 0x4f, 0xf7,
    0x0, 0x0, 0x0, 0x3f, 0xf8, 0x0, 0x6, 0xff,
    0x60, 0x0, 0x0, 0x5, 0xff, 0x60, 0x0, 0x8f,
    0xf4, 0x0, 0x0, 0x0, 0x7f, 0xf5, 0x0, 0x9,
    0xff, 0x20, 0x0, 0x0, 0x8, 0xff, 0x30, 0x0,
    0xbf, 0xf0, 0x0, 0x0, 0x0, 0xaf, 0xf1, 0x0,
    0xd, 0xff, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfc, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xc0, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0x2, 0x24, 0xff,
    0xb2, 0x22, 0x6f, 0xf8, 0x22, 0x10, 0x0, 0x4f,
    0xf8, 0x0, 0x7, 0xff, 0x50, 0x0, 0x0, 0x6,
    0xff, 0x60, 0x0, 0x8f, 0xf4, 0x0, 0x0, 0x0,
    0x7f, 0xf4, 0x0, 0xa, 0xff, 0x20, 0x0, 0x23,
    0x3a, 0xff, 0x53, 0x33, 0xdf, 0xf3, 0x33, 0xc,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1,
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x1c, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf1, 0x0, 0xf, 0xfb, 0x0, 0x4, 0xff, 0x80,
    0x0, 0x0, 0x2, 0xff, 0x90, 0x0, 0x5f, 0xf6,
    0x0, 0x0, 0x0, 0x4f, 0xf7, 0x0, 0x7, 0xff,
    0x40, 0x0, 0x0, 0x6, 0xff, 0x50, 0x0, 0x9f,
    0xf3, 0x0, 0x0, 0x0, 0x8f, 0xf3, 0x0, 0xb,
    0xff, 0x10, 0x0, 0x0, 0xa, 0xff, 0x20, 0x0,
    0xdf, 0xf0, 0x0, 0x0, 0x0, 0xcf, 0xf0, 0x0,
    0xf, 0xfd, 0x0, 0x0, 0x0,

    /* U+0024 "$" */
    0x0, 0x0, 0x0, 0x2f, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2f, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2f, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2f, 0xff, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x3b, 0xff, 0xff, 0xfd, 0x60, 0x0,
    0x0, 0x7, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x20,
    0x0, 0x5f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe1,
    0x0, 0xef, 0xff, 0xfe, 0xbb, 0xef, 0xff, 0xb0,
    0x4, 0xff, 0xff, 0x90, 0x0, 0x6, 0xfd, 0x0,
    0x7, 0xff, 0xff, 0x10, 0x0, 0x0, 0x21, 0x0,
    0x8, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xff, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xff, 0xff, 0xe4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0xff, 0xff, 0xa2, 0x0, 0x0, 0x0,
    0x0, 0x2e, 0xff, 0xff, 0xff, 0xa1, 0x0, 0x0,
    0x0, 0x2, 0xcf, 0xff, 0xff, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x6, 0xff, 0xff, 0xff, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0x19, 0xff, 0xff, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x2c, 0xff, 0xff, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0xfa,
    0x0, 0x83, 0x0, 0x0, 0x0, 0xf, 0xff, 0xf8,
    0x4, 0xff, 0x91, 0x0, 0x0, 0x9f, 0xff, 0xf5,
    0xd, 0xff, 0xff, 0xca, 0xae, 0xff, 0xff, 0xe0,
    0x3f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x50,
    0x3, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x0,
    0x0, 0x5, 0xbf, 0xff, 0xff, 0xfb, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xff, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2f, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2f, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2f, 0xff, 0x0, 0x0, 0x0,

    /* U+0025 "%" */
    0x0, 0x7, 0xdf, 0xeb, 0x40, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x1d,
    0xff, 0xff, 0xff, 0x70, 0x0, 0x0, 0x0, 0x5,
    0xff, 0x70, 0x0, 0x0, 0x0, 0xb, 0xff, 0xfc,
    0xef, 0xff, 0x40, 0x0, 0x0, 0x0, 0xdf, 0xe0,
    0x0, 0x0, 0x0, 0x4, 0xff, 0xf5, 0x0, 0xbf,
    0xfd, 0x0, 0x0, 0x0, 0x5f, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xfb, 0x0, 0x2, 0xff, 0xf3,
    0x0, 0x0, 0xd, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xff, 0x70, 0x0, 0xe, 0xff, 0x60, 0x0,
    0x6, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0xef,
    0xf5, 0x0, 0x0, 0xcf, 0xf8, 0x0, 0x0, 0xef,
    0xd0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0x50,
    0x0, 0xc, 0xff, 0x80, 0x0, 0x6f, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xef, 0xf5, 0x0, 0x0,
    0xcf, 0xf8, 0x0, 0xe, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xff, 0x70, 0x0, 0xe, 0xff,
    0x60, 0x7, 0xff, 0x50, 0x2, 0x8b, 0xb8, 0x20,
    0x0, 0x9f, 0xfb, 0x0, 0x2, 0xff, 0xf2, 0x0,
    0xef, 0xd0, 0x5, 0xff, 0xff, 0xff, 0x60, 0x3,
    0xff, 0xf5, 0x0, 0xbf, 0xfd, 0x0, 0x7f, 0xf5,
    0x4, 0xff, 0xff, 0xff, 0xff, 0x40, 0xb, 0xff,
    0xfc, 0xef, 0xff, 0x40, 0x1e, 0xfc, 0x0, 0xdf,
    0xfc, 0x11, 0xbf, 0xfd, 0x0, 0x1d, 0xff, 0xff,
    0xff, 0x70, 0x8, 0xff, 0x40, 0x4f, 0xff, 0x10,
    0x1, 0xff, 0xf4, 0x0, 0x7, 0xdf, 0xeb, 0x40,
    0x1, 0xff, 0xc0, 0x8, 0xff, 0xc0, 0x0, 0xc,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0xf4, 0x0, 0xaf, 0xfa, 0x0, 0x0, 0x9f, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xfb, 0x0,
    0xb, 0xff, 0x90, 0x0, 0x8, 0xff, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xff, 0x30, 0x0, 0xbf,
    0xf9, 0x0, 0x0, 0x8f, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xff, 0xb0, 0x0, 0xa, 0xff, 0xa0,
    0x0, 0x9, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xf3, 0x0, 0x0, 0x8f, 0xfc, 0x0, 0x0,
    0xbf, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xfb,
    0x0, 0x0, 0x4, 0xff, 0xf1, 0x0, 0xf, 0xff,
    0x50, 0x0, 0x0, 0x0, 0xa, 0xff, 0x20, 0x0,
    0x0, 0xe, 0xff, 0xa0, 0x9, 0xff, 0xe0, 0x0,
    0x0, 0x0, 0x2, 0xff, 0xa0, 0x0, 0x0, 0x0,
    0x6f, 0xff, 0xee, 0xff, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xff, 0xff, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5c, 0xff,
    0xc5, 0x0, 0x0,

    /* U+0026 "&" */
    0x0, 0x0, 0x0, 0x7, 0xce, 0xfd, 0x81, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xdf,
    0xff, 0xff, 0xfe, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xd, 0xff, 0xff, 0xff, 0xff, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff,
    0xe6, 0x5c, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xdf, 0xff, 0x40, 0x3, 0xff, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xfe,
    0x0, 0x1, 0xff, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xfd, 0x0, 0x4, 0xff, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xff,
    0x0, 0xd, 0xff, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xff, 0x42, 0xdf, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff,
    0xdf, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xe, 0xff, 0xff, 0xff, 0xa0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1d, 0xff,
    0xff, 0xf7, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xa0,
    0x0, 0x3, 0xef, 0xff, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0xef, 0xff, 0x50, 0x0, 0x3f, 0xff, 0xff,
    0xff, 0xf9, 0x0, 0x0, 0x4, 0xff, 0xff, 0x0,
    0x1, 0xef, 0xff, 0xfb, 0xff, 0xff, 0x90, 0x0,
    0xb, 0xff, 0xf9, 0x0, 0x8, 0xff, 0xff, 0x60,
    0xbf, 0xff, 0xfa, 0x0, 0x4f, 0xff, 0xf2, 0x0,
    0xd, 0xff, 0xfc, 0x0, 0xd, 0xff, 0xff, 0xc2,
    0xdf, 0xff, 0x90, 0x0, 0xf, 0xff, 0xf7, 0x0,
    0x2, 0xef, 0xff, 0xff, 0xff, 0xfe, 0x10, 0x0,
    0xf, 0xff, 0xf8, 0x0, 0x0, 0x1d, 0xff, 0xff,
    0xff, 0xf5, 0x0, 0x0, 0xe, 0xff, 0xfd, 0x0,
    0x0, 0x1, 0xcf, 0xff, 0xff, 0xf5, 0x0, 0x0,
    0x9, 0xff, 0xff, 0xb2, 0x0, 0x4, 0xcf, 0xff,
    0xff, 0xff, 0xd7, 0x20, 0x2, 0xff, 0xff, 0xff,
    0xec, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe0,
    0x0, 0x6f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0xcf, 0xff, 0xff, 0x90, 0x0, 0x5, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0x60, 0x5, 0xdf, 0xff, 0x50,
    0x0, 0x0, 0x16, 0xce, 0xfe, 0xda, 0x50, 0x0,
    0x0, 0x3, 0x9d, 0x0,

    /* U+0027 "'" */
    0x2b, 0xbb, 0xb6, 0x2f, 0xff, 0xf8, 0x2f, 0xff,
    0xf8, 0x1f, 0xff, 0xf7, 0x1f, 0xff, 0xf6, 0xf,
    0xff, 0xf5, 0xd, 0xff, 0xf3, 0xb, 0xff, 0xf1,
    0x9, 0xff, 0xf0, 0x7, 0xff, 0xd0, 0x5, 0xff,
    0xb0, 0x3, 0xff, 0x90,

    /* U+0028 "(" */
    0x0, 0x0, 0x4, 0x20, 0x0, 0x0, 0x1, 0xff,
    0xa2, 0x0, 0x0, 0x9f, 0xff, 0x10, 0x0, 0x1f,
    0xff, 0x80, 0x0, 0x9, 0xff, 0xf1, 0x0, 0x0,
    0xef, 0xfa, 0x0, 0x0, 0x5f, 0xff, 0x50, 0x0,
    0xb, 0xff, 0xf0, 0x0, 0x0, 0xff, 0xfb, 0x0,
    0x0, 0x4f, 0xff, 0x70, 0x0, 0x8, 0xff, 0xf4,
    0x0, 0x0, 0xbf, 0xff, 0x10, 0x0, 0xe, 0xff,
    0xe0, 0x0, 0x0, 0xff, 0xfc, 0x0, 0x0, 0x2f,
    0xff, 0xb0, 0x0, 0x3, 0xff, 0xfa, 0x0, 0x0,
    0x3f, 0xff, 0x90, 0x0, 0x4, 0xff, 0xf8, 0x0,
    0x0, 0x4f, 0xff, 0x90, 0x0, 0x3, 0xff, 0xfa,
    0x0, 0x0, 0x2f, 0xff, 0xb0, 0x0, 0x0, 0xff,
    0xfc, 0x0, 0x0, 0xf, 0xff, 0xe0, 0x0, 0x0,
    0xcf, 0xff, 0x10, 0x0, 0x9, 0xff, 0xf4, 0x0,
    0x0, 0x5f, 0xff, 0x60, 0x0, 0x1, 0xff, 0xfa,
    0x0, 0x0, 0xc, 0xff, 0xe0, 0x0, 0x0, 0x6f,
    0xff, 0x40, 0x0, 0x1, 0xff, 0xfa, 0x0, 0x0,
    0x9, 0xff, 0xf1, 0x0, 0x0, 0x2f, 0xff, 0x70,
    0x0, 0x0, 0xaf, 0xfe, 0x0, 0x0, 0x2, 0xff,
    0xc2, 0x0, 0x0, 0x6, 0x40, 0x0,

    /* U+0029 ")" */
    0x0, 0x15, 0x0, 0x0, 0x1, 0xaf, 0xf2, 0x0,
    0x0, 0xe, 0xff, 0xb0, 0x0, 0x0, 0x7f, 0xff,
    0x30, 0x0, 0x0, 0xff, 0xfa, 0x0, 0x0, 0x9,
    0xff, 0xf1, 0x0, 0x0, 0x3f, 0xff, 0x70, 0x0,
    0x0, 0xef, 0xfc, 0x0, 0x0, 0x9, 0xff, 0xf1,
    0x0, 0x0, 0x5f, 0xff, 0x60, 0x0, 0x3, 0xff,
    0xfa, 0x0, 0x0, 0xf, 0xff, 0xd0, 0x0, 0x0,
    0xdf, 0xff, 0x0, 0x0, 0xb, 0xff, 0xf2, 0x0,
    0x0, 0x9f, 0xff, 0x30, 0x0, 0x8, 0xff, 0xf4,
    0x0, 0x0, 0x8f, 0xff, 0x50, 0x0, 0x7, 0xff,
    0xf5, 0x0, 0x0, 0x7f, 0xff, 0x50, 0x0, 0x8,
    0xff, 0xf4, 0x0, 0x0, 0x9f, 0xff, 0x30, 0x0,
    0xa, 0xff, 0xf2, 0x0, 0x0, 0xdf, 0xff, 0x0,
    0x0, 0xf, 0xff, 0xd0, 0x0, 0x2, 0xff, 0xfa,
    0x0, 0x0, 0x5f, 0xff, 0x60, 0x0, 0x9, 0xff,
    0xf2, 0x0, 0x0, 0xdf, 0xfd, 0x0, 0x0, 0x2f,
    0xff, 0x80, 0x0, 0x8, 0xff, 0xf2, 0x0, 0x0,
    0xef, 0xfb, 0x0, 0x0, 0x6f, 0xff, 0x40, 0x0,
    0xd, 0xff, 0xc0, 0x0, 0x1, 0xcf, 0xf3, 0x0,
    0x0, 0x0, 0x37, 0x0, 0x0, 0x0,

    /* U+002A "*" */
    0x0, 0x0, 0x3, 0xff, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xff, 0x90, 0x0, 0x0, 0x6, 0x51, 0x8,
    0xff, 0xb0, 0x4, 0x70, 0xf, 0xff, 0xdd, 0xff,
    0xed, 0xff, 0xf3, 0x4f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf7, 0x4, 0xbf, 0xff, 0xff, 0xff, 0xfd,
    0x50, 0x0, 0x3, 0xdf, 0xff, 0xff, 0x40, 0x0,
    0x0, 0x2, 0xff, 0xff, 0xff, 0x40, 0x0, 0x0,
    0xa, 0xff, 0xfe, 0xff, 0xd0, 0x0, 0x0, 0x3f,
    0xff, 0x52, 0xff, 0xf7, 0x0, 0x0, 0x7f, 0xf7,
    0x0, 0x4f, 0xfa, 0x0, 0x0, 0x5, 0x90, 0x0,
    0x6, 0x70, 0x0,

    /* U+002B "+" */
    0x0, 0x0, 0x0, 0x28, 0x88, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xff, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xf2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff,
    0x20, 0x0, 0x0, 0x5, 0x77, 0x77, 0x7a, 0xff,
    0xf9, 0x77, 0x77, 0x74, 0xcf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xac, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfa, 0xcf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xa0, 0x0, 0x0,
    0x5, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xff, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xff, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5f, 0xff, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xff, 0xf2, 0x0, 0x0,
    0x0,

    /* U+002C "," */
    0x0, 0x29, 0xb8, 0x10, 0x2, 0xff, 0xff, 0xc0,
    0x9, 0xff, 0xff, 0xf4, 0xa, 0xff, 0xff, 0xf9,
    0x5, 0xff, 0xff, 0xfa, 0x0, 0x6d, 0xff, 0xfb,
    0x0, 0x0, 0x8f, 0xf9, 0x0, 0x0, 0xcf, 0xf6,
    0x0, 0x6, 0xff, 0xf1, 0x0, 0x7f, 0xff, 0x80,
    0x1d, 0xff, 0xfb, 0x0, 0xe, 0xff, 0xb0, 0x0,
    0x9, 0xd5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+002D "-" */
    0x38, 0x88, 0x88, 0x88, 0x82, 0x7f, 0xff, 0xff,
    0xff, 0xf4, 0x7f, 0xff, 0xff, 0xff, 0xf4, 0x7f,
    0xff, 0xff, 0xff, 0xf4,

    /* U+002E "." */
    0x0, 0x34, 0x10, 0x0, 0xbf, 0xfe, 0x30, 0x7f,
    0xff, 0xfd, 0xa, 0xff, 0xff, 0xf1, 0x9f, 0xff,
    0xff, 0x4, 0xff, 0xff, 0xa0, 0x5, 0xde, 0xa0,
    0x0,

    /* U+002F "/" */
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xf2, 0x0, 0x0, 0x0, 0x3,
    0xff, 0xe0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xa0,
    0x0, 0x0, 0x0, 0xa, 0xff, 0x60, 0x0, 0x0,
    0x0, 0xe, 0xff, 0x20, 0x0, 0x0, 0x0, 0x2f,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0xef, 0xf3, 0x0, 0x0, 0x0, 0x2, 0xff,
    0xf0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xb0, 0x0,
    0x0, 0x0, 0xa, 0xff, 0x70, 0x0, 0x0, 0x0,
    0xe, 0xff, 0x30, 0x0, 0x0, 0x0, 0x2f, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0xf3, 0x0, 0x0, 0x0, 0x1, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x5, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0x9, 0xff, 0x80, 0x0, 0x0, 0x0, 0xd,
    0xff, 0x40, 0x0, 0x0, 0x0, 0x1f, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x5f, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xf8, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0xf4, 0x0, 0x0, 0x0, 0x1, 0xff, 0xf0, 0x0,
    0x0, 0x0, 0x5, 0xff, 0xc0, 0x0, 0x0, 0x0,
    0x9, 0xff, 0x80, 0x0, 0x0, 0x0, 0xd, 0xff,
    0x40, 0x0, 0x0, 0x0, 0x1f, 0xff, 0x10, 0x0,
    0x0, 0x0, 0x4f, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x38, 0x85, 0x0, 0x0, 0x0, 0x0,

    /* U+0030 "0" */
    0x0, 0x0, 0x5, 0xbe, 0xfe, 0xa4, 0x0, 0x0,
    0x0, 0x0, 0x1c, 0xff, 0xff, 0xff, 0xfa, 0x0,
    0x0, 0x0, 0xd, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0x0, 0x0, 0xa, 0xff, 0xff, 0xec, 0xff, 0xff,
    0xf7, 0x0, 0x2, 0xff, 0xff, 0xa0, 0x1, 0xcf,
    0xff, 0xf1, 0x0, 0x9f, 0xff, 0xe0, 0x0, 0x1,
    0xff, 0xff, 0x70, 0xe, 0xff, 0xf8, 0x0, 0x0,
    0xa, 0xff, 0xfc, 0x2, 0xff, 0xff, 0x40, 0x0,
    0x0, 0x6f, 0xff, 0xf0, 0x4f, 0xff, 0xf1, 0x0,
    0x0, 0x3, 0xff, 0xff, 0x27, 0xff, 0xff, 0x0,
    0x0, 0x0, 0x2f, 0xff, 0xf5, 0x8f, 0xff, 0xe0,
    0x0, 0x0, 0x1, 0xff, 0xff, 0x68, 0xff, 0xfd,
    0x0, 0x0, 0x0, 0xf, 0xff, 0xf6, 0x9f, 0xff,
    0xd0, 0x0, 0x0, 0x0, 0xff, 0xff, 0x78, 0xff,
    0xfe, 0x0, 0x0, 0x0, 0xf, 0xff, 0xf6, 0x7f,
    0xff, 0xe0, 0x0, 0x0, 0x1, 0xff, 0xff, 0x56,
    0xff, 0xff, 0x0, 0x0, 0x0, 0x2f, 0xff, 0xf4,
    0x4f, 0xff, 0xf1, 0x0, 0x0, 0x4, 0xff, 0xff,
    0x21, 0xff, 0xff, 0x40, 0x0, 0x0, 0x7f, 0xff,
    0xf0, 0xd, 0xff, 0xf9, 0x0, 0x0, 0xb, 0xff,
    0xfb, 0x0, 0x8f, 0xff, 0xe1, 0x0, 0x2, 0xff,
    0xff, 0x60, 0x2, 0xff, 0xff, 0xb1, 0x2, 0xcf,
    0xff, 0xe0, 0x0, 0x8, 0xff, 0xff, 0xfd, 0xff,
    0xff, 0xf6, 0x0, 0x0, 0xc, 0xff, 0xff, 0xff,
    0xff, 0xfa, 0x0, 0x0, 0x0, 0x1b, 0xff, 0xff,
    0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x5, 0xbe,
    0xfe, 0xa4, 0x0, 0x0, 0x0,

    /* U+0031 "1" */
    0x0, 0x0, 0x2, 0xbf, 0xff, 0x60, 0x0, 0x0,
    0x2, 0x6b, 0xff, 0xff, 0xf6, 0x0, 0x0, 0x8,
    0xff, 0xff, 0xff, 0xff, 0x60, 0x0, 0x0, 0x9f,
    0xff, 0xff, 0xff, 0xf6, 0x0, 0x0, 0x9, 0xff,
    0xff, 0xff, 0xff, 0x60, 0x0, 0x0, 0x23, 0x33,
    0x6f, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xff, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x4f,
    0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff,
    0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff,
    0xf6, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xff, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xff, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xff, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xff, 0xff, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xff, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0xff, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xff, 0xff, 0x60, 0x0, 0x5, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0x6f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xd6, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfd, 0x6f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xd0,

    /* U+0032 "2" */
    0x0, 0x0, 0x6b, 0xdf, 0xec, 0x71, 0x0, 0x0,
    0x0, 0x5, 0xef, 0xff, 0xff, 0xff, 0xf5, 0x0,
    0x0, 0x9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6,
    0x0, 0x8, 0xff, 0xff, 0xfe, 0xef, 0xff, 0xff,
    0xf2, 0x0, 0x3e, 0xff, 0xc3, 0x0, 0x1b, 0xff,
    0xff, 0x80, 0x0, 0x3e, 0x90, 0x0, 0x0, 0xe,
    0xff, 0xfd, 0x0, 0x0, 0x20, 0x0, 0x0, 0x0,
    0x9f, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xff, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xa, 0xff, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xa0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xff, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xe, 0xff, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xff, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xff, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xff, 0xff, 0xe1, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xef, 0xff, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xdf, 0xff, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xcf, 0xff, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xff, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xcf, 0xff, 0xfc, 0x0, 0x11,
    0x11, 0x11, 0x1, 0xdf, 0xff, 0xff, 0xef, 0xff,
    0xff, 0xff, 0xf4, 0x9f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x4a, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf4, 0xaf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x40,

    /* U+0033 "3" */
    0x0, 0x0, 0x16, 0xbe, 0xff, 0xda, 0x40, 0x0,
    0x0, 0x0, 0x7, 0xef, 0xff, 0xff, 0xff, 0xfc,
    0x20, 0x0, 0x1, 0xbf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xe2, 0x0, 0x1, 0xef, 0xff, 0xff, 0xef,
    0xff, 0xff, 0xfb, 0x0, 0x0, 0x3f, 0xfb, 0x30,
    0x0, 0x7f, 0xff, 0xff, 0x10, 0x0, 0x4, 0x60,
    0x0, 0x0, 0xa, 0xff, 0xff, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xff, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0xff, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xff, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x1, 0x38, 0xff, 0xff,
    0xf3, 0x0, 0x0, 0x0, 0x2, 0xff, 0xff, 0xff,
    0xfd, 0x30, 0x0, 0x0, 0x0, 0x2, 0xff, 0xff,
    0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff,
    0xff, 0xff, 0xfd, 0x40, 0x0, 0x0, 0x0, 0x1,
    0x89, 0xbe, 0xff, 0xff, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5e, 0xff, 0xff, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xff, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0xff, 0xf1, 0x0, 0x13, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0xff, 0xf0, 0x0, 0xbf, 0x40, 0x0, 0x0,
    0x3, 0xff, 0xff, 0xe0, 0x6, 0xff, 0xfa, 0x41,
    0x2, 0x7f, 0xff, 0xff, 0x90, 0xe, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x10, 0x4, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0x0, 0x0,
    0x2b, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x30, 0x0,
    0x0, 0x0, 0x38, 0xce, 0xfe, 0xd9, 0x40, 0x0,
    0x0,

    /* U+0034 "4" */
    0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0xff, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xff,
    0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xff,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff,
    0xff, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0xff, 0xef, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0xff, 0x7f, 0xff, 0xf2, 0x0, 0x0, 0x0,
    0x3, 0xff, 0xfe, 0x2f, 0xff, 0xf2, 0x0, 0x0,
    0x0, 0xc, 0xff, 0xf7, 0x2f, 0xff, 0xf2, 0x0,
    0x0, 0x0, 0x5f, 0xff, 0xf1, 0x3f, 0xff, 0xf2,
    0x0, 0x0, 0x0, 0xef, 0xff, 0x70, 0x3f, 0xff,
    0xf2, 0x0, 0x0, 0x8, 0xff, 0xfd, 0x0, 0x3f,
    0xff, 0xf2, 0x0, 0x0, 0x1f, 0xff, 0xf4, 0x0,
    0x3f, 0xff, 0xf2, 0x0, 0x0, 0xaf, 0xff, 0xb0,
    0x0, 0x3f, 0xff, 0xf2, 0x0, 0x3, 0xff, 0xff,
    0x20, 0x0, 0x3f, 0xff, 0xf2, 0x0, 0xc, 0xff,
    0xf8, 0x0, 0x0, 0x3f, 0xff, 0xf2, 0x0, 0x4f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x5f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x5f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x3b, 0xbb, 0xbb, 0xbb, 0xbb, 0xcf,
    0xff, 0xfc, 0xbb, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xff, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xff, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0xf2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0xf2,
    0x0,

    /* U+0035 "5" */
    0x0, 0xa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x0, 0x0, 0xb, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x0, 0x0, 0xc, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x0, 0x0, 0xd, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x0, 0x0, 0xe, 0xff, 0xf7,
    0x11, 0x11, 0x11, 0x11, 0x0, 0x0, 0xf, 0xff,
    0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xff, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xff, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xff, 0xf1, 0x46, 0x53, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xff, 0xff, 0xff, 0xff, 0xe8,
    0x0, 0x0, 0x0, 0x3f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xd1, 0x0, 0x0, 0x3f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0x0, 0x0, 0x5, 0xed, 0x61,
    0x2, 0x9f, 0xff, 0xff, 0x50, 0x0, 0x0, 0x10,
    0x0, 0x0, 0x7, 0xff, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xef, 0xff, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xff,
    0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0xff, 0xf0, 0x0, 0x12, 0x0, 0x0, 0x0, 0x0,
    0xef, 0xff, 0xe0, 0x0, 0xae, 0x30, 0x0, 0x0,
    0x7, 0xff, 0xff, 0xa0, 0x5, 0xff, 0xfa, 0x30,
    0x2, 0x9f, 0xff, 0xff, 0x30, 0xe, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf9, 0x0, 0x4, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x0, 0x0,
    0x1a, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0x27, 0xce, 0xff, 0xd9, 0x20, 0x0,
    0x0,

    /* U+0036 "6" */
    0x0, 0x0, 0x0, 0x4a, 0xdf, 0xed, 0x82, 0x0,
    0x0, 0x0, 0x2, 0xcf, 0xff, 0xff, 0xff, 0xf9,
    0x0, 0x0, 0x2, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xfc, 0x0, 0x0, 0xdf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x80, 0x0, 0x9f, 0xff, 0xfb, 0x30, 0x3,
    0xaf, 0x90, 0x0, 0x1f, 0xff, 0xfa, 0x0, 0x0,
    0x0, 0x40, 0x0, 0x7, 0xff, 0xfe, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0xff, 0x10,
    0x6c, 0xfe, 0xc7, 0x10, 0x0, 0x4f, 0xff, 0xf2,
    0xdf, 0xff, 0xff, 0xfe, 0x30, 0x5, 0xff, 0xff,
    0xef, 0xff, 0xff, 0xff, 0xfe, 0x10, 0x6f, 0xff,
    0xff, 0xfc, 0x78, 0xdf, 0xff, 0xfa, 0x6, 0xff,
    0xff, 0xf5, 0x0, 0x0, 0xbf, 0xff, 0xf0, 0x5f,
    0xff, 0xf6, 0x0, 0x0, 0x1, 0xff, 0xff, 0x54,
    0xff, 0xff, 0x0, 0x0, 0x0, 0xd, 0xff, 0xf6,
    0x2f, 0xff, 0xf3, 0x0, 0x0, 0x0, 0xcf, 0xff,
    0x80, 0xef, 0xff, 0x60, 0x0, 0x0, 0xc, 0xff,
    0xf7, 0xa, 0xff, 0xfb, 0x0, 0x0, 0x0, 0xff,
    0xff, 0x50, 0x4f, 0xff, 0xf3, 0x0, 0x0, 0x5f,
    0xff, 0xf2, 0x0, 0xdf, 0xff, 0xe2, 0x0, 0x2e,
    0xff, 0xfc, 0x0, 0x4, 0xff, 0xff, 0xfc, 0xcf,
    0xff, 0xff, 0x30, 0x0, 0x7, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x6, 0xff, 0xff,
    0xff, 0xff, 0x60, 0x0, 0x0, 0x0, 0x1, 0x8c,
    0xff, 0xd9, 0x20, 0x0, 0x0,

    /* U+0037 "7" */
    0x6f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x56, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf5, 0x6f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x56, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xe1, 0x1, 0x11, 0x11, 0x11, 0x11, 0x2f,
    0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xff, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xff, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xff, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xff, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xd, 0xff, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xff, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xff, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xff, 0xff, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff,
    0xfb, 0x0, 0x0, 0x0, 0x0,

    /* U+0038 "8" */
    0x0, 0x0, 0x16, 0xbe, 0xfe, 0xc7, 0x10, 0x0,
    0x0, 0x0, 0x4e, 0xff, 0xff, 0xff, 0xfe, 0x40,
    0x0, 0x0, 0x3f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x30, 0x0, 0xd, 0xff, 0xff, 0x97, 0x9f, 0xff,
    0xfc, 0x0, 0x4, 0xff, 0xff, 0x40, 0x0, 0x3f,
    0xff, 0xf2, 0x0, 0x7f, 0xff, 0xc0, 0x0, 0x0,
    0xaf, 0xff, 0x50, 0x8, 0xff, 0xf9, 0x0, 0x0,
    0x7, 0xff, 0xf6, 0x0, 0x6f, 0xff, 0xb0, 0x0,
    0x0, 0x7f, 0xff, 0x40, 0x3, 0xff, 0xff, 0x30,
    0x0, 0xa, 0xff, 0xf1, 0x0, 0xc, 0xff, 0xff,
    0x60, 0x1, 0xff, 0xf8, 0x0, 0x0, 0x2e, 0xff,
    0xff, 0xc4, 0xbf, 0xfc, 0x0, 0x0, 0x0, 0x3e,
    0xff, 0xff, 0xff, 0xfc, 0x10, 0x0, 0x0, 0x1,
    0xcf, 0xff, 0xff, 0xff, 0xd2, 0x0, 0x0, 0x3,
    0xef, 0xfc, 0xdf, 0xff, 0xff, 0xf4, 0x0, 0x2,
    0xff, 0xfc, 0x0, 0x5d, 0xff, 0xff, 0xf3, 0x0,
    0xcf, 0xff, 0x30, 0x0, 0x8, 0xff, 0xff, 0xc0,
    0x3f, 0xff, 0xc0, 0x0, 0x0, 0x8, 0xff, 0xff,
    0x26, 0xff, 0xf9, 0x0, 0x0, 0x0, 0x1f, 0xff,
    0xf5, 0x7f, 0xff, 0x90, 0x0, 0x0, 0x0, 0xff,
    0xff, 0x66, 0xff, 0xfe, 0x0, 0x0, 0x0, 0x3f,
    0xff, 0xf4, 0x2f, 0xff, 0xfa, 0x0, 0x0, 0xc,
    0xff, 0xff, 0x10, 0xbf, 0xff, 0xfe, 0x97, 0x9e,
    0xff, 0xff, 0x80, 0x1, 0xdf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xc0, 0x0, 0x1, 0xaf, 0xff, 0xff,
    0xff, 0xff, 0x90, 0x0, 0x0, 0x0, 0x38, 0xce,
    0xfe, 0xc8, 0x20, 0x0, 0x0,

    /* U+0039 "9" */
    0x0, 0x0, 0x3a, 0xdf, 0xfc, 0x70, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0xff, 0xff, 0xff, 0xe4, 0x0,
    0x0, 0x0, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xf5,
    0x0, 0x0, 0x8f, 0xff, 0xff, 0xbc, 0xff, 0xff,
    0xf2, 0x0, 0x1f, 0xff, 0xfc, 0x10, 0x3, 0xef,
    0xff, 0xb0, 0x6, 0xff, 0xff, 0x10, 0x0, 0x3,
    0xff, 0xff, 0x30, 0x9f, 0xff, 0xb0, 0x0, 0x0,
    0xc, 0xff, 0xf8, 0xb, 0xff, 0xf9, 0x0, 0x0,
    0x0, 0x7f, 0xff, 0xc0, 0xaf, 0xff, 0x90, 0x0,
    0x0, 0x4, 0xff, 0xff, 0x9, 0xff, 0xfc, 0x0,
    0x0, 0x0, 0x4f, 0xff, 0xf1, 0x6f, 0xff, 0xf2,
    0x0, 0x0, 0x1d, 0xff, 0xff, 0x32, 0xff, 0xff,
    0xe5, 0x11, 0x6e, 0xff, 0xff, 0xf3, 0xa, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x30, 0xc,
    0xff, 0xff, 0xff, 0xff, 0x6f, 0xff, 0xf2, 0x0,
    0x9, 0xff, 0xff, 0xfc, 0x33, 0xff, 0xff, 0x10,
    0x0, 0x0, 0x46, 0x63, 0x0, 0x5f, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xff,
    0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff,
    0xf3, 0x0, 0x2, 0x60, 0x0, 0x0, 0x1d, 0xff,
    0xfd, 0x0, 0x0, 0xdf, 0xb3, 0x0, 0x5d, 0xff,
    0xff, 0x50, 0x0, 0xbf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xb0, 0x0, 0x1d, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xc1, 0x0, 0x0, 0x1a, 0xff, 0xff, 0xff,
    0xff, 0xa1, 0x0, 0x0, 0x0, 0x3, 0x9d, 0xef,
    0xd9, 0x30, 0x0, 0x0, 0x0,

    /* U+003A ":" */
    0x6, 0xde, 0xa0, 0x4, 0xff, 0xff, 0xa0, 0x9f,
    0xff, 0xff, 0xa, 0xff, 0xff, 0xf1, 0x7f, 0xff,
    0xfd, 0x0, 0xbf, 0xfe, 0x30, 0x0, 0x24, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x34, 0x10, 0x0, 0xbf, 0xfe,
    0x30, 0x7f, 0xff, 0xfd, 0xa, 0xff, 0xff, 0xf1,
    0x9f, 0xff, 0xff, 0x4, 0xff, 0xff, 0xa0, 0x5,
    0xde, 0xa0, 0x0,

    /* U+003B ";" */
    0x0, 0x6d, 0xea, 0x0, 0x4, 0xff, 0xff, 0xa0,
    0x9, 0xff, 0xff, 0xf0, 0xa, 0xff, 0xff, 0xf1,
    0x7, 0xff, 0xff, 0xd0, 0x0, 0xbf, 0xfe, 0x30,
    0x0, 0x2, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x29, 0xb8, 0x10,
    0x2, 0xff, 0xff, 0xc0, 0x9, 0xff, 0xff, 0xf4,
    0xa, 0xff, 0xff, 0xf9, 0x5, 0xff, 0xff, 0xfa,
    0x0, 0x6d, 0xff, 0xfb, 0x0, 0x0, 0x8f, 0xf9,
    0x0, 0x0, 0xcf, 0xf6, 0x0, 0x6, 0xff, 0xf1,
    0x0, 0x7f, 0xff, 0x80, 0x1d, 0xff, 0xfb, 0x0,
    0xe, 0xff, 0xb0, 0x0, 0x9, 0xd5, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+003C "<" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x7e,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4b, 0xff,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0x18, 0xef, 0xff,
    0xff, 0xfa, 0x0, 0x0, 0x4, 0xbf, 0xff, 0xff,
    0xff, 0xfb, 0x40, 0x1, 0x8e, 0xff, 0xff, 0xff,
    0xfb, 0x61, 0x0, 0x4b, 0xff, 0xff, 0xff, 0xfc,
    0x61, 0x0, 0x0, 0xc, 0xff, 0xff, 0xfe, 0x82,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0xff, 0xf7, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0xff, 0xfd,
    0x71, 0x0, 0x0, 0x0, 0x0, 0x4b, 0xff, 0xff,
    0xff, 0xfb, 0x50, 0x0, 0x0, 0x0, 0x2, 0x8e,
    0xff, 0xff, 0xff, 0xfa, 0x40, 0x0, 0x0, 0x0,
    0x5, 0xcf, 0xff, 0xff, 0xff, 0xe9, 0x30, 0x0,
    0x0, 0x0, 0x28, 0xef, 0xff, 0xff, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5c, 0xff, 0xff, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x9e, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0x70,

    /* U+003D "=" */
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xac, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfa, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xa5, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77,
    0x77, 0x74, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x57, 0x77, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x77, 0x4c, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfa, 0xcf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xac, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,

    /* U+003E ">" */
    0x73, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xfd, 0x71, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0xff, 0xfa, 0x40, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xff, 0xff, 0xff, 0xd7, 0x10, 0x0,
    0x0, 0x0, 0x5b, 0xff, 0xff, 0xff, 0xff, 0xa4,
    0x0, 0x0, 0x0, 0x1, 0x6c, 0xff, 0xff, 0xff,
    0xfd, 0x71, 0x0, 0x0, 0x0, 0x2, 0x7d, 0xff,
    0xff, 0xff, 0xfa, 0x30, 0x0, 0x0, 0x0, 0x3,
    0x9e, 0xff, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xff, 0xff, 0xa0, 0x0, 0x0, 0x0,
    0x2, 0x8e, 0xff, 0xff, 0xfa, 0x0, 0x0, 0x1,
    0x6c, 0xff, 0xff, 0xff, 0xfb, 0x30, 0x0, 0x5b,
    0xff, 0xff, 0xff, 0xfe, 0x71, 0x0, 0x4a, 0xff,
    0xff, 0xff, 0xff, 0xb4, 0x0, 0x0, 0xc, 0xff,
    0xff, 0xff, 0xe8, 0x10, 0x0, 0x0, 0x0, 0xcf,
    0xff, 0xfb, 0x50, 0x0, 0x0, 0x0, 0x0, 0xc,
    0xfe, 0x82, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x85, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+003F "?" */
    0x0, 0x1, 0x7c, 0xef, 0xda, 0x50, 0x0, 0x0,
    0x7f, 0xff, 0xff, 0xff, 0xfb, 0x10, 0xa, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xb0, 0x4f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf4, 0x5, 0xff, 0x92, 0x1,
    0xaf, 0xff, 0xfa, 0x0, 0x46, 0x0, 0x0, 0xe,
    0xff, 0xfc, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff,
    0xfd, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xf6, 0x0,
    0x0, 0x0, 0x1, 0xef, 0xff, 0xd0, 0x0, 0x0,
    0x0, 0xc, 0xff, 0xff, 0x30, 0x0, 0x0, 0x0,
    0x9f, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x4, 0xff,
    0xff, 0x80, 0x0, 0x0, 0x0, 0xd, 0xff, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0xff, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0x5f, 0xff, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xff, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x13, 0x33, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x43, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xff, 0xf5,
    0x0, 0x0, 0x0, 0x2, 0xff, 0xff, 0xf9, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xff, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xff, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x1a, 0xfd, 0x40, 0x0, 0x0,

    /* U+0040 "@" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x6a, 0xde,
    0xff, 0xeb, 0x83, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xbf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xd5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3e, 0xff, 0xff, 0xb6, 0x21, 0x0, 0x24, 0x9f,
    0xff, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff,
    0xff, 0xb2, 0x0, 0x0, 0x0, 0x0, 0x1, 0xaf,
    0xff, 0xb0, 0x0, 0x0, 0x0, 0x2f, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff,
    0xf6, 0x0, 0x0, 0x1, 0xdf, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xfe,
    0x0, 0x0, 0x9, 0xff, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0x60,
    0x0, 0x2f, 0xff, 0x80, 0x0, 0x0, 0x29, 0xdf,
    0xc3, 0x2b, 0xb7, 0x0, 0x7, 0xff, 0xc0, 0x0,
    0xaf, 0xfe, 0x0, 0x0, 0x5, 0xff, 0xff, 0xff,
    0xaf, 0xf8, 0x0, 0x1, 0xff, 0xf0, 0x1, 0xff,
    0xf6, 0x0, 0x0, 0x5f, 0xff, 0xff, 0xff, 0xff,
    0xf5, 0x0, 0x0, 0xdf, 0xf3, 0x7, 0xff, 0xe0,
    0x0, 0x1, 0xff, 0xff, 0x73, 0x7f, 0xff, 0xf2,
    0x0, 0x0, 0xbf, 0xf5, 0xb, 0xff, 0x90, 0x0,
    0x9, 0xff, 0xf5, 0x0, 0x9, 0xff, 0xf0, 0x0,
    0x0, 0x9f, 0xf6, 0xe, 0xff, 0x50, 0x0, 0x1f,
    0xff, 0xa0, 0x0, 0xa, 0xff, 0xb0, 0x0, 0x0,
    0x9f, 0xf5, 0xf, 0xff, 0x20, 0x0, 0x5f, 0xff,
    0x40, 0x0, 0xd, 0xff, 0x80, 0x0, 0x0, 0xbf,
    0xf4, 0x2f, 0xff, 0x0, 0x0, 0x9f, 0xff, 0x0,
    0x0, 0xf, 0xff, 0x50, 0x0, 0x0, 0xdf, 0xf2,
    0x3f, 0xff, 0x0, 0x0, 0xbf, 0xfd, 0x0, 0x0,
    0x2f, 0xff, 0x20, 0x0, 0x2, 0xff, 0xe0, 0x3f,
    0xff, 0x0, 0x0, 0xbf, 0xfd, 0x0, 0x0, 0x5f,
    0xff, 0x0, 0x0, 0xa, 0xff, 0x90, 0x2f, 0xff,
    0x10, 0x0, 0xaf, 0xff, 0x10, 0x0, 0xcf, 0xff,
    0x10, 0x0, 0x5f, 0xff, 0x20, 0x1f, 0xff, 0x30,
    0x0, 0x7f, 0xff, 0xa2, 0x3c, 0xff, 0xff, 0x91,
    0x18, 0xff, 0xf8, 0x0, 0xe, 0xff, 0x60, 0x0,
    0x1f, 0xff, 0xff, 0xff, 0xea, 0xff, 0xff, 0xff,
    0xff, 0xb0, 0x0, 0xb, 0xff, 0xb0, 0x0, 0x6,
    0xff, 0xff, 0xfe, 0x31, 0xef, 0xff, 0xff, 0xfa,
    0x0, 0x0, 0x6, 0xff, 0xf2, 0x0, 0x0, 0x4c,
    0xfe, 0x91, 0x0, 0x2a, 0xff, 0xea, 0x40, 0x0,
    0x0, 0x1, 0xef, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xff, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xff, 0xff, 0xb2, 0x0, 0x0, 0x0, 0x0, 0x2,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f,
    0xff, 0xff, 0xb6, 0x30, 0x0, 0x36, 0xbf, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xcf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xdf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x91, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0x8b,
    0xdf, 0xfe, 0xda, 0x61, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+0041 "A" */
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xff, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0xff, 0xff, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0xff, 0xff,
    0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe,
    0xff, 0xea, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xff, 0xb6, 0xff, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0x72,
    0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0xff, 0x30, 0xef, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xff, 0xff, 0x0, 0xaf, 0xff,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xfb,
    0x0, 0x6f, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xff, 0xf7, 0x0, 0x2f, 0xff, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xff, 0xf3, 0x0, 0xe,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff,
    0xf0, 0x0, 0xa, 0xff, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0xff, 0xb0, 0x0, 0x6, 0xff, 0xff,
    0x20, 0x0, 0x0, 0x0, 0xef, 0xff, 0x70, 0x0,
    0x2, 0xff, 0xff, 0x70, 0x0, 0x0, 0x3, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x0,
    0x0, 0x8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf1, 0x0, 0x0, 0xd, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf5, 0x0, 0x0, 0x2f,
    0xff, 0xfd, 0xdd, 0xdd, 0xdd, 0xdf, 0xff, 0xfa,
    0x0, 0x0, 0x7f, 0xff, 0xf2, 0x0, 0x0, 0x0,
    0xd, 0xff, 0xff, 0x0, 0x0, 0xcf, 0xff, 0xe0,
    0x0, 0x0, 0x0, 0x9, 0xff, 0xff, 0x40, 0x1,
    0xff, 0xff, 0x90, 0x0, 0x0, 0x0, 0x4, 0xff,
    0xff, 0x90, 0x5, 0xff, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xe0, 0xa, 0xff, 0xff,
    0x10, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff, 0xf2,
    0xf, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xff, 0xf7,

    /* U+0042 "B" */
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xec, 0x83, 0x0,
    0x0, 0x1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfc, 0x20, 0x0, 0x1f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0x20, 0x1, 0xff, 0xff, 0xec,
    0xcc, 0xdf, 0xff, 0xff, 0xfc, 0x0, 0x1f, 0xff,
    0xfa, 0x0, 0x0, 0x7, 0xff, 0xff, 0xf1, 0x1,
    0xff, 0xff, 0xa0, 0x0, 0x0, 0x9, 0xff, 0xff,
    0x40, 0x1f, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x5f,
    0xff, 0xf4, 0x1, 0xff, 0xff, 0xa0, 0x0, 0x0,
    0x5, 0xff, 0xff, 0x20, 0x1f, 0xff, 0xfa, 0x0,
    0x0, 0x0, 0x9f, 0xff, 0xe0, 0x1, 0xff, 0xff,
    0xa0, 0x0, 0x0, 0x6f, 0xff, 0xf7, 0x0, 0x1f,
    0xff, 0xfd, 0x99, 0x9a, 0xef, 0xff, 0xfc, 0x0,
    0x1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0x0, 0x0, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xe8, 0x0, 0x1, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfc, 0x10, 0x1f, 0xff, 0xfa,
    0x0, 0x0, 0x15, 0xcf, 0xff, 0xfb, 0x1, 0xff,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0xaf, 0xff, 0xf2,
    0x1f, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x3, 0xff,
    0xff, 0x61, 0xff, 0xff, 0xa0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0xf7, 0x1f, 0xff, 0xfa, 0x0, 0x0,
    0x0, 0x3, 0xff, 0xff, 0x71, 0xff, 0xff, 0xa0,
    0x0, 0x0, 0x0, 0xaf, 0xff, 0xf5, 0x1f, 0xff,
    0xfa, 0x0, 0x0, 0x2, 0xaf, 0xff, 0xff, 0x1,
    0xff, 0xff, 0xec, 0xcc, 0xce, 0xff, 0xff, 0xff,
    0x80, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x90, 0x1, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0x60, 0x0, 0x1f, 0xff, 0xff, 0xff,
    0xff, 0xed, 0xa5, 0x0, 0x0, 0x0,

    /* U+0043 "C" */
    0x0, 0x0, 0x0, 0x2, 0x7c, 0xef, 0xec, 0x82,
    0x0, 0x0, 0x0, 0x0, 0x1, 0x9f, 0xff, 0xff,
    0xff, 0xff, 0x90, 0x0, 0x0, 0x0, 0x2e, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0x10, 0x0, 0x2,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x60,
    0x0, 0xd, 0xff, 0xff, 0xfc, 0x64, 0x48, 0xef,
    0xf9, 0x0, 0x0, 0x8f, 0xff, 0xff, 0x50, 0x0,
    0x0, 0x9, 0xc0, 0x0, 0x1, 0xff, 0xff, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff,
    0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xff, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xff,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xff, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xff, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0xff, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xff, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9, 0xff, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xff, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x50, 0x0, 0x0, 0xbf,
    0xff, 0xff, 0x40, 0x0, 0x0, 0x9, 0xf8, 0x0,
    0x0, 0x2f, 0xff, 0xff, 0xfb, 0x64, 0x58, 0xef,
    0xff, 0x60, 0x0, 0x5, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xd0, 0x0, 0x0, 0x5f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0x10, 0x0, 0x0,
    0x2, 0xcf, 0xff, 0xff, 0xff, 0xff, 0x91, 0x0,
    0x0, 0x0, 0x0, 0x3, 0x9c, 0xff, 0xec, 0x82,
    0x0, 0x0,

    /* U+0044 "D" */
    0x1f, 0xff, 0xff, 0xff, 0xed, 0xa6, 0x20, 0x0,
    0x0, 0x0, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfa, 0x20, 0x0, 0x0, 0x1f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf5, 0x0, 0x0, 0x1f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x50, 0x0,
    0x1f, 0xff, 0xfa, 0x0, 0x14, 0xaf, 0xff, 0xff,
    0xf2, 0x0, 0x1f, 0xff, 0xfa, 0x0, 0x0, 0x2,
    0xef, 0xff, 0xfb, 0x0, 0x1f, 0xff, 0xfa, 0x0,
    0x0, 0x0, 0x3f, 0xff, 0xff, 0x20, 0x1f, 0xff,
    0xfa, 0x0, 0x0, 0x0, 0x9, 0xff, 0xff, 0x70,
    0x1f, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x3, 0xff,
    0xff, 0xb0, 0x1f, 0xff, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xff, 0xe0, 0x1f, 0xff, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0xff, 0xf0, 0x1f, 0xff,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff, 0xf1,
    0x1f, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xff, 0xf1, 0x1f, 0xff, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0xff, 0xf1, 0x1f, 0xff, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0xff, 0xf0, 0x1f, 0xff,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xe0,
    0x1f, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x4, 0xff,
    0xff, 0xb0, 0x1f, 0xff, 0xfa, 0x0, 0x0, 0x0,
    0xa, 0xff, 0xff, 0x70, 0x1f, 0xff, 0xfa, 0x0,
    0x0, 0x0, 0x4f, 0xff, 0xff, 0x10, 0x1f, 0xff,
    0xfa, 0x0, 0x0, 0x4, 0xff, 0xff, 0xfa, 0x0,
    0x1f, 0xff, 0xfa, 0x0, 0x15, 0xbf, 0xff, 0xff,
    0xf1, 0x0, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x50, 0x0, 0x1f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf5, 0x0, 0x0, 0x1f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfb, 0x20, 0x0, 0x0,
    0x1f, 0xff, 0xff, 0xff, 0xfe, 0xb7, 0x20, 0x0,
    0x0, 0x0,

    /* U+0045 "E" */
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7,
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7,
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7,
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7,
    0x1f, 0xff, 0xfa, 0x11, 0x11, 0x11, 0x11, 0x10,
    0x1f, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0,
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0,
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0,
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0,
    0x1f, 0xff, 0xfa, 0x11, 0x11, 0x11, 0x11, 0x0,
    0x1f, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0xfa, 0x11, 0x11, 0x11, 0x11, 0x11,
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,

    /* U+0046 "F" */
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0x1f, 0xff, 0xfa, 0x11, 0x11, 0x11, 0x11, 0x10,
    0x1f, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x10,
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x10,
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x10,
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x10,
    0x1f, 0xff, 0xfa, 0x11, 0x11, 0x11, 0x11, 0x0,
    0x1f, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0047 "G" */
    0x0, 0x0, 0x0, 0x1, 0x6b, 0xef, 0xfd, 0xb6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xff,
    0xff, 0xff, 0xe6, 0x0, 0x0, 0x0, 0x2d, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xa0, 0x0, 0x2,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2,
    0x0, 0xd, 0xff, 0xff, 0xfd, 0x74, 0x35, 0xaf,
    0xff, 0x40, 0x0, 0x8f, 0xff, 0xff, 0x70, 0x0,
    0x0, 0x2, 0xc6, 0x0, 0x1, 0xff, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff,
    0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xff, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0xff, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff,
    0xfa, 0x0, 0x0, 0xa, 0xff, 0xff, 0xff, 0xfc,
    0x4f, 0xff, 0xf9, 0x0, 0x0, 0xa, 0xff, 0xff,
    0xff, 0xfc, 0x3f, 0xff, 0xfa, 0x0, 0x0, 0xa,
    0xff, 0xff, 0xff, 0xfc, 0x2f, 0xff, 0xfc, 0x0,
    0x0, 0xa, 0xff, 0xff, 0xff, 0xfc, 0xf, 0xff,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xfc,
    0xd, 0xff, 0xff, 0x40, 0x0, 0x0, 0x0, 0x7,
    0xff, 0xfc, 0x8, 0xff, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x7, 0xff, 0xfc, 0x2, 0xff, 0xff, 0xf5,
    0x0, 0x0, 0x0, 0x7, 0xff, 0xfc, 0x0, 0xaf,
    0xff, 0xff, 0x60, 0x0, 0x0, 0x7, 0xff, 0xfc,
    0x0, 0x1f, 0xff, 0xff, 0xfd, 0x85, 0x45, 0x9f,
    0xff, 0xfc, 0x0, 0x4, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfc, 0x0, 0x0, 0x4f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x0, 0x0,
    0x1, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x20,
    0x0, 0x0, 0x0, 0x2, 0x7b, 0xef, 0xfd, 0xb7,
    0x20, 0x0,

    /* U+0048 "H" */
    0x1f, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xff, 0xf5, 0x1f, 0xff, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xff, 0xf5, 0x1f, 0xff, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xff, 0xf5, 0x1f, 0xff,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xf5,
    0x1f, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xff, 0xf5, 0x1f, 0xff, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xff, 0xf5, 0x1f, 0xff, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xff, 0xf5, 0x1f, 0xff,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xf5,
    0x1f, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xff, 0xf5, 0x1f, 0xff, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xff, 0xf5, 0x1f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x1f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf5,
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf5, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf5, 0x1f, 0xff, 0xfb, 0x44,
    0x44, 0x44, 0x44, 0x8f, 0xff, 0xf5, 0x1f, 0xff,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xf5,
    0x1f, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xff, 0xf5, 0x1f, 0xff, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xff, 0xf5, 0x1f, 0xff, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xff, 0xf5, 0x1f, 0xff,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xf5,
    0x1f, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xff, 0xf5, 0x1f, 0xff, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xff, 0xf5, 0x1f, 0xff, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xff, 0xf5, 0x1f, 0xff,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xf5,
    0x1f, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xff, 0xf5,

    /* U+0049 "I" */
    0x1f, 0xff, 0xfa, 0x1f, 0xff, 0xfa, 0x1f, 0xff,
    0xfa, 0x1f, 0xff, 0xfa, 0x1f, 0xff, 0xfa, 0x1f,
    0xff, 0xfa, 0x1f, 0xff, 0xfa, 0x1f, 0xff, 0xfa,
    0x1f, 0xff, 0xfa, 0x1f, 0xff, 0xfa, 0x1f, 0xff,
    0xfa, 0x1f, 0xff, 0xfa, 0x1f, 0xff, 0xfa, 0x1f,
    0xff, 0xfa, 0x1f, 0xff, 0xfa, 0x1f, 0xff, 0xfa,
    0x1f, 0xff, 0xfa, 0x1f, 0xff, 0xfa, 0x1f, 0xff,
    0xfa, 0x1f, 0xff, 0xfa, 0x1f, 0xff, 0xfa, 0x1f,
    0xff, 0xfa, 0x1f, 0xff, 0xfa, 0x1f, 0xff, 0xfa,
    0x1f, 0xff, 0xfa,

    /* U+004A "J" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xf4,
    0x0, 0x6, 0x20, 0x0, 0x0, 0x9f, 0xff, 0xf3,
    0x0, 0xaf, 0xc1, 0x0, 0x1, 0xef, 0xff, 0xf0,
    0x1d, 0xff, 0xfd, 0x64, 0x6e, 0xff, 0xff, 0xb0,
    0xc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x30,
    0x1, 0xef, 0xff, 0xff, 0xff, 0xff, 0xf9, 0x0,
    0x0, 0x1c, 0xff, 0xff, 0xff, 0xff, 0x90, 0x0,
    0x0, 0x0, 0x4a, 0xdf, 0xfe, 0xa4, 0x0, 0x0,

    /* U+004B "K" */
    0x1f, 0xff, 0xfa, 0x0, 0x0, 0x0, 0xc, 0xff,
    0xff, 0x60, 0x1, 0xff, 0xff, 0xa0, 0x0, 0x0,
    0x8, 0xff, 0xff, 0xb0, 0x0, 0x1f, 0xff, 0xfa,
    0x0, 0x0, 0x3, 0xff, 0xff, 0xe1, 0x0, 0x1,
    0xff, 0xff, 0xa0, 0x0, 0x1, 0xef, 0xff, 0xf3,
    0x0, 0x0, 0x1f, 0xff, 0xfa, 0x0, 0x0, 0xaf,
    0xff, 0xf7, 0x0, 0x0, 0x1, 0xff, 0xff, 0xa0,
    0x0, 0x6f, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x1f,
    0xff, 0xfa, 0x0, 0x2f, 0xff, 0xfe, 0x10, 0x0,
    0x0, 0x1, 0xff, 0xff, 0xa0, 0xc, 0xff, 0xff,
    0x40, 0x0, 0x0, 0x0, 0x1f, 0xff, 0xfa, 0x8,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x1, 0xff,
    0xff, 0xa4, 0xff, 0xff, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xff, 0xfb, 0xef, 0xff, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf1, 0x0, 0x0, 0x0,
    0x1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x1f, 0xff, 0xff, 0xff, 0x3d,
    0xff, 0xff, 0x20, 0x0, 0x0, 0x1, 0xff, 0xff,
    0xff, 0x70, 0x4f, 0xff, 0xfb, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0xff, 0xb0, 0x0, 0xcf, 0xff, 0xf4,
    0x0, 0x0, 0x1, 0xff, 0xff, 0xe1, 0x0, 0x3,
    0xff, 0xff, 0xd0, 0x0, 0x0, 0x1f, 0xff, 0xfa,
    0x0, 0x0, 0xa, 0xff, 0xff, 0x60, 0x0, 0x1,
    0xff, 0xff, 0xa0, 0x0, 0x0, 0x2f, 0xff, 0xfe,
    0x10, 0x0, 0x1f, 0xff, 0xfa, 0x0, 0x0, 0x0,
    0x9f, 0xff, 0xf8, 0x0, 0x1, 0xff, 0xff, 0xa0,
    0x0, 0x0, 0x1, 0xff, 0xff, 0xf2, 0x0, 0x1f,
    0xff, 0xfa, 0x0, 0x0, 0x0, 0x7, 0xff, 0xff,
    0xa0, 0x1, 0xff, 0xff, 0xa0, 0x0, 0x0, 0x0,
    0xe, 0xff, 0xff, 0x30, 0x1f, 0xff, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xff, 0xfc, 0x0,

    /* U+004C "L" */
    0x1f, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0xfa, 0x11, 0x11, 0x11, 0x11, 0x10,
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,

    /* U+004D "M" */
    0x1f, 0xff, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xff, 0xff, 0x61, 0xff, 0xff, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xff, 0xff, 0xf6, 0x1f,
    0xff, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0xff, 0xff, 0x61, 0xff, 0xff, 0xff, 0x50, 0x0,
    0x0, 0x0, 0xf, 0xff, 0xff, 0xf6, 0x1f, 0xff,
    0xff, 0xfb, 0x0, 0x0, 0x0, 0x5, 0xff, 0xff,
    0xff, 0x61, 0xff, 0xff, 0xff, 0xf1, 0x0, 0x0,
    0x0, 0xbf, 0xff, 0xff, 0xf6, 0x1f, 0xff, 0xef,
    0xff, 0x60, 0x0, 0x0, 0xf, 0xff, 0xdf, 0xff,
    0x61, 0xff, 0xfb, 0xef, 0xfb, 0x0, 0x0, 0x5,
    0xff, 0xf9, 0xff, 0xf6, 0x1f, 0xff, 0xca, 0xff,
    0xf1, 0x0, 0x0, 0xbf, 0xfd, 0x8f, 0xff, 0x61,
    0xff, 0xfd, 0x4f, 0xff, 0x60, 0x0, 0x1f, 0xff,
    0x89, 0xff, 0xf6, 0x1f, 0xff, 0xf0, 0xff, 0xfc,
    0x0, 0x6, 0xff, 0xf3, 0xbf, 0xff, 0x61, 0xff,
    0xff, 0xa, 0xff, 0xf1, 0x0, 0xbf, 0xfd, 0xc,
    0xff, 0xf6, 0x1f, 0xff, 0xf1, 0x5f, 0xff, 0x60,
    0xf, 0xff, 0x80, 0xdf, 0xff, 0x61, 0xff, 0xff,
    0x20, 0xff, 0xfb, 0x5, 0xff, 0xf3, 0xe, 0xff,
    0xf6, 0x1f, 0xff, 0xf2, 0xa, 0xff, 0xf0, 0x9f,
    0xfd, 0x0, 0xef, 0xff, 0x61, 0xff, 0xff, 0x20,
    0x4f, 0xff, 0x5e, 0xff, 0x70, 0xe, 0xff, 0xf6,
    0x1f, 0xff, 0xf2, 0x0, 0xef, 0xfd, 0xff, 0xf2,
    0x0, 0xef, 0xff, 0x61, 0xff, 0xff, 0x20, 0x9,
    0xff, 0xff, 0xfc, 0x0, 0xe, 0xff, 0xf6, 0x1f,
    0xff, 0xf2, 0x0, 0x3f, 0xff, 0xff, 0x70, 0x0,
    0xef, 0xff, 0x61, 0xff, 0xff, 0x20, 0x0, 0xef,
    0xff, 0xf1, 0x0, 0xe, 0xff, 0xf6, 0x1f, 0xff,
    0xf2, 0x0, 0x8, 0xff, 0xfb, 0x0, 0x0, 0xef,
    0xff, 0x61, 0xff, 0xff, 0x20, 0x0, 0x2f, 0xff,
    0x60, 0x0, 0xe, 0xff, 0xf6, 0x1f, 0xff, 0xf2,
    0x0, 0x0, 0xab, 0xb1, 0x0, 0x0, 0xef, 0xff,
    0x61, 0xff, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xe, 0xff, 0xf6, 0x1f, 0xff, 0xf2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xff, 0x60,

    /* U+004E "N" */
    0x1f, 0xff, 0xff, 0x10, 0x0, 0x0, 0x0, 0x7f,
    0xff, 0xf0, 0x1f, 0xff, 0xff, 0x80, 0x0, 0x0,
    0x0, 0x7f, 0xff, 0xf0, 0x1f, 0xff, 0xff, 0xf1,
    0x0, 0x0, 0x0, 0x7f, 0xff, 0xf0, 0x1f, 0xff,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xf0,
    0x1f, 0xff, 0xff, 0xff, 0x10, 0x0, 0x0, 0x7f,
    0xff, 0xf0, 0x1f, 0xff, 0xff, 0xff, 0x90, 0x0,
    0x0, 0x7f, 0xff, 0xf0, 0x1f, 0xff, 0xee, 0xff,
    0xf2, 0x0, 0x0, 0x7f, 0xff, 0xf0, 0x1f, 0xff,
    0xe7, 0xff, 0xf9, 0x0, 0x0, 0x7f, 0xff, 0xf0,
    0x1f, 0xff, 0xf1, 0xff, 0xff, 0x20, 0x0, 0x7f,
    0xff, 0xf0, 0x1f, 0xff, 0xf1, 0x9f, 0xff, 0xa0,
    0x0, 0x7f, 0xff, 0xf0, 0x1f, 0xff, 0xf2, 0x2f,
    0xff, 0xf2, 0x0, 0x6f, 0xff, 0xf0, 0x1f, 0xff,
    0xf3, 0xa, 0xff, 0xfa, 0x0, 0x6f, 0xff, 0xf0,
    0x1f, 0xff, 0xf4, 0x3, 0xff, 0xff, 0x30, 0x5f,
    0xff, 0xf0, 0x1f, 0xff, 0xf5, 0x0, 0xaf, 0xff,
    0xb0, 0x4f, 0xff, 0xf0, 0x1f, 0xff, 0xf6, 0x0,
    0x2f, 0xff, 0xf2, 0x3f, 0xff, 0xf0, 0x1f, 0xff,
    0xf6, 0x0, 0xa, 0xff, 0xf9, 0x2f, 0xff, 0xf0,
    0x1f, 0xff, 0xf6, 0x0, 0x2, 0xff, 0xff, 0x1f,
    0xff, 0xf0, 0x1f, 0xff, 0xf6, 0x0, 0x0, 0xaf,
    0xff, 0x7f, 0xff, 0xf0, 0x1f, 0xff, 0xf6, 0x0,
    0x0, 0x2f, 0xff, 0xee, 0xff, 0xf0, 0x1f, 0xff,
    0xf6, 0x0, 0x0, 0x9, 0xff, 0xff, 0xff, 0xf0,
    0x1f, 0xff, 0xf6, 0x0, 0x0, 0x2, 0xff, 0xff,
    0xff, 0xf0, 0x1f, 0xff, 0xf6, 0x0, 0x0, 0x0,
    0x9f, 0xff, 0xff, 0xf0, 0x1f, 0xff, 0xf6, 0x0,
    0x0, 0x0, 0x1f, 0xff, 0xff, 0xf0, 0x1f, 0xff,
    0xf6, 0x0, 0x0, 0x0, 0x9, 0xff, 0xff, 0xf0,
    0x1f, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x1, 0xff,
    0xff, 0xf0,

    /* U+004F "O" */
    0x0, 0x0, 0x0, 0x5, 0xad, 0xff, 0xec, 0x83,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xdf, 0xff,
    0xff, 0xff, 0xff, 0xb1, 0x0, 0x0, 0x0, 0x0,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x30,
    0x0, 0x0, 0x8, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf2, 0x0, 0x0, 0x3f, 0xff, 0xff,
    0xf8, 0x43, 0x5b, 0xff, 0xff, 0xfd, 0x0, 0x0,
    0xcf, 0xff, 0xfd, 0x10, 0x0, 0x0, 0x5f, 0xff,
    0xff, 0x70, 0x4, 0xff, 0xff, 0xf2, 0x0, 0x0,
    0x0, 0x6, 0xff, 0xff, 0xd0, 0x9, 0xff, 0xff,
    0x80, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xff, 0xf3,
    0xd, 0xff, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xff, 0xf7, 0xf, 0xff, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xff, 0xfa, 0x2f, 0xff,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff,
    0xfc, 0x3f, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xff, 0xfd, 0x4f, 0xff, 0xf9, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xfe, 0x3f,
    0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xff, 0xfd, 0x2f, 0xff, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xff, 0xfc, 0xf, 0xff, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xfa,
    0xc, 0xff, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x8f, 0xff, 0xf7, 0x8, 0xff, 0xff, 0x90, 0x0,
    0x0, 0x0, 0x0, 0xef, 0xff, 0xf2, 0x2, 0xff,
    0xff, 0xf3, 0x0, 0x0, 0x0, 0x8, 0xff, 0xff,
    0xc0, 0x0, 0xbf, 0xff, 0xfe, 0x20, 0x0, 0x0,
    0x6f, 0xff, 0xff, 0x50, 0x0, 0x2f, 0xff, 0xff,
    0xf9, 0x54, 0x6b, 0xff, 0xff, 0xfc, 0x0, 0x0,
    0x6, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xe1, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0x20, 0x0, 0x0, 0x0, 0x4,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xa1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xad, 0xff, 0xec, 0x82,
    0x0, 0x0, 0x0,

    /* U+0050 "P" */
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xdb, 0x73, 0x0,
    0x0, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xb2, 0x0, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0x20, 0x1f, 0xff, 0xff, 0xee, 0xef,
    0xff, 0xff, 0xff, 0xe0, 0x1f, 0xff, 0xfa, 0x0,
    0x0, 0x28, 0xff, 0xff, 0xf6, 0x1f, 0xff, 0xfa,
    0x0, 0x0, 0x0, 0x4f, 0xff, 0xfb, 0x1f, 0xff,
    0xfa, 0x0, 0x0, 0x0, 0xd, 0xff, 0xfe, 0x1f,
    0xff, 0xfa, 0x0, 0x0, 0x0, 0xb, 0xff, 0xff,
    0x1f, 0xff, 0xfa, 0x0, 0x0, 0x0, 0xb, 0xff,
    0xfe, 0x1f, 0xff, 0xfa, 0x0, 0x0, 0x0, 0xe,
    0xff, 0xfd, 0x1f, 0xff, 0xfa, 0x0, 0x0, 0x0,
    0x6f, 0xff, 0xf9, 0x1f, 0xff, 0xfa, 0x0, 0x0,
    0x18, 0xff, 0xff, 0xf3, 0x1f, 0xff, 0xff, 0xdd,
    0xde, 0xff, 0xff, 0xff, 0xa0, 0x1f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0x0, 0x1f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0x0, 0x1f,
    0xff, 0xff, 0xff, 0xff, 0xdb, 0x61, 0x0, 0x0,
    0x1f, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xff, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xff, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+0051 "Q" */
    0x0, 0x0, 0x0, 0x4, 0xad, 0xff, 0xec, 0x82,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4d, 0xff,
    0xff, 0xff, 0xff, 0xfa, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0x20, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0x20, 0x0, 0x0, 0x2f,
    0xff, 0xff, 0xf9, 0x43, 0x5b, 0xff, 0xff, 0xfc,
    0x0, 0x0, 0xb, 0xff, 0xff, 0xd2, 0x0, 0x0,
    0x6, 0xff, 0xff, 0xf5, 0x0, 0x2, 0xff, 0xff,
    0xf2, 0x0, 0x0, 0x0, 0x8, 0xff, 0xff, 0xc0,
    0x0, 0x8f, 0xff, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0xe, 0xff, 0xff, 0x20, 0xc, 0xff, 0xff, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xf6, 0x0,
    0xff, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xff, 0xff, 0xa0, 0x2f, 0xff, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xff, 0xfc, 0x3, 0xff,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xd0, 0x4f, 0xff, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xff, 0xfe, 0x3, 0xff, 0xff,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff,
    0xd0, 0x3f, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xff, 0xfc, 0x1, 0xff, 0xff, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0xff, 0xb0,
    0xe, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0xf8, 0x0, 0xaf, 0xff, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xff, 0xff, 0x40, 0x6,
    0xff, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x1, 0xff,
    0xff, 0xf0, 0x0, 0xf, 0xff, 0xff, 0x40, 0x0,
    0x0, 0x0, 0xaf, 0xff, 0xf9, 0x0, 0x0, 0x8f,
    0xff, 0xff, 0x40, 0x0, 0x0, 0x8f, 0xff, 0xff,
    0x10, 0x0, 0x0, 0xdf, 0xff, 0xff, 0xa6, 0x57,
    0xdf, 0xff, 0xff, 0x80, 0x0, 0x0, 0x2, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb0, 0x0,
    0x0, 0x0, 0x2, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x9f,
    0xff, 0xff, 0xff, 0xfe, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x15, 0xef, 0xff, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xff, 0xff, 0xe2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xff, 0xff, 0xf8, 0x30,
    0x0, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1d,
    0xff, 0xff, 0xff, 0xfe, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2d, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9,
    0xff, 0xff, 0xff, 0xff, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0x7b, 0xef, 0xfe, 0xc7,

    /* U+0052 "R" */
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xec, 0x84, 0x0,
    0x0, 0x0, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xc3, 0x0, 0x0, 0x1f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x40, 0x0, 0x1f, 0xff,
    0xff, 0xee, 0xef, 0xff, 0xff, 0xff, 0xf1, 0x0,
    0x1f, 0xff, 0xfa, 0x0, 0x0, 0x16, 0xef, 0xff,
    0xf8, 0x0, 0x1f, 0xff, 0xfa, 0x0, 0x0, 0x0,
    0x2f, 0xff, 0xfd, 0x0, 0x1f, 0xff, 0xfa, 0x0,
    0x0, 0x0, 0xb, 0xff, 0xff, 0x0, 0x1f, 0xff,
    0xfa, 0x0, 0x0, 0x0, 0x9, 0xff, 0xff, 0x0,
    0x1f, 0xff, 0xfa, 0x0, 0x0, 0x0, 0xa, 0xff,
    0xff, 0x0, 0x1f, 0xff, 0xfa, 0x0, 0x0, 0x0,
    0xd, 0xff, 0xfe, 0x0, 0x1f, 0xff, 0xfa, 0x0,
    0x0, 0x0, 0x6f, 0xff, 0xfb, 0x0, 0x1f, 0xff,
    0xfa, 0x0, 0x0, 0x39, 0xff, 0xff, 0xf5, 0x0,
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xb0, 0x0, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfc, 0x0, 0x0, 0x1f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x80, 0x0, 0x0, 0x1f, 0xff,
    0xff, 0xee, 0xef, 0xff, 0xfc, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0xfa, 0x0, 0xb, 0xff, 0xff, 0x50,
    0x0, 0x0, 0x1f, 0xff, 0xfa, 0x0, 0x3, 0xff,
    0xff, 0xd0, 0x0, 0x0, 0x1f, 0xff, 0xfa, 0x0,
    0x0, 0xaf, 0xff, 0xf7, 0x0, 0x0, 0x1f, 0xff,
    0xfa, 0x0, 0x0, 0x2f, 0xff, 0xfe, 0x10, 0x0,
    0x1f, 0xff, 0xfa, 0x0, 0x0, 0x9, 0xff, 0xff,
    0x90, 0x0, 0x1f, 0xff, 0xfa, 0x0, 0x0, 0x1,
    0xff, 0xff, 0xf2, 0x0, 0x1f, 0xff, 0xfa, 0x0,
    0x0, 0x0, 0x8f, 0xff, 0xfb, 0x0, 0x1f, 0xff,
    0xfa, 0x0, 0x0, 0x0, 0x1e, 0xff, 0xff, 0x40,
    0x1f, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x7, 0xff,
    0xff, 0xc0,

    /* U+0053 "S" */
    0x0, 0x0, 0x2, 0x8c, 0xef, 0xec, 0x72, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0xff, 0xff, 0xff, 0xff,
    0xa1, 0x0, 0x0, 0xc, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0x30, 0x0, 0xaf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xa0, 0x3, 0xff, 0xff, 0xfa,
    0x53, 0x48, 0xdf, 0xfd, 0x0, 0x8, 0xff, 0xff,
    0x80, 0x0, 0x0, 0x7, 0xe2, 0x0, 0xa, 0xff,
    0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xff, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xff, 0xff, 0xe3, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xff, 0xff, 0xff, 0xa3, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0xff, 0xff, 0xff, 0xc6,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xff, 0xff,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff,
    0xff, 0xff, 0xff, 0xe3, 0x0, 0x0, 0x0, 0x1,
    0x8e, 0xff, 0xff, 0xff, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x6d, 0xff, 0xff, 0xff, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4d, 0xff, 0xff, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xff,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xff, 0xfa, 0x0, 0x48, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xff, 0xfa, 0x2, 0xef, 0xc3, 0x0, 0x0,
    0x0, 0xaf, 0xff, 0xf7, 0xd, 0xff, 0xff, 0xc7,
    0x44, 0x6c, 0xff, 0xff, 0xf1, 0x6f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0x5, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x0, 0x0,
    0x2a, 0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0x0,
    0x0, 0x0, 0x16, 0xbd, 0xff, 0xdb, 0x61, 0x0,
    0x0,

    /* U+0054 "T" */
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0xf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0x1, 0x11, 0x11, 0x17, 0xff, 0xff, 0x61, 0x11,
    0x11, 0x10, 0x0, 0x0, 0x0, 0x6, 0xff, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xff, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xff, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xff, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xff, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xff, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xff, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xff, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xff, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xff, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xff, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xff, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xff, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xff, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xff, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xff, 0xff, 0x50, 0x0,
    0x0, 0x0,

    /* U+0055 "U" */
    0x3f, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xff, 0xf2, 0x3f, 0xff, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xff, 0xf2, 0x3f, 0xff, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xff, 0xf2, 0x3f, 0xff,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xf2,
    0x3f, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xff, 0xf2, 0x3f, 0xff, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xff, 0xf2, 0x3f, 0xff, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xff, 0xf2, 0x3f, 0xff,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xf2,
    0x3f, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xff, 0xf2, 0x3f, 0xff, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xff, 0xf2, 0x3f, 0xff, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xff, 0xf2, 0x3f, 0xff,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xf2,
    0x3f, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xff, 0xf2, 0x2f, 0xff, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xff, 0xf2, 0x2f, 0xff, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xff, 0xf1, 0x1f, 0xff,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xf0,
    0xf, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xff, 0xf0, 0xd, 0xff, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0xdf, 0xff, 0xc0, 0x9, 0xff, 0xff, 0x50,
    0x0, 0x0, 0x3, 0xff, 0xff, 0x90, 0x4, 0xff,
    0xff, 0xd1, 0x0, 0x0, 0x1d, 0xff, 0xff, 0x40,
    0x0, 0xef, 0xff, 0xfe, 0x84, 0x47, 0xef, 0xff,
    0xfd, 0x0, 0x0, 0x4f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf4, 0x0, 0x0, 0x8, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x6e, 0xff, 0xff, 0xff, 0xff, 0xe6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6b, 0xef, 0xfe, 0xb6, 0x0,
    0x0, 0x0,

    /* U+0056 "V" */
    0xf, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xd0, 0xbf, 0xff, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xff, 0xf8, 0x6, 0xff, 0xff,
    0x70, 0x0, 0x0, 0x0, 0x7, 0xff, 0xff, 0x30,
    0x2f, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xff, 0xf0, 0x0, 0xdf, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0xf, 0xff, 0xfa, 0x0, 0x9, 0xff, 0xff,
    0x30, 0x0, 0x0, 0x3, 0xff, 0xff, 0x60, 0x0,
    0x4f, 0xff, 0xf7, 0x0, 0x0, 0x0, 0x7f, 0xff,
    0xf1, 0x0, 0x0, 0xff, 0xff, 0xb0, 0x0, 0x0,
    0xb, 0xff, 0xfc, 0x0, 0x0, 0xb, 0xff, 0xff,
    0x0, 0x0, 0x0, 0xff, 0xff, 0x80, 0x0, 0x0,
    0x6f, 0xff, 0xf4, 0x0, 0x0, 0x3f, 0xff, 0xf3,
    0x0, 0x0, 0x1, 0xff, 0xff, 0x80, 0x0, 0x7,
    0xff, 0xfe, 0x0, 0x0, 0x0, 0xd, 0xff, 0xfc,
    0x0, 0x0, 0xbf, 0xff, 0xa0, 0x0, 0x0, 0x0,
    0x8f, 0xff, 0xf0, 0x0, 0xf, 0xff, 0xf5, 0x0,
    0x0, 0x0, 0x4, 0xff, 0xff, 0x40, 0x3, 0xff,
    0xff, 0x10, 0x0, 0x0, 0x0, 0xf, 0xff, 0xf8,
    0x0, 0x7f, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0xff, 0xb0, 0xb, 0xff, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xff, 0xff, 0x0, 0xff, 0xff,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0xf3,
    0x3f, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0xff, 0x77, 0xff, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xfb, 0xbf, 0xff, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0xff,
    0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xef, 0xff, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xff, 0xff, 0xff, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xff,
    0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xff, 0xff, 0xfe, 0x0, 0x0, 0x0, 0x0,

    /* U+0057 "W" */
    0x4f, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x6f, 0xff,
    0xe0, 0x0, 0x0, 0x0, 0xef, 0xff, 0x81, 0xff,
    0xff, 0xa0, 0x0, 0x0, 0x9, 0xff, 0xff, 0x20,
    0x0, 0x0, 0x1f, 0xff, 0xf6, 0xe, 0xff, 0xfc,
    0x0, 0x0, 0x0, 0xdf, 0xff, 0xf5, 0x0, 0x0,
    0x3, 0xff, 0xff, 0x30, 0xcf, 0xff, 0xf0, 0x0,
    0x0, 0xf, 0xff, 0xff, 0x90, 0x0, 0x0, 0x6f,
    0xff, 0xf0, 0x9, 0xff, 0xff, 0x10, 0x0, 0x4,
    0xff, 0xff, 0xfc, 0x0, 0x0, 0x8, 0xff, 0xfd,
    0x0, 0x6f, 0xff, 0xf4, 0x0, 0x0, 0x7f, 0xff,
    0xff, 0xf0, 0x0, 0x0, 0xbf, 0xff, 0xa0, 0x3,
    0xff, 0xff, 0x60, 0x0, 0xb, 0xff, 0xff, 0xff,
    0x40, 0x0, 0xd, 0xff, 0xf7, 0x0, 0xf, 0xff,
    0xf8, 0x0, 0x0, 0xef, 0xfa, 0xff, 0xf7, 0x0,
    0x0, 0xff, 0xff, 0x50, 0x0, 0xdf, 0xff, 0xb0,
    0x0, 0x2f, 0xff, 0x5f, 0xff, 0xb0, 0x0, 0x2f,
    0xff, 0xf2, 0x0, 0xa, 0xff, 0xfd, 0x0, 0x6,
    0xff, 0xf1, 0xef, 0xfe, 0x0, 0x5, 0xff, 0xff,
    0x0, 0x0, 0x7f, 0xff, 0xf0, 0x0, 0x9f, 0xfe,
    0xb, 0xff, 0xf2, 0x0, 0x7f, 0xff, 0xc0, 0x0,
    0x4, 0xff, 0xff, 0x20, 0xd, 0xff, 0xb0, 0x8f,
    0xff, 0x60, 0x9, 0xff, 0xf9, 0x0, 0x0, 0x1f,
    0xff, 0xf4, 0x0, 0xff, 0xf8, 0x4, 0xff, 0xf9,
    0x0, 0xcf, 0xff, 0x70, 0x0, 0x0, 0xef, 0xff,
    0x70, 0x3f, 0xff, 0x50, 0x1f, 0xff, 0xc0, 0xe,
    0xff, 0xf4, 0x0, 0x0, 0xc, 0xff, 0xf9, 0x6,
    0xff, 0xf2, 0x0, 0xef, 0xff, 0x0, 0xff, 0xff,
    0x10, 0x0, 0x0, 0x9f, 0xff, 0xb0, 0x9f, 0xfe,
    0x0, 0xa, 0xff, 0xf2, 0x2f, 0xff, 0xe0, 0x0,
    0x0, 0x6, 0xff, 0xfd, 0xc, 0xff, 0xb0, 0x0,
    0x7f, 0xff, 0x64, 0xff, 0xfb, 0x0, 0x0, 0x0,
    0x3f, 0xff, 0xf0, 0xff, 0xf8, 0x0, 0x3, 0xff,
    0xf9, 0x6f, 0xff, 0x90, 0x0, 0x0, 0x0, 0xff,
    0xff, 0x5f, 0xff, 0x40, 0x0, 0xf, 0xff, 0xb9,
    0xff, 0xf6, 0x0, 0x0, 0x0, 0xd, 0xff, 0xfa,
    0xff, 0xf1, 0x0, 0x0, 0xcf, 0xfe, 0xbf, 0xff,
    0x30, 0x0, 0x0, 0x0, 0xaf, 0xff, 0xff, 0xfe,
    0x0, 0x0, 0x9, 0xff, 0xff, 0xff, 0xf0, 0x0,
    0x0, 0x0, 0x7, 0xff, 0xff, 0xff, 0xa0, 0x0,
    0x0, 0x6f, 0xff, 0xff, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0xff, 0xff, 0xf7, 0x0, 0x0, 0x2,
    0xff, 0xff, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x1,
    0xff, 0xff, 0xff, 0x40, 0x0, 0x0, 0xf, 0xff,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0xe, 0xff,
    0xff, 0xf0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xff,
    0x50, 0x0, 0x0,

    /* U+0058 "X" */
    0xe, 0xff, 0xff, 0x50, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xe0, 0x6, 0xff, 0xff, 0xc0, 0x0, 0x0,
    0x7, 0xff, 0xff, 0x70, 0x0, 0xdf, 0xff, 0xf3,
    0x0, 0x0, 0xd, 0xff, 0xfe, 0x0, 0x0, 0x5f,
    0xff, 0xfb, 0x0, 0x0, 0x4f, 0xff, 0xf6, 0x0,
    0x0, 0xd, 0xff, 0xff, 0x20, 0x0, 0xbf, 0xff,
    0xe0, 0x0, 0x0, 0x5, 0xff, 0xff, 0x90, 0x2,
    0xff, 0xff, 0x60, 0x0, 0x0, 0x0, 0xcf, 0xff,
    0xf1, 0x9, 0xff, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xff, 0xf8, 0xf, 0xff, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xff, 0xfe, 0x6f, 0xff, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xff, 0xff,
    0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xff, 0xff, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xff, 0xff, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xff, 0xff, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xff,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff,
    0xff, 0xff, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x9, 0xff, 0xfc, 0xff, 0xff, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xff, 0xf4, 0xaf, 0xff, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0xff, 0xd0, 0x3f,
    0xff, 0xfa, 0x0, 0x0, 0x0, 0x2, 0xff, 0xff,
    0x60, 0xb, 0xff, 0xff, 0x30, 0x0, 0x0, 0xa,
    0xff, 0xfe, 0x0, 0x4, 0xff, 0xff, 0xb0, 0x0,
    0x0, 0x3f, 0xff, 0xf8, 0x0, 0x0, 0xcf, 0xff,
    0xf3, 0x0, 0x0, 0xbf, 0xff, 0xf1, 0x0, 0x0,
    0x4f, 0xff, 0xfc, 0x0, 0x3, 0xff, 0xff, 0x90,
    0x0, 0x0, 0xd, 0xff, 0xff, 0x40, 0xb, 0xff,
    0xff, 0x20, 0x0, 0x0, 0x5, 0xff, 0xff, 0xc0,
    0x4f, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0xff, 0xf5,

    /* U+0059 "Y" */
    0xe, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0x5f,
    0xff, 0xf8, 0x8, 0xff, 0xff, 0x60, 0x0, 0x0,
    0x0, 0xbf, 0xff, 0xf1, 0x1, 0xff, 0xff, 0xc0,
    0x0, 0x0, 0x1, 0xff, 0xff, 0xa0, 0x0, 0x9f,
    0xff, 0xf3, 0x0, 0x0, 0x8, 0xff, 0xff, 0x30,
    0x0, 0x2f, 0xff, 0xf9, 0x0, 0x0, 0xe, 0xff,
    0xfc, 0x0, 0x0, 0xb, 0xff, 0xfe, 0x0, 0x0,
    0x4f, 0xff, 0xf4, 0x0, 0x0, 0x4, 0xff, 0xff,
    0x50, 0x0, 0xaf, 0xff, 0xd0, 0x0, 0x0, 0x0,
    0xdf, 0xff, 0xb0, 0x1, 0xff, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x5f, 0xff, 0xf1, 0x6, 0xff, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0xf7, 0xc,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff,
    0xfd, 0x3f, 0xff, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xff, 0xff, 0xcf, 0xff, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0xff, 0xff, 0xff, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xff, 0xff,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xff, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xff, 0xff, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xff, 0xff, 0xa0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xff,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xff, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xff, 0xff, 0xa0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xff, 0xff, 0xa0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xff,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xff, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xff, 0xff, 0xa0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xff, 0xff, 0xa0, 0x0,
    0x0, 0x0,

    /* U+005A "Z" */
    0x9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf3, 0x9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf3, 0x9, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf3, 0x9, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xd0, 0x0, 0x11, 0x11, 0x11,
    0x11, 0x1d, 0xff, 0xff, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xff, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xff, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xff, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xff, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xff,
    0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xff,
    0xfe, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xff, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xff, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1e, 0xff, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xff, 0xff, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xff, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xff, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xff, 0xff, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0xff, 0x81,
    0x11, 0x11, 0x11, 0x11, 0x10, 0x4f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x9f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0xaf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf5,
    0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf5,

    /* U+005B "[" */
    0xcf, 0xff, 0xff, 0xf9, 0xcf, 0xff, 0xff, 0xf9,
    0xcf, 0xfe, 0xaa, 0xa5, 0xcf, 0xfc, 0x0, 0x0,
    0xcf, 0xfc, 0x0, 0x0, 0xcf, 0xfc, 0x0, 0x0,
    0xcf, 0xfc, 0x0, 0x0, 0xcf, 0xfc, 0x0, 0x0,
    0xcf, 0xfc, 0x0, 0x0, 0xcf, 0xfc, 0x0, 0x0,
    0xcf, 0xfc, 0x0, 0x0, 0xcf, 0xfc, 0x0, 0x0,
    0xcf, 0xfc, 0x0, 0x0, 0xcf, 0xfc, 0x0, 0x0,
    0xcf, 0xfc, 0x0, 0x0, 0xcf, 0xfc, 0x0, 0x0,
    0xcf, 0xfc, 0x0, 0x0, 0xcf, 0xfc, 0x0, 0x0,
    0xcf, 0xfc, 0x0, 0x0, 0xcf, 0xfc, 0x0, 0x0,
    0xcf, 0xfc, 0x0, 0x0, 0xcf, 0xfc, 0x0, 0x0,
    0xcf, 0xfc, 0x0, 0x0, 0xcf, 0xfc, 0x0, 0x0,
    0xcf, 0xfc, 0x0, 0x0, 0xcf, 0xfc, 0x0, 0x0,
    0xcf, 0xfc, 0x0, 0x0, 0xcf, 0xfc, 0x0, 0x0,
    0xcf, 0xfc, 0x0, 0x0, 0xcf, 0xfe, 0x99, 0x95,
    0xcf, 0xff, 0xff, 0xf9, 0xcf, 0xff, 0xff, 0xf9,

    /* U+005C "\\" */
    0xf, 0xff, 0x10, 0x0, 0x0, 0x0, 0xb, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x8, 0xff, 0x90, 0x0,
    0x0, 0x0, 0x4, 0xff, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xf1, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xf9, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xff, 0x10, 0x0, 0x0, 0x0, 0xc,
    0xff, 0x50, 0x0, 0x0, 0x0, 0x8, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x4, 0xff, 0xd0, 0x0, 0x0,
    0x0, 0x1, 0xff, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xff, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xff, 0x40, 0x0, 0x0, 0x0, 0x9, 0xff,
    0x80, 0x0, 0x0, 0x0, 0x5, 0xff, 0xc0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0xdf, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x2f, 0xff, 0x0, 0x0, 0x0,
    0x0, 0xe, 0xff, 0x40, 0x0, 0x0, 0x0, 0xa,
    0xff, 0x70, 0x0, 0x0, 0x0, 0x6, 0xff, 0xb0,
    0x0, 0x0, 0x0, 0x2, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xf3, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x18, 0x87,

    /* U+005D "]" */
    0x7f, 0xff, 0xff, 0xfd, 0x7f, 0xff, 0xff, 0xfd,
    0x4a, 0xaa, 0xdf, 0xfd, 0x0, 0x0, 0xaf, 0xfd,
    0x0, 0x0, 0xaf, 0xfd, 0x0, 0x0, 0xaf, 0xfd,
    0x0, 0x0, 0xaf, 0xfd, 0x0, 0x0, 0xaf, 0xfd,
    0x0, 0x0, 0xaf, 0xfd, 0x0, 0x0, 0xaf, 0xfd,
    0x0, 0x0, 0xaf, 0xfd, 0x0, 0x0, 0xaf, 0xfd,
    0x0, 0x0, 0xaf, 0xfd, 0x0, 0x0, 0xaf, 0xfd,
    0x0, 0x0, 0xaf, 0xfd, 0x0, 0x0, 0xaf, 0xfd,
    0x0, 0x0, 0xaf, 0xfd, 0x0, 0x0, 0xaf, 0xfd,
    0x0, 0x0, 0xaf, 0xfd, 0x0, 0x0, 0xaf, 0xfd,
    0x0, 0x0, 0xaf, 0xfd, 0x0, 0x0, 0xaf, 0xfd,
    0x0, 0x0, 0xaf, 0xfd, 0x0, 0x0, 0xaf, 0xfd,
    0x0, 0x0, 0xaf, 0xfd, 0x0, 0x0, 0xaf, 0xfd,
    0x0, 0x0, 0xaf, 0xfd, 0x0, 0x0, 0xaf, 0xfd,
    0x0, 0x0, 0xaf, 0xfd, 0x49, 0x99, 0xdf, 0xfd,
    0x7f, 0xff, 0xff, 0xfd, 0x7f, 0xff, 0xff, 0xfd,

    /* U+005E "^" */
    0x0, 0x0, 0x0, 0xaf, 0xff, 0x80, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xff, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xff, 0xff, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xff, 0xff, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xff, 0xdf, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xff, 0x3f, 0xff, 0x60, 0x0,
    0x0, 0x0, 0xef, 0xfb, 0xd, 0xff, 0xc0, 0x0,
    0x0, 0x4, 0xff, 0xf5, 0x8, 0xff, 0xf2, 0x0,
    0x0, 0xa, 0xff, 0xf1, 0x2, 0xff, 0xf8, 0x0,
    0x0, 0xf, 0xff, 0xb0, 0x0, 0xdf, 0xfe, 0x0,
    0x0, 0x6f, 0xff, 0x60, 0x0, 0x8f, 0xff, 0x40,
    0x0, 0xcf, 0xff, 0x10, 0x0, 0x2f, 0xff, 0x90,
    0x2, 0xff, 0xfb, 0x0, 0x0, 0xd, 0xff, 0xf0,
    0x8, 0xff, 0xf5, 0x0, 0x0, 0x7, 0xff, 0xf5,
    0xe, 0xff, 0xf0, 0x0, 0x0, 0x2, 0xff, 0xfb,

    /* U+005F "_" */
    0x7d, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd,
    0xd9, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfb, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfb,

    /* U+0060 "`" */
    0x0, 0x4, 0x0, 0x0, 0x0, 0x0, 0x5f, 0x90,
    0x0, 0x0, 0x5, 0xff, 0xf6, 0x0, 0x0, 0x3f,
    0xff, 0xff, 0x30, 0x0, 0x8, 0xff, 0xff, 0xe1,
    0x0, 0x0, 0x6f, 0xff, 0xfd, 0x0, 0x0, 0x5,
    0xff, 0xff, 0xa0, 0x0, 0x0, 0x4f, 0xff, 0xc0,
    0x0, 0x0, 0x3, 0xfc, 0x10, 0x0, 0x0, 0x0,
    0x21, 0x0,

    /* U+0061 "a" */
    0x0, 0x0, 0x27, 0xce, 0xff, 0xd8, 0x10, 0x0,
    0x0, 0x3b, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x0,
    0x7, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x30,
    0x2, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0,
    0x0, 0xaf, 0xc6, 0x10, 0x4, 0xef, 0xff, 0xf2,
    0x0, 0x15, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xff, 0xf8,
    0x0, 0x0, 0x0, 0x37, 0xac, 0xff, 0xff, 0xf9,
    0x0, 0x1, 0x8e, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0x0, 0x5f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0x5, 0xff, 0xff, 0xf9, 0x41, 0x1f, 0xff, 0xfa,
    0xe, 0xff, 0xfd, 0x10, 0x0, 0x1f, 0xff, 0xfa,
    0x3f, 0xff, 0xf4, 0x0, 0x0, 0x1f, 0xff, 0xfa,
    0x5f, 0xff, 0xf2, 0x0, 0x0, 0x3f, 0xff, 0xfa,
    0x4f, 0xff, 0xfa, 0x0, 0x6, 0xff, 0xff, 0xfa,
    0x1f, 0xff, 0xff, 0xfd, 0xff, 0xff, 0xff, 0xfa,
    0x9, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xfa,
    0x0, 0xcf, 0xff, 0xff, 0xfe, 0x56, 0xff, 0xfa,
    0x0, 0x7, 0xdf, 0xfc, 0x70, 0x4, 0xff, 0xfa,

    /* U+0062 "b" */
    0x7f, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xff, 0xf3, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xff, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff,
    0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f,
    0xff, 0xf2, 0x6, 0xce, 0xfd, 0x81, 0x0, 0x0,
    0x7f, 0xff, 0xf4, 0xdf, 0xff, 0xff, 0xff, 0x40,
    0x0, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf3, 0x0, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfc, 0x0, 0x7f, 0xff, 0xff, 0xc4, 0x1,
    0x7f, 0xff, 0xff, 0x40, 0x7f, 0xff, 0xf9, 0x0,
    0x0, 0x8, 0xff, 0xff, 0x90, 0x7f, 0xff, 0xf3,
    0x0, 0x0, 0x1, 0xff, 0xff, 0xe0, 0x7f, 0xff,
    0xf3, 0x0, 0x0, 0x0, 0xdf, 0xff, 0xf0, 0x7f,
    0xff, 0xf3, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xf1,
    0x7f, 0xff, 0xf3, 0x0, 0x0, 0x0, 0xbf, 0xff,
    0xf1, 0x7f, 0xff, 0xf3, 0x0, 0x0, 0x0, 0xcf,
    0xff, 0xf1, 0x7f, 0xff, 0xf3, 0x0, 0x0, 0x0,
    0xef, 0xff, 0xf0, 0x7f, 0xff, 0xf3, 0x0, 0x0,
    0x3, 0xff, 0xff, 0xc0, 0x7f, 0xff, 0xf4, 0x0,
    0x0, 0xc, 0xff, 0xff, 0x70, 0x7f, 0xff, 0xff,
    0x72, 0x2, 0xbf, 0xff, 0xff, 0x10, 0x7f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x0, 0x7f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb0, 0x0,
    0x7f, 0xff, 0x76, 0xff, 0xff, 0xff, 0xf9, 0x0,
    0x0, 0x7f, 0xff, 0x40, 0x29, 0xef, 0xea, 0x30,
    0x0, 0x0,

    /* U+0063 "c" */
    0x0, 0x0, 0x1, 0x7c, 0xef, 0xeb, 0x60, 0x0,
    0x0, 0x7, 0xff, 0xff, 0xff, 0xff, 0xd2, 0x0,
    0xb, 0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0xa,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xb0, 0x5, 0xff,
    0xff, 0xf9, 0x20, 0x17, 0xd1, 0x0, 0xdf, 0xff,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xff, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xff, 0xff, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x8f, 0xff, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xff, 0xff, 0x70, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xff, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xff, 0xf7, 0x0, 0x0, 0x1,
    0x10, 0x7, 0xff, 0xff, 0xf9, 0x20, 0x16, 0xea,
    0x0, 0xc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0x0, 0x2d, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0,
    0x0, 0x1b, 0xff, 0xff, 0xff, 0xff, 0xd2, 0x0,
    0x0, 0x4, 0x9d, 0xff, 0xda, 0x40, 0x0,

    /* U+0064 "d" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xff,
    0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xff, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xff, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xff, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xff, 0xf1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0xff, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xf1, 0x0,
    0x0, 0x7, 0xcf, 0xfc, 0x60, 0x8f, 0xff, 0xf1,
    0x0, 0x2, 0xdf, 0xff, 0xff, 0xfd, 0x9f, 0xff,
    0xf1, 0x0, 0x2e, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf1, 0x0, 0xcf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf1, 0x6, 0xff, 0xff, 0xf7, 0x10,
    0x4b, 0xff, 0xff, 0xf1, 0xd, 0xff, 0xff, 0x60,
    0x0, 0x0, 0xaf, 0xff, 0xf1, 0x2f, 0xff, 0xfd,
    0x0, 0x0, 0x0, 0x9f, 0xff, 0xf1, 0x5f, 0xff,
    0xf8, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xf1, 0x7f,
    0xff, 0xf6, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xf1,
    0x7f, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x9f, 0xff,
    0xf1, 0x7f, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x9f,
    0xff, 0xf1, 0x6f, 0xff, 0xf8, 0x0, 0x0, 0x0,
    0x9f, 0xff, 0xf1, 0x3f, 0xff, 0xfc, 0x0, 0x0,
    0x0, 0x9f, 0xff, 0xf1, 0xf, 0xff, 0xff, 0x40,
    0x0, 0x1, 0xdf, 0xff, 0xf1, 0x9, 0xff, 0xff,
    0xe5, 0x0, 0x4d, 0xff, 0xff, 0xf1, 0x2, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0x0,
    0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1,
    0x0, 0x7, 0xff, 0xff, 0xff, 0xfc, 0x2f, 0xff,
    0xf1, 0x0, 0x0, 0x29, 0xdf, 0xeb, 0x50, 0xd,
    0xff, 0xf1,

    /* U+0065 "e" */
    0x0, 0x0, 0x3, 0x9d, 0xff, 0xd8, 0x10, 0x0,
    0x0, 0x0, 0xa, 0xff, 0xff, 0xff, 0xff, 0x50,
    0x0, 0x0, 0x1d, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x50, 0x0, 0xb, 0xff, 0xff, 0xfd, 0xdf, 0xff,
    0xfe, 0x0, 0x5, 0xff, 0xff, 0xa1, 0x0, 0x2d,
    0xff, 0xf7, 0x0, 0xdf, 0xff, 0xb0, 0x0, 0x0,
    0x4f, 0xff, 0xc0, 0x2f, 0xff, 0xf3, 0x0, 0x0,
    0x0, 0xff, 0xff, 0x6, 0xff, 0xff, 0x44, 0x44,
    0x44, 0x4e, 0xff, 0xf2, 0x8f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x39, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf3, 0x8f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x16, 0xff, 0xff,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff,
    0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef,
    0xff, 0xe1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xff, 0xff, 0xe5, 0x0, 0x0, 0x3b, 0x30, 0x0,
    0xb, 0xff, 0xff, 0xff, 0xdd, 0xff, 0xfc, 0x0,
    0x0, 0x1d, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf5,
    0x0, 0x0, 0xa, 0xff, 0xff, 0xff, 0xff, 0xf9,
    0x10, 0x0, 0x0, 0x3, 0x9d, 0xff, 0xeb, 0x71,
    0x0, 0x0,

    /* U+0066 "f" */
    0x0, 0x0, 0x0, 0x7c, 0xef, 0xec, 0x70, 0x0,
    0x2, 0xdf, 0xff, 0xff, 0xfb, 0x0, 0x0, 0xcf,
    0xff, 0xff, 0xff, 0x70, 0x0, 0x4f, 0xff, 0xff,
    0xfe, 0xf4, 0x0, 0x8, 0xff, 0xff, 0x80, 0x0,
    0x0, 0x0, 0xaf, 0xff, 0xf0, 0x0, 0x0, 0x0,
    0xa, 0xff, 0xff, 0x0, 0x0, 0x1, 0xde, 0xff,
    0xff, 0xff, 0xff, 0x60, 0x1f, 0xff, 0xff, 0xff,
    0xff, 0xf6, 0x1, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x60, 0x1d, 0xdf, 0xff, 0xff, 0xdd, 0xd5, 0x0,
    0x0, 0xaf, 0xff, 0xf0, 0x0, 0x0, 0x0, 0xa,
    0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff,
    0xf0, 0x0, 0x0, 0x0, 0xa, 0xff, 0xff, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0xa, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0xff, 0xf0, 0x0, 0x0, 0x0, 0xa, 0xff,
    0xff, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0xa, 0xff, 0xff, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0xff, 0xf0, 0x0, 0x0, 0x0,
    0xa, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0xff, 0xf0, 0x0, 0x0, 0x0, 0xa, 0xff, 0xff,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0xff, 0xf0, 0x0,
    0x0,

    /* U+0067 "g" */
    0x0, 0x0, 0x39, 0xdf, 0xff, 0xff, 0xff, 0xff,
    0xf4, 0x0, 0x1a, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf4, 0x0, 0xcf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf4, 0x8, 0xff, 0xff, 0xd7, 0x6b,
    0xff, 0xff, 0xb9, 0x92, 0xe, 0xff, 0xfc, 0x0,
    0x0, 0x9f, 0xff, 0xb0, 0x0, 0x1f, 0xff, 0xf6,
    0x0, 0x0, 0x2f, 0xff, 0xe0, 0x0, 0xf, 0xff,
    0xf5, 0x0, 0x0, 0x2f, 0xff, 0xf0, 0x0, 0xd,
    0xff, 0xfa, 0x0, 0x0, 0x6f, 0xff, 0xd0, 0x0,
    0x7, 0xff, 0xff, 0x81, 0x16, 0xff, 0xff, 0x80,
    0x0, 0x0, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0x10, 0x0, 0x0, 0xe, 0xff, 0xff, 0xff, 0xff,
    0xd2, 0x0, 0x0, 0x0, 0x9f, 0xfd, 0xcf, 0xfe,
    0xb6, 0x0, 0x0, 0x0, 0x4, 0xff, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff,
    0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xff, 0xec, 0xcc, 0xcc, 0xb8, 0x40, 0x0,
    0x2, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x30, 0x0, 0x5f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xe1, 0x2, 0xdf, 0xfd, 0xef, 0xff, 0xff,
    0xff, 0xff, 0xf6, 0x1e, 0xff, 0xb0, 0x0, 0x0,
    0x1, 0x8f, 0xff, 0xf9, 0x7f, 0xff, 0x50, 0x0,
    0x0, 0x0, 0xf, 0xff, 0xf8, 0x9f, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x5f, 0xff, 0xf3, 0x7f, 0xff,
    0xfa, 0x53, 0x23, 0x5b, 0xff, 0xff, 0xa0, 0x1e,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x0,
    0x2, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x60,
    0x0, 0x0, 0x4, 0x9c, 0xef, 0xfe, 0xc9, 0x40,
    0x0, 0x0,

    /* U+0068 "h" */
    0x7f, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xff, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xff, 0xff, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xff, 0xf3, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xff, 0xff, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xff, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xff, 0xff, 0x20, 0x29,
    0xdf, 0xeb, 0x40, 0x0, 0x7f, 0xff, 0xf1, 0x8f,
    0xff, 0xff, 0xff, 0x60, 0x7, 0xff, 0xff, 0xbf,
    0xff, 0xff, 0xff, 0xff, 0x20, 0x7f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf9, 0x7, 0xff, 0xff,
    0xff, 0x83, 0x4b, 0xff, 0xff, 0xd0, 0x7f, 0xff,
    0xfe, 0x20, 0x0, 0xe, 0xff, 0xff, 0x7, 0xff,
    0xff, 0x40, 0x0, 0x0, 0xaf, 0xff, 0xf1, 0x7f,
    0xff, 0xf3, 0x0, 0x0, 0x8, 0xff, 0xff, 0x27,
    0xff, 0xff, 0x30, 0x0, 0x0, 0x8f, 0xff, 0xf2,
    0x7f, 0xff, 0xf3, 0x0, 0x0, 0x8, 0xff, 0xff,
    0x27, 0xff, 0xff, 0x30, 0x0, 0x0, 0x8f, 0xff,
    0xf2, 0x7f, 0xff, 0xf3, 0x0, 0x0, 0x8, 0xff,
    0xff, 0x27, 0xff, 0xff, 0x30, 0x0, 0x0, 0x8f,
    0xff, 0xf2, 0x7f, 0xff, 0xf3, 0x0, 0x0, 0x8,
    0xff, 0xff, 0x27, 0xff, 0xff, 0x30, 0x0, 0x0,
    0x8f, 0xff, 0xf2, 0x7f, 0xff, 0xf3, 0x0, 0x0,
    0x8, 0xff, 0xff, 0x27, 0xff, 0xff, 0x30, 0x0,
    0x0, 0x8f, 0xff, 0xf2, 0x7f, 0xff, 0xf3, 0x0,
    0x0, 0x8, 0xff, 0xff, 0x27, 0xff, 0xff, 0x30,
    0x0, 0x0, 0x8f, 0xff, 0xf2,

    /* U+0069 "i" */
    0xa, 0xee, 0x70, 0x8f, 0xff, 0xf5, 0xcf, 0xff,
    0xf8, 0xaf, 0xff, 0xf7, 0x2e, 0xff, 0xc0, 0x0,
    0x33, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xf3,
    0x7f, 0xff, 0xf3, 0x7f, 0xff, 0xf3, 0x7f, 0xff,
    0xf3, 0x7f, 0xff, 0xf3, 0x7f, 0xff, 0xf3, 0x7f,
    0xff, 0xf3, 0x7f, 0xff, 0xf3, 0x7f, 0xff, 0xf3,
    0x7f, 0xff, 0xf3, 0x7f, 0xff, 0xf3, 0x7f, 0xff,
    0xf3, 0x7f, 0xff, 0xf3, 0x7f, 0xff, 0xf3, 0x7f,
    0xff, 0xf3, 0x7f, 0xff, 0xf3, 0x7f, 0xff, 0xf3,
    0x7f, 0xff, 0xf3, 0x7f, 0xff, 0xf3,

    /* U+006A "j" */
    0x0, 0x0, 0x9, 0xee, 0x70, 0x0, 0x0, 0x8f,
    0xff, 0xf5, 0x0, 0x0, 0xcf, 0xff, 0xf9, 0x0,
    0x0, 0xaf, 0xff, 0xf7, 0x0, 0x0, 0x2e, 0xff,
    0xc0, 0x0, 0x0, 0x0, 0x33, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xf4,
    0x0, 0x0, 0x7f, 0xff, 0xf4, 0x0, 0x0, 0x7f,
    0xff, 0xf4, 0x0, 0x0, 0x7f, 0xff, 0xf4, 0x0,
    0x0, 0x7f, 0xff, 0xf4, 0x0, 0x0, 0x7f, 0xff,
    0xf4, 0x0, 0x0, 0x7f, 0xff, 0xf4, 0x0, 0x0,
    0x7f, 0xff, 0xf4, 0x0, 0x0, 0x7f, 0xff, 0xf4,
    0x0, 0x0, 0x7f, 0xff, 0xf4, 0x0, 0x0, 0x7f,
    0xff, 0xf4, 0x0, 0x0, 0x7f, 0xff, 0xf4, 0x0,
    0x0, 0x7f, 0xff, 0xf4, 0x0, 0x0, 0x7f, 0xff,
    0xf4, 0x0, 0x0, 0x7f, 0xff, 0xf4, 0x0, 0x0,
    0x7f, 0xff, 0xf4, 0x0, 0x0, 0x7f, 0xff, 0xf4,
    0x0, 0x0, 0x7f, 0xff, 0xf4, 0x0, 0x0, 0x7f,
    0xff, 0xf4, 0x0, 0x0, 0x7f, 0xff, 0xf4, 0x0,
    0x0, 0x9f, 0xff, 0xf2, 0x0, 0x1, 0xef, 0xff,
    0xf0, 0xf, 0xdf, 0xff, 0xff, 0xb0, 0x4f, 0xff,
    0xff, 0xff, 0x30, 0x8f, 0xff, 0xff, 0xf6, 0x0,
    0x6d, 0xff, 0xda, 0x30, 0x0,

    /* U+006B "k" */
    0x7f, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xff, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xff, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xf2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff,
    0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f,
    0xff, 0xf2, 0x0, 0x0, 0x1e, 0xff, 0xff, 0x30,
    0x7f, 0xff, 0xf2, 0x0, 0x0, 0xbf, 0xff, 0xf6,
    0x0, 0x7f, 0xff, 0xf2, 0x0, 0x7, 0xff, 0xff,
    0x90, 0x0, 0x7f, 0xff, 0xf2, 0x0, 0x3f, 0xff,
    0xfc, 0x0, 0x0, 0x7f, 0xff, 0xf2, 0x1, 0xef,
    0xff, 0xe1, 0x0, 0x0, 0x7f, 0xff, 0xf2, 0xb,
    0xff, 0xff, 0x40, 0x0, 0x0, 0x7f, 0xff, 0xf2,
    0x7f, 0xff, 0xf7, 0x0, 0x0, 0x0, 0x7f, 0xff,
    0xf6, 0xff, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x7f,
    0xff, 0xff, 0xff, 0xff, 0xc0, 0x0, 0x0, 0x0,
    0x7f, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x0, 0x0,
    0x0, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x10,
    0x0, 0x0, 0x7f, 0xff, 0xff, 0xf7, 0xff, 0xff,
    0xa0, 0x0, 0x0, 0x7f, 0xff, 0xff, 0x80, 0x9f,
    0xff, 0xf3, 0x0, 0x0, 0x7f, 0xff, 0xfb, 0x0,
    0x1f, 0xff, 0xfd, 0x0, 0x0, 0x7f, 0xff, 0xf2,
    0x0, 0x7, 0xff, 0xff, 0x60, 0x0, 0x7f, 0xff,
    0xf2, 0x0, 0x0, 0xef, 0xff, 0xf1, 0x0, 0x7f,
    0xff, 0xf2, 0x0, 0x0, 0x5f, 0xff, 0xfa, 0x0,
    0x7f, 0xff, 0xf2, 0x0, 0x0, 0xc, 0xff, 0xff,
    0x30, 0x7f, 0xff, 0xf2, 0x0, 0x0, 0x4, 0xff,
    0xff, 0xd0,

    /* U+006C "l" */
    0x7f, 0xff, 0xf3, 0x0, 0x7f, 0xff, 0xf3, 0x0,
    0x7f, 0xff, 0xf3, 0x0, 0x7f, 0xff, 0xf3, 0x0,
    0x7f, 0xff, 0xf3, 0x0, 0x7f, 0xff, 0xf3, 0x0,
    0x7f, 0xff, 0xf3, 0x0, 0x7f, 0xff, 0xf3, 0x0,
    0x7f, 0xff, 0xf3, 0x0, 0x7f, 0xff, 0xf3, 0x0,
    0x7f, 0xff, 0xf3, 0x0, 0x7f, 0xff, 0xf3, 0x0,
    0x7f, 0xff, 0xf3, 0x0, 0x7f, 0xff, 0xf3, 0x0,
    0x7f, 0xff, 0xf3, 0x0, 0x7f, 0xff, 0xf3, 0x0,
    0x7f, 0xff, 0xf3, 0x0, 0x7f, 0xff, 0xf3, 0x0,
    0x7f, 0xff, 0xf3, 0x0, 0x7f, 0xff, 0xf3, 0x0,
    0x7f, 0xff, 0xf3, 0x0, 0x5f, 0xff, 0xf6, 0x0,
    0x3f, 0xff, 0xff, 0xc0, 0xe, 0xff, 0xff, 0xf0,
    0x6, 0xff, 0xff, 0xf3, 0x0, 0x6c, 0xff, 0xd3,

    /* U+006D "m" */
    0x7f, 0xff, 0x60, 0x4, 0xbe, 0xfd, 0x91, 0x0,
    0x0, 0x6c, 0xff, 0xc6, 0x0, 0x7, 0xff, 0xf8,
    0xa, 0xff, 0xff, 0xff, 0xe2, 0x2, 0xcf, 0xff,
    0xff, 0xfb, 0x0, 0x7f, 0xff, 0xbc, 0xff, 0xff,
    0xff, 0xff, 0xd3, 0xef, 0xff, 0xff, 0xff, 0xf6,
    0x7, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xd0, 0x7f, 0xff,
    0xff, 0xf7, 0x35, 0xef, 0xff, 0xff, 0xfc, 0x53,
    0x8f, 0xff, 0xff, 0x27, 0xff, 0xff, 0xd2, 0x0,
    0x3, 0xff, 0xff, 0xfa, 0x0, 0x0, 0x9f, 0xff,
    0xf5, 0x7f, 0xff, 0xf4, 0x0, 0x0, 0xe, 0xff,
    0xfe, 0x0, 0x0, 0x4, 0xff, 0xff, 0x67, 0xff,
    0xff, 0x30, 0x0, 0x0, 0xdf, 0xff, 0xd0, 0x0,
    0x0, 0x3f, 0xff, 0xf7, 0x7f, 0xff, 0xf3, 0x0,
    0x0, 0xd, 0xff, 0xfd, 0x0, 0x0, 0x3, 0xff,
    0xff, 0x77, 0xff, 0xff, 0x30, 0x0, 0x0, 0xdf,
    0xff, 0xd0, 0x0, 0x0, 0x3f, 0xff, 0xf7, 0x7f,
    0xff, 0xf3, 0x0, 0x0, 0xd, 0xff, 0xfd, 0x0,
    0x0, 0x3, 0xff, 0xff, 0x77, 0xff, 0xff, 0x30,
    0x0, 0x0, 0xdf, 0xff, 0xd0, 0x0, 0x0, 0x3f,
    0xff, 0xf7, 0x7f, 0xff, 0xf3, 0x0, 0x0, 0xd,
    0xff, 0xfd, 0x0, 0x0, 0x3, 0xff, 0xff, 0x77,
    0xff, 0xff, 0x30, 0x0, 0x0, 0xdf, 0xff, 0xd0,
    0x0, 0x0, 0x3f, 0xff, 0xf7, 0x7f, 0xff, 0xf3,
    0x0, 0x0, 0xd, 0xff, 0xfd, 0x0, 0x0, 0x3,
    0xff, 0xff, 0x77, 0xff, 0xff, 0x30, 0x0, 0x0,
    0xdf, 0xff, 0xd0, 0x0, 0x0, 0x3f, 0xff, 0xf7,
    0x7f, 0xff, 0xf3, 0x0, 0x0, 0xd, 0xff, 0xfd,
    0x0, 0x0, 0x3, 0xff, 0xff, 0x77, 0xff, 0xff,
    0x30, 0x0, 0x0, 0xdf, 0xff, 0xd0, 0x0, 0x0,
    0x3f, 0xff, 0xf7, 0x7f, 0xff, 0xf3, 0x0, 0x0,
    0xd, 0xff, 0xfd, 0x0, 0x0, 0x3, 0xff, 0xff,
    0x70,

    /* U+006E "n" */
    0x7f, 0xff, 0x60, 0x3, 0x9d, 0xfe, 0xb4, 0x0,
    0x7, 0xff, 0xf8, 0x9, 0xff, 0xff, 0xff, 0xf6,
    0x0, 0x7f, 0xff, 0xcc, 0xff, 0xff, 0xff, 0xff,
    0xf2, 0x7, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x90, 0x7f, 0xff, 0xff, 0xf8, 0x34, 0xbf,
    0xff, 0xfd, 0x7, 0xff, 0xff, 0xe2, 0x0, 0x0,
    0xef, 0xff, 0xf0, 0x7f, 0xff, 0xf4, 0x0, 0x0,
    0xa, 0xff, 0xff, 0x17, 0xff, 0xff, 0x30, 0x0,
    0x0, 0x8f, 0xff, 0xf2, 0x7f, 0xff, 0xf3, 0x0,
    0x0, 0x8, 0xff, 0xff, 0x27, 0xff, 0xff, 0x30,
    0x0, 0x0, 0x8f, 0xff, 0xf2, 0x7f, 0xff, 0xf3,
    0x0, 0x0, 0x8, 0xff, 0xff, 0x27, 0xff, 0xff,
    0x30, 0x0, 0x0, 0x8f, 0xff, 0xf2, 0x7f, 0xff,
    0xf3, 0x0, 0x0, 0x8, 0xff, 0xff, 0x27, 0xff,
    0xff, 0x30, 0x0, 0x0, 0x8f, 0xff, 0xf2, 0x7f,
    0xff, 0xf3, 0x0, 0x0, 0x8, 0xff, 0xff, 0x27,
    0xff, 0xff, 0x30, 0x0, 0x0, 0x8f, 0xff, 0xf2,
    0x7f, 0xff, 0xf3, 0x0, 0x0, 0x8, 0xff, 0xff,
    0x27, 0xff, 0xff, 0x30, 0x0, 0x0, 0x8f, 0xff,
    0xf2, 0x7f, 0xff, 0xf3, 0x0, 0x0, 0x8, 0xff,
    0xff, 0x20,

    /* U+006F "o" */
    0x0, 0x0, 0x3, 0x9d, 0xff, 0xd9, 0x30, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xff, 0xff, 0xff, 0xfa,
    0x0, 0x0, 0x0, 0x1d, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xd1, 0x0, 0x0, 0xbf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfb, 0x0, 0x6, 0xff, 0xff, 0xe5,
    0x0, 0x5e, 0xff, 0xff, 0x70, 0xe, 0xff, 0xff,
    0x30, 0x0, 0x3, 0xff, 0xff, 0xe0, 0x3f, 0xff,
    0xfc, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xf3, 0x6f,
    0xff, 0xf6, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xf7,
    0x8f, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x4f, 0xff,
    0xf9, 0x9f, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x3f,
    0xff, 0xf9, 0x8f, 0xff, 0xf4, 0x0, 0x0, 0x0,
    0x4f, 0xff, 0xf9, 0x6f, 0xff, 0xf6, 0x0, 0x0,
    0x0, 0x6f, 0xff, 0xf7, 0x3f, 0xff, 0xfc, 0x0,
    0x0, 0x0, 0xbf, 0xff, 0xf3, 0xe, 0xff, 0xff,
    0x30, 0x0, 0x3, 0xff, 0xff, 0xe0, 0x6, 0xff,
    0xff, 0xe5, 0x0, 0x5e, 0xff, 0xff, 0x70, 0x0,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x0,
    0x0, 0x1d, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd1,
    0x0, 0x0, 0x0, 0xaf, 0xff, 0xff, 0xff, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x3, 0x9d, 0xff, 0xd9,
    0x30, 0x0, 0x0,

    /* U+0070 "p" */
    0x7f, 0xff, 0x70, 0x6, 0xce, 0xfd, 0x81, 0x0,
    0x0, 0x7f, 0xff, 0x95, 0xef, 0xff, 0xff, 0xff,
    0x40, 0x0, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf3, 0x0, 0x7f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0x0, 0x7f, 0xff, 0xff, 0xc4,
    0x1, 0x7f, 0xff, 0xff, 0x40, 0x7f, 0xff, 0xf9,
    0x0, 0x0, 0x8, 0xff, 0xff, 0x90, 0x7f, 0xff,
    0xf3, 0x0, 0x0, 0x1, 0xff, 0xff, 0xe0, 0x7f,
    0xff, 0xf3, 0x0, 0x0, 0x0, 0xdf, 0xff, 0xf0,
    0x7f, 0xff, 0xf3, 0x0, 0x0, 0x0, 0xbf, 0xff,
    0xf1, 0x7f, 0xff, 0xf3, 0x0, 0x0, 0x0, 0xbf,
    0xff, 0xf1, 0x7f, 0xff, 0xf3, 0x0, 0x0, 0x0,
    0xcf, 0xff, 0xf1, 0x7f, 0xff, 0xf3, 0x0, 0x0,
    0x0, 0xef, 0xff, 0xf0, 0x7f, 0xff, 0xf3, 0x0,
    0x0, 0x3, 0xff, 0xff, 0xc0, 0x7f, 0xff, 0xf4,
    0x0, 0x0, 0xc, 0xff, 0xff, 0x70, 0x7f, 0xff,
    0xff, 0x82, 0x2, 0xbf, 0xff, 0xff, 0x10, 0x7f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x0,
    0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb0,
    0x0, 0x7f, 0xff, 0xf7, 0xff, 0xff, 0xff, 0xf9,
    0x0, 0x0, 0x7f, 0xff, 0xf2, 0x19, 0xdf, 0xea,
    0x30, 0x0, 0x0, 0x7f, 0xff, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff,
    0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f,
    0xff, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+0071 "q" */
    0x0, 0x0, 0x7, 0xcf, 0xfd, 0x81, 0xa, 0xff,
    0xf1, 0x0, 0x2, 0xdf, 0xff, 0xff, 0xfe, 0x4d,
    0xff, 0xf1, 0x0, 0x2e, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf1, 0x0, 0xcf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf1, 0x6, 0xff, 0xff, 0xf7,
    0x10, 0x4b, 0xff, 0xff, 0xf1, 0xd, 0xff, 0xff,
    0x60, 0x0, 0x0, 0xaf, 0xff, 0xf1, 0x2f, 0xff,
    0xfd, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xf1, 0x5f,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xf1,
    0x7f, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x9f, 0xff,
    0xf1, 0x7f, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x9f,
    0xff, 0xf1, 0x7f, 0xff, 0xf6, 0x0, 0x0, 0x0,
    0x9f, 0xff, 0xf1, 0x6f, 0xff, 0xf8, 0x0, 0x0,
    0x0, 0x9f, 0xff, 0xf1, 0x3f, 0xff, 0xfc, 0x0,
    0x0, 0x0, 0x9f, 0xff, 0xf1, 0xf, 0xff, 0xff,
    0x40, 0x0, 0x1, 0xdf, 0xff, 0xf1, 0x9, 0xff,
    0xff, 0xe5, 0x0, 0x4d, 0xff, 0xff, 0xf1, 0x2,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1,
    0x0, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf1, 0x0, 0x7, 0xff, 0xff, 0xff, 0xfa, 0x8f,
    0xff, 0xf1, 0x0, 0x0, 0x29, 0xdf, 0xeb, 0x40,
    0x8f, 0xff, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xff, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xff, 0xf1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0xff, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xff,
    0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xff, 0xf1,

    /* U+0072 "r" */
    0x7f, 0xff, 0x60, 0x3, 0xbe, 0xfe, 0x7f, 0xff,
    0x80, 0x7f, 0xff, 0xfb, 0x7f, 0xff, 0x96, 0xff,
    0xff, 0xf9, 0x7f, 0xff, 0xdf, 0xff, 0xff, 0xf6,
    0x7f, 0xff, 0xff, 0xfe, 0x75, 0x72, 0x7f, 0xff,
    0xff, 0xb0, 0x0, 0x0, 0x7f, 0xff, 0xfe, 0x0,
    0x0, 0x0, 0x7f, 0xff, 0xf6, 0x0, 0x0, 0x0,
    0x7f, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x7f, 0xff,
    0xf3, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xf3, 0x0,
    0x0, 0x0, 0x7f, 0xff, 0xf3, 0x0, 0x0, 0x0,
    0x7f, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x7f, 0xff,
    0xf3, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xf3, 0x0,
    0x0, 0x0, 0x7f, 0xff, 0xf3, 0x0, 0x0, 0x0,
    0x7f, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x7f, 0xff,
    0xf3, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xf3, 0x0,
    0x0, 0x0,

    /* U+0073 "s" */
    0x0, 0x0, 0x17, 0xce, 0xfe, 0xc7, 0x10, 0x0,
    0x0, 0x4e, 0xff, 0xff, 0xff, 0xff, 0x70, 0x0,
    0x3f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x50, 0xc,
    0xff, 0xff, 0xdb, 0xcf, 0xff, 0xb0, 0x0, 0xff,
    0xff, 0x90, 0x0, 0x19, 0xe1, 0x0, 0x2f, 0xff,
    0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff,
    0xb1, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xff,
    0xf9, 0x30, 0x0, 0x0, 0x0, 0x1e, 0xff, 0xff,
    0xff, 0xc5, 0x0, 0x0, 0x0, 0x1b, 0xff, 0xff,
    0xff, 0xfc, 0x10, 0x0, 0x0, 0x3, 0xbf, 0xff,
    0xff, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x28, 0xff,
    0xff, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x2, 0xef,
    0xff, 0xa0, 0x2, 0x30, 0x0, 0x0, 0xa, 0xff,
    0xfb, 0x0, 0xcf, 0x91, 0x0, 0x1, 0xef, 0xff,
    0x90, 0x8f, 0xff, 0xfd, 0xbb, 0xff, 0xff, 0xf4,
    0xd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x0,
    0x8, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x0, 0x0,
    0x1, 0x6b, 0xef, 0xed, 0x93, 0x0, 0x0,

    /* U+0074 "t" */
    0x0, 0x0, 0xff, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x4, 0xff,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xe0,
    0x0, 0x0, 0x0, 0x7, 0xff, 0xfe, 0x0, 0x0,
    0x4, 0xde, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x5f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x55, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf5, 0x4d, 0xdf, 0xff, 0xff,
    0xdd, 0xdd, 0x40, 0x0, 0xcf, 0xff, 0xe0, 0x0,
    0x0, 0x0, 0xc, 0xff, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0xff, 0xe0, 0x0, 0x0, 0x0, 0xc,
    0xff, 0xfe, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff,
    0xe0, 0x0, 0x0, 0x0, 0xc, 0xff, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xff, 0xe0, 0x0, 0x0,
    0x0, 0xc, 0xff, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0xff, 0xe0, 0x0, 0x0, 0x0, 0xb, 0xff,
    0xff, 0x10, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xfa,
    0x0, 0x10, 0x0, 0x4, 0xff, 0xff, 0xff, 0xef,
    0x40, 0x0, 0xc, 0xff, 0xff, 0xff, 0xf7, 0x0,
    0x0, 0x2d, 0xff, 0xff, 0xff, 0xb0, 0x0, 0x0,
    0x17, 0xde, 0xfd, 0xa6,

    /* U+0075 "u" */
    0xaf, 0xff, 0xf0, 0x0, 0x0, 0xd, 0xff, 0xfe,
    0xaf, 0xff, 0xf0, 0x0, 0x0, 0xd, 0xff, 0xfe,
    0xaf, 0xff, 0xf0, 0x0, 0x0, 0xd, 0xff, 0xfe,
    0xaf, 0xff, 0xf0, 0x0, 0x0, 0xd, 0xff, 0xfe,
    0xaf, 0xff, 0xf0, 0x0, 0x0, 0xd, 0xff, 0xfe,
    0xaf, 0xff, 0xf0, 0x0, 0x0, 0xd, 0xff, 0xfe,
    0xaf, 0xff, 0xf0, 0x0, 0x0, 0xd, 0xff, 0xfe,
    0xaf, 0xff, 0xf0, 0x0, 0x0, 0xd, 0xff, 0xfe,
    0xaf, 0xff, 0xf0, 0x0, 0x0, 0xd, 0xff, 0xfe,
    0xaf, 0xff, 0xf0, 0x0, 0x0, 0xd, 0xff, 0xfe,
    0xaf, 0xff, 0xf0, 0x0, 0x0, 0xd, 0xff, 0xfe,
    0xaf, 0xff, 0xf0, 0x0, 0x0, 0xd, 0xff, 0xfe,
    0x9f, 0xff, 0xf2, 0x0, 0x0, 0xe, 0xff, 0xfe,
    0x8f, 0xff, 0xf6, 0x0, 0x0, 0xaf, 0xff, 0xfe,
    0x5f, 0xff, 0xff, 0x63, 0x5c, 0xff, 0xff, 0xfe,
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0xa, 0xff, 0xff, 0xff, 0xff, 0xe6, 0xff, 0xfe,
    0x1, 0xdf, 0xff, 0xff, 0xfd, 0x22, 0xff, 0xfe,
    0x0, 0x8, 0xdf, 0xec, 0x60, 0x0, 0xff, 0xfe,

    /* U+0076 "v" */
    0x6f, 0xff, 0xf5, 0x0, 0x0, 0x0, 0xa, 0xff,
    0xfd, 0x1f, 0xff, 0xf9, 0x0, 0x0, 0x0, 0xe,
    0xff, 0xf8, 0xb, 0xff, 0xfd, 0x0, 0x0, 0x0,
    0x3f, 0xff, 0xf3, 0x6, 0xff, 0xff, 0x20, 0x0,
    0x0, 0x8f, 0xff, 0xe0, 0x1, 0xff, 0xff, 0x60,
    0x0, 0x0, 0xcf, 0xff, 0x90, 0x0, 0xcf, 0xff,
    0xb0, 0x0, 0x1, 0xff, 0xff, 0x40, 0x0, 0x7f,
    0xff, 0xf0, 0x0, 0x5, 0xff, 0xfe, 0x0, 0x0,
    0x2f, 0xff, 0xf3, 0x0, 0x9, 0xff, 0xf9, 0x0,
    0x0, 0xd, 0xff, 0xf8, 0x0, 0xe, 0xff, 0xf4,
    0x0, 0x0, 0x7, 0xff, 0xfc, 0x0, 0x2f, 0xff,
    0xf0, 0x0, 0x0, 0x2, 0xff, 0xff, 0x0, 0x6f,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0xdf, 0xff, 0x40,
    0xaf, 0xff, 0x50, 0x0, 0x0, 0x0, 0x8f, 0xff,
    0x80, 0xef, 0xff, 0x10, 0x0, 0x0, 0x0, 0x3f,
    0xff, 0xc2, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0xe, 0xff, 0xf7, 0xff, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xff, 0xff, 0xff, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xff, 0xff, 0xff, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xef, 0xff, 0xff, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xff,
    0x20, 0x0, 0x0,

    /* U+0077 "w" */
    0xf, 0xff, 0xfa, 0x0, 0x0, 0x4, 0xff, 0xff,
    0x10, 0x0, 0x0, 0xcf, 0xff, 0x90, 0xbf, 0xff,
    0xe0, 0x0, 0x0, 0x8f, 0xff, 0xf5, 0x0, 0x0,
    0xf, 0xff, 0xf5, 0x8, 0xff, 0xff, 0x10, 0x0,
    0xb, 0xff, 0xff, 0x90, 0x0, 0x2, 0xff, 0xff,
    0x20, 0x4f, 0xff, 0xf4, 0x0, 0x0, 0xff, 0xff,
    0xfd, 0x0, 0x0, 0x6f, 0xff, 0xe0, 0x0, 0xff,
    0xff, 0x80, 0x0, 0x3f, 0xff, 0xff, 0xf1, 0x0,
    0x9, 0xff, 0xfa, 0x0, 0xc, 0xff, 0xfb, 0x0,
    0x7, 0xff, 0xbf, 0xff, 0x50, 0x0, 0xcf, 0xff,
    0x70, 0x0, 0x8f, 0xff, 0xe0, 0x0, 0xaf, 0xf6,
    0xef, 0xf8, 0x0, 0xf, 0xff, 0xf3, 0x0, 0x4,
    0xff, 0xff, 0x20, 0xe, 0xff, 0x4c, 0xff, 0xc0,
    0x3, 0xff, 0xff, 0x0, 0x0, 0xf, 0xff, 0xf5,
    0x2, 0xff, 0xf1, 0x9f, 0xff, 0x0, 0x6f, 0xff,
    0xb0, 0x0, 0x0, 0xcf, 0xff, 0x90, 0x6f, 0xfe,
    0x5, 0xff, 0xf4, 0x9, 0xff, 0xf8, 0x0, 0x0,
    0x9, 0xff, 0xfb, 0x9, 0xff, 0xb0, 0x2f, 0xff,
    0x70, 0xcf, 0xff, 0x40, 0x0, 0x0, 0x5f, 0xff,
    0xd0, 0xcf, 0xf8, 0x0, 0xff, 0xfa, 0xe, 0xff,
    0xf0, 0x0, 0x0, 0x1, 0xff, 0xff, 0xf, 0xff,
    0x40, 0xb, 0xff, 0xe1, 0xff, 0xfc, 0x0, 0x0,
    0x0, 0xd, 0xff, 0xf5, 0xff, 0xf1, 0x0, 0x7f,
    0xff, 0x5f, 0xff, 0x90, 0x0, 0x0, 0x0, 0x9f,
    0xff, 0xbf, 0xfd, 0x0, 0x4, 0xff, 0xfb, 0xff,
    0xf5, 0x0, 0x0, 0x0, 0x5, 0xff, 0xff, 0xff,
    0xa0, 0x0, 0xf, 0xff, 0xff, 0xff, 0x10, 0x0,
    0x0, 0x0, 0x1f, 0xff, 0xff, 0xf6, 0x0, 0x0,
    0xcf, 0xff, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0xff, 0xff, 0x30, 0x0, 0x9, 0xff, 0xff,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0xff,
    0xf0, 0x0, 0x0, 0x5f, 0xff, 0xff, 0x60, 0x0,
    0x0,

    /* U+0078 "x" */
    0xd, 0xff, 0xff, 0x30, 0x0, 0x0, 0xef, 0xff,
    0xd0, 0x4, 0xff, 0xff, 0xb0, 0x0, 0x6, 0xff,
    0xff, 0x40, 0x0, 0xbf, 0xff, 0xf4, 0x0, 0xd,
    0xff, 0xfc, 0x0, 0x0, 0x2f, 0xff, 0xfc, 0x0,
    0x4f, 0xff, 0xf3, 0x0, 0x0, 0x8, 0xff, 0xff,
    0x40, 0xbf, 0xff, 0xa0, 0x0, 0x0, 0x0, 0xef,
    0xff, 0xc2, 0xff, 0xff, 0x20, 0x0, 0x0, 0x0,
    0x6f, 0xff, 0xfc, 0xff, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0xd, 0xff, 0xff, 0xff, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xff, 0xff, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xff, 0xff, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0xff, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0xff,
    0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff,
    0x9f, 0xff, 0xf9, 0x0, 0x0, 0x0, 0x5, 0xff,
    0xfe, 0x9, 0xff, 0xff, 0x20, 0x0, 0x0, 0xe,
    0xff, 0xf7, 0x1, 0xff, 0xff, 0xc0, 0x0, 0x0,
    0x8f, 0xff, 0xf1, 0x0, 0x8f, 0xff, 0xf5, 0x0,
    0x1, 0xff, 0xff, 0x90, 0x0, 0x1e, 0xff, 0xfe,
    0x0, 0xa, 0xff, 0xff, 0x10, 0x0, 0x6, 0xff,
    0xff, 0x90, 0x3f, 0xff, 0xfa, 0x0, 0x0, 0x0,
    0xdf, 0xff, 0xf2,

    /* U+0079 "y" */
    0x5f, 0xff, 0xf5, 0x0, 0x0, 0x0, 0xb, 0xff,
    0xfc, 0xf, 0xff, 0xfa, 0x0, 0x0, 0x0, 0xf,
    0xff, 0xf7, 0x9, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x4f, 0xff, 0xf1, 0x3, 0xff, 0xff, 0x50, 0x0,
    0x0, 0x9f, 0xff, 0xc0, 0x0, 0xdf, 0xff, 0xa0,
    0x0, 0x0, 0xdf, 0xff, 0x70, 0x0, 0x7f, 0xff,
    0xf0, 0x0, 0x2, 0xff, 0xff, 0x10, 0x0, 0x1f,
    0xff, 0xf5, 0x0, 0x6, 0xff, 0xfc, 0x0, 0x0,
    0xa, 0xff, 0xfa, 0x0, 0xb, 0xff, 0xf7, 0x0,
    0x0, 0x4, 0xff, 0xff, 0x0, 0xf, 0xff, 0xf1,
    0x0, 0x0, 0x0, 0xef, 0xff, 0x40, 0x4f, 0xff,
    0xc0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0x90, 0x8f,
    0xff, 0x70, 0x0, 0x0, 0x0, 0x2f, 0xff, 0xe0,
    0xbf, 0xff, 0x10, 0x0, 0x0, 0x0, 0xc, 0xff,
    0xf2, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xff, 0xfa, 0xff, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xff, 0xff, 0xf1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xff, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xff, 0xff, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xff, 0xff, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xff,
    0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f,
    0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0xae,
    0xff, 0xff, 0xfe, 0x10, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xff, 0xff, 0xff, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xdf, 0xfd, 0x92, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+007A "z" */
    0xc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x30,
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0xc,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x20, 0xad,
    0xdd, 0xdd, 0xdf, 0xff, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0x1, 0xff, 0xff, 0xe1, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x1e,
    0xff, 0xff, 0x10, 0x0, 0x0, 0x0, 0xa, 0xff,
    0xff, 0x60, 0x0, 0x0, 0x0, 0x4, 0xff, 0xff,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xff, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xff, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xff, 0xff, 0x30, 0x0, 0x0,
    0x0, 0x7, 0xff, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x2, 0xff, 0xff, 0xfe, 0xdd, 0xdd, 0xdd, 0xd6,
    0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x7b,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7, 0xbf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x70,

    /* U+007B "{" */
    0x0, 0x0, 0x3b, 0xef, 0xf9, 0x0, 0x3, 0xff,
    0xff, 0xf9, 0x0, 0xa, 0xff, 0xfe, 0xa5, 0x0,
    0xe, 0xff, 0xf1, 0x0, 0x0, 0xf, 0xff, 0xc0,
    0x0, 0x0, 0xf, 0xff, 0xc0, 0x0, 0x0, 0xf,
    0xff, 0xc0, 0x0, 0x0, 0xf, 0xff, 0xc0, 0x0,
    0x0, 0xe, 0xff, 0xc0, 0x0, 0x0, 0xd, 0xff,
    0xd0, 0x0, 0x0, 0xc, 0xff, 0xd0, 0x0, 0x0,
    0xc, 0xff, 0xd0, 0x0, 0x0, 0xc, 0xff, 0xd0,
    0x0, 0x0, 0x1f, 0xff, 0xc0, 0x0, 0x3, 0xcf,
    0xff, 0x80, 0x0, 0xdf, 0xff, 0xfc, 0x0, 0x0,
    0xdf, 0xff, 0xb0, 0x0, 0x0, 0xcf, 0xff, 0xfd,
    0x10, 0x0, 0x1, 0xbf, 0xff, 0x90, 0x0, 0x0,
    0x1f, 0xff, 0xc0, 0x0, 0x0, 0xc, 0xff, 0xd0,
    0x0, 0x0, 0xc, 0xff, 0xd0, 0x0, 0x0, 0xd,
    0xff, 0xd0, 0x0, 0x0, 0xe, 0xff, 0xd0, 0x0,
    0x0, 0xf, 0xff, 0xc0, 0x0, 0x0, 0xf, 0xff,
    0xc0, 0x0, 0x0, 0xf, 0xff, 0xc0, 0x0, 0x0,
    0xf, 0xff, 0xc0, 0x0, 0x0, 0xf, 0xff, 0xf1,
    0x0, 0x0, 0xb, 0xff, 0xfe, 0x95, 0x0, 0x4,
    0xff, 0xff, 0xf9, 0x0, 0x0, 0x4b, 0xef, 0xf9,

    /* U+007C "|" */
    0x79, 0x92, 0xcf, 0xf4, 0xcf, 0xf4, 0xcf, 0xf4,
    0xcf, 0xf4, 0xcf, 0xf4, 0xcf, 0xf4, 0xcf, 0xf4,
    0xcf, 0xf4, 0xcf, 0xf4, 0xcf, 0xf4, 0xcf, 0xf4,
    0xcf, 0xf4, 0xcf, 0xf4, 0xcf, 0xf4, 0xcf, 0xf4,
    0xcf, 0xf4, 0xcf, 0xf4, 0xcf, 0xf4, 0xcf, 0xf4,
    0xcf, 0xf4, 0xcf, 0xf4, 0xcf, 0xf4, 0xcf, 0xf4,
    0xcf, 0xf4, 0xcf, 0xf4, 0xcf, 0xf4, 0xcf, 0xf4,
    0xcf, 0xf4, 0xcf, 0xf4, 0xcf, 0xf4, 0xcf, 0xf4,
    0xcf, 0xf4, 0xcf, 0xf4, 0xcf, 0xf4, 0xcf, 0xf4,
    0xcf, 0xf4, 0xcf, 0xf4,

    /* U+007D "}" */
    0x7f, 0xfe, 0xb4, 0x0, 0x0, 0x7f, 0xff, 0xff,
    0x40, 0x0, 0x4a, 0xef, 0xff, 0xc0, 0x0, 0x0,
    0x1e, 0xff, 0xf0, 0x0, 0x0, 0xb, 0xff, 0xf1,
    0x0, 0x0, 0xa, 0xff, 0xf2, 0x0, 0x0, 0xa,
    0xff, 0xf2, 0x0, 0x0, 0xb, 0xff, 0xf1, 0x0,
    0x0, 0xb, 0xff, 0xf0, 0x0, 0x0, 0xc, 0xff,
    0xf0, 0x0, 0x0, 0xc, 0xff, 0xe0, 0x0, 0x0,
    0xc, 0xff, 0xd0, 0x0, 0x0, 0xc, 0xff, 0xe0,
    0x0, 0x0, 0xb, 0xff, 0xf2, 0x0, 0x0, 0x6,
    0xff, 0xfd, 0x30, 0x0, 0x0, 0xbf, 0xff, 0xff,
    0x0, 0x0, 0xa, 0xff, 0xff, 0x0, 0x1, 0xcf,
    0xff, 0xfe, 0x0, 0x8, 0xff, 0xfb, 0x20, 0x0,
    0xb, 0xff, 0xf1, 0x0, 0x0, 0xc, 0xff, 0xe0,
    0x0, 0x0, 0xc, 0xff, 0xe0, 0x0, 0x0, 0xc,
    0xff, 0xe0, 0x0, 0x0, 0xb, 0xff, 0xf0, 0x0,
    0x0, 0xb, 0xff, 0xf0, 0x0, 0x0, 0xa, 0xff,
    0xf1, 0x0, 0x0, 0xa, 0xff, 0xf2, 0x0, 0x0,
    0xb, 0xff, 0xf2, 0x0, 0x0, 0xe, 0xff, 0xf0,
    0x0, 0x49, 0xdf, 0xff, 0xd0, 0x0, 0x7f, 0xff,
    0xff, 0x50, 0x0, 0x7f, 0xff, 0xb5, 0x0, 0x0,

    /* U+007E "~" */
    0x0, 0x0, 0x46, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xef, 0xff, 0xd3, 0x0, 0x0, 0x6,
    0x0, 0x8, 0xff, 0xff, 0xff, 0xf7, 0x0, 0x8,
    0xfb, 0x15, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x8b,
    0xff, 0xf8, 0x8f, 0xfd, 0x30, 0x6f, 0xff, 0xff,
    0xff, 0xfd, 0x0, 0x6e, 0x10, 0x0, 0x2c, 0xff,
    0xff, 0xfd, 0x10, 0x0, 0x10, 0x0, 0x0, 0x7,
    0xdf, 0xd7, 0x0, 0x0,

    /* U+5173 "关" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0x8f, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xfc, 0x71, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xff, 0xe1, 0x0, 0x0,
    0x0, 0x0, 0x9, 0xff, 0xff, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xff, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xff, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xff, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x9f, 0xff, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xd0, 0x0,
    0x0, 0x2, 0xff, 0xff, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0x80, 0x0,
    0x0, 0xa, 0xff, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbc, 0xcc, 0xce, 0xfd, 0xcc, 0xcc,
    0xcc, 0xcf, 0xff, 0xfd, 0xcc, 0xcc, 0x60, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xff,
    0xff, 0xeb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0x20,
    0x1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x20,
    0x1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x20,
    0x1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x20,
    0x0, 0x11, 0x11, 0x11, 0x11, 0x11, 0x2f, 0xff,
    0xff, 0xfb, 0x11, 0x11, 0x11, 0x11, 0x11, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xff,
    0xff, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xff,
    0xff, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xff,
    0x8c, 0xff, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0xff, 0xfc,
    0x1, 0xef, 0xff, 0xfa, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xef, 0xff, 0xff, 0xd1,
    0x0, 0x4f, 0xff, 0xff, 0xe6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xdf, 0xff, 0xff, 0xfc, 0x10,
    0x0, 0x4, 0xff, 0xff, 0xff, 0xe7, 0x20, 0x0,
    0x0, 0x4a, 0xff, 0xff, 0xff, 0xff, 0x90, 0x0,
    0x0, 0x0, 0x3e, 0xff, 0xff, 0xff, 0xfc, 0x81,
    0xa, 0xff, 0xff, 0xff, 0xff, 0xd3, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xbf, 0xff, 0xff, 0xff, 0x90,
    0x0, 0xcf, 0xff, 0xff, 0xd6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xdf, 0xff, 0xfc, 0x0,
    0x0, 0x1e, 0xff, 0xb4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xcf, 0xf2, 0x0,
    0x0, 0x4, 0x61, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x40, 0x0,

    /* U+5206 "分" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xc8, 0x30, 0x0, 0x0,
    0x1, 0x7d, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0xff, 0xc0, 0x0, 0x5,
    0xff, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xff, 0xf6, 0x0, 0x0, 0xe,
    0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xff, 0xfe, 0x0, 0x0, 0x0, 0x6f,
    0xff, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xff, 0xff, 0x60, 0x0, 0x0, 0x0, 0xdf,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xef, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x3, 0xff,
    0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0xff, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff,
    0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xff,
    0xff, 0x30, 0x0, 0x0, 0x0, 0x3f, 0xff, 0xff,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff,
    0xfe, 0x10, 0x0, 0x0, 0x3e, 0xff, 0xff, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff,
    0xfd, 0x10, 0x0, 0x3e, 0xff, 0xff, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xff,
    0xfd, 0x20, 0x5f, 0xff, 0xff, 0xfa, 0xaa, 0xaa,
    0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xab, 0xff, 0xff,
    0xfd, 0x6, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0x20, 0x5, 0xff, 0xdd, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf7, 0xfe, 0x20,
    0x0, 0x4, 0xc1, 0xcf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x24, 0x40, 0x0,
    0x0, 0x0, 0x1, 0x22, 0x22, 0xbf, 0xff, 0x72,
    0x22, 0x22, 0x2e, 0xff, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xff, 0xf3, 0x0,
    0x0, 0x0, 0xef, 0xff, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xff, 0xff, 0x0, 0x0,
    0x0, 0xf, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5f, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xff, 0xf8, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xff, 0xff, 0x30, 0x0, 0x0, 0x2,
    0xff, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x3f,
    0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x4, 0xff,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f,
    0xff, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff,
    0xff, 0x40, 0x0, 0x0, 0x0, 0x9, 0xff, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xbf, 0xff, 0xff,
    0x70, 0x0, 0x0, 0x0, 0x0, 0xef, 0xff, 0x60,
    0x0, 0x0, 0x0, 0x4a, 0xff, 0xff, 0xff, 0x80,
    0x0, 0x5, 0xdc, 0xcc, 0xef, 0xff, 0xf2, 0x0,
    0x0, 0x0, 0xb, 0xff, 0xff, 0xff, 0x70, 0x0,
    0x0, 0xe, 0xff, 0xff, 0xff, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0xd, 0xff, 0xfd, 0x30, 0x0, 0x0,
    0x0, 0xaf, 0xff, 0xff, 0xff, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xd6, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xff, 0xff, 0xd9, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+524D "前" */
    0x0, 0x0, 0x0, 0x0, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0x9f, 0xd0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xe9, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x1e, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xff, 0xff, 0x70, 0x0, 0x0, 0x0, 0x5, 0xff,
    0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xff, 0xf1, 0x0, 0x0, 0x0, 0xe, 0xff, 0xf9,
    0x0, 0x0, 0x0, 0x57, 0x77, 0x77, 0x8f, 0xff,
    0xa7, 0x77, 0x77, 0x77, 0xaf, 0xff, 0xf7, 0x77,
    0x77, 0x72, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf5, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf5,
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x22, 0x21, 0x0, 0x0, 0x99, 0x99,
    0x99, 0x99, 0x99, 0x99, 0x60, 0x1, 0x33, 0x30,
    0x2, 0xff, 0xf9, 0x0, 0x0, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xb0, 0x9, 0xff, 0xf0, 0x2,
    0xff, 0xf9, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xb0, 0x9, 0xff, 0xf0, 0x2, 0xff,
    0xf9, 0x0, 0x0, 0xff, 0xfd, 0xbb, 0xbb, 0xbf,
    0xff, 0xb0, 0x9, 0xff, 0xf0, 0x2, 0xff, 0xf9,
    0x0, 0x0, 0xff, 0xf9, 0x0, 0x0, 0xf, 0xff,
    0xb0, 0x9, 0xff, 0xf0, 0x2, 0xff, 0xf9, 0x0,
    0x0, 0xff, 0xfb, 0x55, 0x55, 0x5f, 0xff, 0xb0,
    0x9, 0xff, 0xf0, 0x2, 0xff, 0xf9, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb0, 0x9,
    0xff, 0xf0, 0x2, 0xff, 0xf9, 0x0, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xb0, 0x9, 0xff,
    0xf0, 0x2, 0xff, 0xf9, 0x0, 0x0, 0xff, 0xfc,
    0x88, 0x88, 0x8f, 0xff, 0xb0, 0x9, 0xff, 0xf0,
    0x2, 0xff, 0xf9, 0x0, 0x0, 0xff, 0xf9, 0x0,
    0x0, 0xf, 0xff, 0xb0, 0x9, 0xff, 0xf0, 0x2,
    0xff, 0xf9, 0x0, 0x0, 0xff, 0xfc, 0x66, 0x66,
    0x6f, 0xff, 0xb0, 0x9, 0xff, 0xf0, 0x2, 0xff,
    0xf9, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xb0, 0x9, 0xff, 0xf0, 0x2, 0xff, 0xf9,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xb0, 0x9, 0xff, 0xf0, 0x2, 0xff, 0xf9, 0x0,
    0x0, 0xff, 0xfc, 0x66, 0x66, 0x6f, 0xff, 0xb0,
    0x8, 0xee, 0xe0, 0x2, 0xff, 0xf9, 0x0, 0x0,
    0xff, 0xf9, 0x0, 0x0, 0xf, 0xff, 0xb0, 0x0,
    0x0, 0x0, 0x2, 0xff, 0xf9, 0x0, 0x0, 0xff,
    0xf9, 0x0, 0x0, 0xf, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x2, 0xff, 0xf9, 0x0, 0x0, 0xff, 0xf9,
    0x1, 0x33, 0x4f, 0xff, 0xa0, 0x0, 0x5, 0x99,
    0x9b, 0xff, 0xf9, 0x0, 0x0, 0xff, 0xf9, 0x1,
    0xff, 0xff, 0xff, 0x90, 0x0, 0x2, 0xff, 0xff,
    0xff, 0xf6, 0x0, 0x0, 0xff, 0xf9, 0x0, 0xaf,
    0xff, 0xff, 0x30, 0x0, 0x0, 0xbf, 0xff, 0xff,
    0xe1, 0x0, 0x0, 0xee, 0xe8, 0x0, 0x5f, 0xfe,
    0xa3, 0x0, 0x0, 0x0, 0x6f, 0xfe, 0xc8, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+52A8 "动" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xaa, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x3, 0xee,
    0xee, 0xee, 0xee, 0xee, 0xee, 0x50, 0x0, 0xf,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x50, 0x0, 0xf, 0xff,
    0xc0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x50, 0x0, 0xf, 0xff, 0xc0,
    0x0, 0x0, 0x0, 0x1, 0x77, 0x77, 0x77, 0x77,
    0x77, 0x77, 0x20, 0x0, 0xf, 0xff, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0x66, 0x6f, 0xff, 0xd6, 0x66, 0x66,
    0x64, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x14,
    0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x48, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x4f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xe3, 0x77, 0x8f,
    0xff, 0xc7, 0x77, 0xff, 0xfb, 0x4f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xe0, 0x0, 0x3f, 0xff,
    0x80, 0x0, 0xff, 0xfb, 0x4f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe0, 0x0, 0x4f, 0xff, 0x70,
    0x0, 0xff, 0xfa, 0x3, 0x33, 0x8f, 0xff, 0x93,
    0x33, 0x33, 0x20, 0x0, 0x5f, 0xff, 0x60, 0x1,
    0xff, 0xfa, 0x0, 0x0, 0xaf, 0xff, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xff, 0x40, 0x1, 0xff,
    0xf9, 0x0, 0x0, 0xdf, 0xff, 0x0, 0x3, 0x81,
    0x0, 0x0, 0x9f, 0xff, 0x20, 0x2, 0xff, 0xf9,
    0x0, 0x1, 0xff, 0xfa, 0x0, 0xef, 0xf7, 0x0,
    0x0, 0xbf, 0xff, 0x0, 0x3, 0xff, 0xf8, 0x0,
    0x6, 0xff, 0xf6, 0x0, 0xaf, 0xfe, 0x0, 0x0,
    0xdf, 0xfe, 0x0, 0x3, 0xff, 0xf8, 0x0, 0xa,
    0xff, 0xf1, 0x0, 0x4f, 0xff, 0x40, 0x1, 0xff,
    0xfb, 0x0, 0x4, 0xff, 0xf7, 0x0, 0xf, 0xff,
    0xb0, 0x0, 0xe, 0xff, 0xa0, 0x5, 0xff, 0xf8,
    0x0, 0x5, 0xff, 0xf6, 0x0, 0x5f, 0xff, 0x50,
    0x2, 0x6e, 0xff, 0xf1, 0x9, 0xff, 0xf4, 0x0,
    0x6, 0xff, 0xf5, 0x0, 0xbf, 0xff, 0x7b, 0xef,
    0xff, 0xff, 0xf6, 0xe, 0xff, 0xf1, 0x0, 0x7,
    0xff, 0xf4, 0x4, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfb, 0x4f, 0xff, 0xc0, 0x0, 0x9, 0xff,
    0xf3, 0xe, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xef,
    0xff, 0xbf, 0xff, 0x60, 0x0, 0xb, 0xff, 0xf2,
    0xb, 0xff, 0xff, 0xff, 0xb7, 0x20, 0x7f, 0xba,
    0xff, 0xff, 0x10, 0x0, 0xd, 0xff, 0xf0, 0x6,
    0xff, 0xe9, 0x40, 0x0, 0x0, 0x10, 0xe, 0xff,
    0xf9, 0x0, 0x0, 0x2f, 0xff, 0xd0, 0x1, 0xa3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xf2,
    0xab, 0xaa, 0xef, 0xff, 0xa0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xff, 0xff, 0x80, 0x9f,
    0xff, 0xff, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5f, 0xfc, 0x0, 0x4f, 0xff,
    0xff, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xe1, 0x0, 0xf, 0xff, 0xfc,
    0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+540C "同" */
    0x6a, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa,
    0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xa7, 0xaf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfb, 0xaf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfb, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0xaf, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xff, 0xfb, 0xaf, 0xff,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xff, 0xfb, 0xaf, 0xff, 0x11, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x1,
    0xff, 0xfb, 0xaf, 0xff, 0x11, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x1, 0xff, 0xfb,
    0xaf, 0xff, 0x11, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x1, 0xff, 0xfb, 0xaf, 0xff,
    0x10, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x1, 0xff, 0xfb, 0xaf, 0xff, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xff, 0xfb, 0xaf, 0xff, 0x10, 0x1, 0x11, 0x11,
    0x11, 0x11, 0x11, 0x11, 0x0, 0x1, 0xff, 0xfb,
    0xaf, 0xff, 0x10, 0x9, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x80, 0x1, 0xff, 0xfb, 0xaf, 0xff,
    0x10, 0x9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x80, 0x1, 0xff, 0xfb, 0xaf, 0xff, 0x10, 0x9,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0x1,
    0xff, 0xfb, 0xaf, 0xff, 0x10, 0x9, 0xff, 0xe1,
    0x11, 0x11, 0x1f, 0xff, 0x80, 0x1, 0xff, 0xfb,
    0xaf, 0xff, 0x10, 0x9, 0xff, 0xe0, 0x0, 0x0,
    0xf, 0xff, 0x80, 0x1, 0xff, 0xfb, 0xaf, 0xff,
    0x10, 0x9, 0xff, 0xe0, 0x0, 0x0, 0xf, 0xff,
    0x80, 0x1, 0xff, 0xfb, 0xaf, 0xff, 0x10, 0x9,
    0xff, 0xe0, 0x0, 0x0, 0xf, 0xff, 0x80, 0x1,
    0xff, 0xfb, 0xaf, 0xff, 0x10, 0x9, 0xff, 0xf4,
    0x44, 0x44, 0x5f, 0xff, 0x80, 0x1, 0xff, 0xfb,
    0xaf, 0xff, 0x10, 0x9, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x80, 0x1, 0xff, 0xfb, 0xaf, 0xff,
    0x10, 0x9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x80, 0x1, 0xff, 0xfb, 0xaf, 0xff, 0x10, 0x9,
    0xff, 0xfe, 0xee, 0xee, 0xee, 0xee, 0x70, 0x1,
    0xff, 0xfb, 0xaf, 0xff, 0x10, 0x9, 0xff, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xfb,
    0xaf, 0xff, 0x10, 0x8, 0xff, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xff, 0xfa, 0xaf, 0xff,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xbb, 0xbd, 0xff, 0xf9, 0xaf, 0xff, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xff,
    0xff, 0xf6, 0xaf, 0xff, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xff, 0xff, 0xff, 0xd0,
    0xaf, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xdf, 0xfe, 0xb7, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+540E "后" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x14, 0x69, 0xcf, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x23, 0x56, 0x89, 0xbd,
    0xff, 0xff, 0xff, 0xff, 0xfa, 0x0, 0x0, 0x0,
    0x9, 0xcd, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x0,
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0xb8, 0x52, 0x0, 0x0, 0x0, 0xc,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0xb9, 0x75,
    0x31, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0xff, 0x96, 0x54, 0x21, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff,
    0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0xfa,
    0x99, 0x99, 0x99, 0x99, 0x99, 0x99, 0x99, 0x99,
    0x99, 0x99, 0x99, 0x0, 0x0, 0xcf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0x0, 0xc, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x0, 0x0, 0xcf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0x0, 0xd, 0xff, 0xf3, 0x11, 0x11,
    0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
    0x10, 0x0, 0x0, 0xdf, 0xff, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xe, 0xff, 0xf1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xff, 0xf0, 0x1f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x0, 0x0,
    0x2, 0xff, 0xfd, 0x1, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xd0, 0x0, 0x0,
    0x4f, 0xff, 0xb0, 0x1f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0x0, 0x0, 0x6,
    0xff, 0xf9, 0x1, 0xff, 0xfe, 0x88, 0x88, 0x88,
    0x88, 0x88, 0xaf, 0xff, 0xd0, 0x0, 0x0, 0x9f,
    0xff, 0x60, 0x1f, 0xff, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0xfd, 0x0, 0x0, 0xd, 0xff,
    0xf3, 0x1, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0xff, 0xd0, 0x0, 0x2, 0xff, 0xff,
    0x0, 0x1f, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xff, 0xfd, 0x0, 0x0, 0x7f, 0xff, 0xb0,
    0x1, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xff, 0xd0, 0x0, 0xd, 0xff, 0xf7, 0x0,
    0x1f, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xff, 0xfd, 0x0, 0x5, 0xff, 0xff, 0x10, 0x1,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xd0, 0x1, 0xef, 0xff, 0xb0, 0x0, 0x1f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfd, 0x0, 0x4f, 0xff, 0xf4, 0x0, 0x1, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xd0, 0x0, 0x3e, 0xfb, 0x0, 0x0, 0x1f, 0xff,
    0xe8, 0x88, 0x88, 0x88, 0x88, 0x8a, 0xff, 0xfd,
    0x0, 0x0, 0x2d, 0x20, 0x0, 0x1, 0xff, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x11, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+5E18 "帘" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xcf, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xff, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x45, 0x55, 0x55, 0x55, 0x55, 0x57,
    0xff, 0xff, 0x85, 0x55, 0x55, 0x55, 0x55, 0x54,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0x0, 0x0, 0x2, 0xff, 0xff,
    0xff, 0xfe, 0x0, 0x0, 0x6e, 0xd4, 0x0, 0x0,
    0x4f, 0xe8, 0x20, 0x2, 0xff, 0xff, 0xde, 0xec,
    0x0, 0x6d, 0xff, 0xff, 0x70, 0x1, 0xef, 0xff,
    0xfc, 0x51, 0x99, 0x98, 0x0, 0x2, 0x8e, 0xff,
    0xff, 0xf7, 0x0, 0x1, 0x9e, 0xff, 0xff, 0xfe,
    0x71, 0x0, 0x7, 0xcf, 0xff, 0xff, 0xfc, 0x20,
    0x0, 0x0, 0x0, 0x5c, 0xff, 0xff, 0xff, 0x81,
    0xb, 0xff, 0xff, 0xfd, 0x50, 0x0, 0xab, 0xbb,
    0x20, 0x0, 0x3a, 0xff, 0xff, 0xe1, 0x1, 0xff,
    0xfd, 0x60, 0x0, 0x0, 0xef, 0xff, 0x30, 0x0,
    0x0, 0x2a, 0xff, 0x50, 0x0, 0x6a, 0x40, 0x0,
    0x0, 0x0, 0xef, 0xff, 0x30, 0x0, 0x0, 0x0,
    0x27, 0x0, 0x0, 0x58, 0x88, 0x88, 0x88, 0x88,
    0xff, 0xff, 0xa8, 0x88, 0x88, 0x88, 0x88, 0x0,
    0x0, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x10, 0x0, 0xaf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x10, 0x0, 0xaf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x10, 0x0, 0xaf, 0xff, 0x60, 0x0, 0x0,
    0xef, 0xff, 0x30, 0x0, 0x0, 0xcf, 0xff, 0x10,
    0x0, 0xaf, 0xff, 0x60, 0x0, 0x0, 0xef, 0xff,
    0x30, 0x0, 0x0, 0xcf, 0xff, 0x10, 0x0, 0xaf,
    0xff, 0x60, 0x0, 0x0, 0xef, 0xff, 0x30, 0x0,
    0x0, 0xcf, 0xff, 0x10, 0x0, 0xaf, 0xff, 0x60,
    0x0, 0x0, 0xef, 0xff, 0x30, 0x0, 0x0, 0xcf,
    0xff, 0x10, 0x0, 0xaf, 0xff, 0x60, 0x0, 0x0,
    0xef, 0xff, 0x30, 0x0, 0x0, 0xcf, 0xff, 0x10,
    0x0, 0xaf, 0xff, 0x60, 0x0, 0x0, 0xef, 0xff,
    0x30, 0x0, 0x0, 0xdf, 0xff, 0x10, 0x0, 0xaf,
    0xff, 0x60, 0x0, 0x0, 0xef, 0xff, 0x33, 0xff,
    0xff, 0xff, 0xff, 0x0, 0x0, 0xaf, 0xff, 0x60,
    0x0, 0x0, 0xef, 0xff, 0x30, 0xdf, 0xff, 0xff,
    0xfc, 0x0, 0x0, 0xaf, 0xff, 0x60, 0x0, 0x0,
    0xef, 0xff, 0x30, 0x8f, 0xff, 0xff, 0xd3, 0x0,
    0x0, 0x6a, 0xaa, 0x40, 0x0, 0x0, 0xef, 0xff,
    0x30, 0x39, 0x88, 0x64, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xef, 0xff, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xff, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+6B65 "步" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x33,
    0x32, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0x11, 0x10, 0x0, 0x6, 0xff, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xff, 0xc0, 0x0, 0x6, 0xff, 0xfe, 0xaa, 0xaa,
    0xaa, 0xaa, 0xa0, 0x0, 0x0, 0x0, 0x2f, 0xff,
    0xc0, 0x0, 0x6, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf1, 0x0, 0x0, 0x0, 0x2f, 0xff, 0xc0,
    0x0, 0x6, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf1, 0x0, 0x0, 0x0, 0x2f, 0xff, 0xc0, 0x0,
    0x6, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x2f, 0xff, 0xc0, 0x0, 0x6,
    0xff, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xff, 0xc0, 0x0, 0x6, 0xff,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2f, 0xff, 0xc0, 0x0, 0x6, 0xff, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf1, 0xaf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf2, 0xaf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf2, 0x6a, 0xaa, 0xaa, 0xaa, 0xaa,
    0xaa, 0xaf, 0xff, 0xfb, 0xaa, 0xaa, 0xaa, 0xaa,
    0xaa, 0xa1, 0x0, 0x0, 0x0, 0x3, 0x0, 0x0,
    0xe, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xfa, 0x50, 0xe,
    0xff, 0xf4, 0x0, 0x0, 0x7, 0xe7, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xef, 0xff, 0xa0, 0xe, 0xff,
    0xf4, 0x0, 0x0, 0x1f, 0xff, 0xe5, 0x0, 0x0,
    0x0, 0x2e, 0xff, 0xfe, 0x10, 0xe, 0xff, 0xf4,
    0x0, 0x0, 0xcf, 0xff, 0xf1, 0x0, 0x0, 0x3,
    0xef, 0xff, 0xf3, 0x0, 0xe, 0xff, 0xf4, 0x0,
    0x9, 0xff, 0xff, 0x70, 0x0, 0x0, 0x6f, 0xff,
    0xff, 0x40, 0x0, 0xe, 0xff, 0xf4, 0x0, 0x8f,
    0xff, 0xfb, 0x0, 0x0, 0xb, 0xff, 0xff, 0xf4,
    0x0, 0x0, 0xe, 0xff, 0xf4, 0x9, 0xff, 0xff,
    0xe1, 0x0, 0x0, 0x3, 0xff, 0xff, 0x40, 0x0,
    0x0, 0xe, 0xff, 0xf6, 0xcf, 0xff, 0xfe, 0x20,
    0x0, 0x0, 0x0, 0x3f, 0xe3, 0x0, 0x0, 0x0,
    0xe, 0xff, 0xff, 0xff, 0xff, 0xe2, 0x0, 0x0,
    0x0, 0x0, 0x3, 0x10, 0x0, 0x0, 0x0, 0x2,
    0x8e, 0xff, 0xff, 0xfd, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0x9f, 0xff,
    0xff, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0x59, 0xef, 0xff, 0xff, 0xff,
    0xd4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x25,
    0x7a, 0xef, 0xff, 0xff, 0xff, 0xff, 0xe6, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xc6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0xff, 0xff,
    0xff, 0xfc, 0x72, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xff, 0xff, 0xfd, 0x95,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb9, 0x64, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+7126 "焦" */
    0x0, 0x0, 0x0, 0x0, 0x2, 0x0, 0x0, 0x0,
    0x0, 0x12, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xd8, 0x20, 0x0,
    0x5b, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xef, 0xff, 0x50, 0x0,
    0xdf, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xff, 0xfd, 0x0, 0x0,
    0x6f, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2f, 0xff, 0xf8, 0x33, 0x33,
    0x4f, 0xff, 0xf4, 0x33, 0x33, 0x33, 0x33, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x10,
    0x0, 0x0, 0x7, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x10,
    0x0, 0x0, 0x3f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x10,
    0x0, 0x2, 0xef, 0xff, 0xfb, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1e, 0xff, 0xff, 0xfb, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0x0,
    0xd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0x0,
    0x1, 0xcf, 0xfa, 0xff, 0xff, 0xee, 0xee, 0xee,
    0xef, 0xff, 0xfe, 0xee, 0xee, 0xee, 0x60, 0x0,
    0x0, 0x9, 0x71, 0xff, 0xfb, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xfe, 0xbb, 0xbb, 0xbb,
    0xbf, 0xff, 0xeb, 0xbb, 0xbb, 0xbb, 0x50, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xfc, 0x44, 0x44, 0x44,
    0x4f, 0xff, 0xc4, 0x44, 0x44, 0x44, 0x20, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xfb, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xff, 0xdd, 0xdd, 0xdd,
    0xdf, 0xff, 0xfd, 0xdd, 0xdd, 0xdd, 0xdd, 0x10,
    0x0, 0x0, 0x1, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x20,
    0x0, 0x0, 0x1, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x20,
    0x0, 0x0, 0x1, 0xff, 0xfc, 0x55, 0x55, 0x55,
    0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x0,
    0x0, 0x0, 0x3e, 0xea, 0x63, 0x0, 0x0, 0x0,
    0x0, 0x2, 0x40, 0x0, 0x38, 0xda, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xff, 0x80, 0x4d, 0xef, 0x30,
    0x7, 0xff, 0xf4, 0x1, 0xef, 0xff, 0x50, 0x0,
    0x0, 0x2, 0xff, 0xff, 0x20, 0x3f, 0xff, 0x60,
    0x4, 0xff, 0xfa, 0x0, 0x6f, 0xff, 0xe1, 0x0,
    0x0, 0xa, 0xff, 0xfa, 0x0, 0x1f, 0xff, 0x90,
    0x0, 0xef, 0xff, 0x0, 0xb, 0xff, 0xfa, 0x0,
    0x0, 0x5f, 0xff, 0xf2, 0x0, 0xf, 0xff, 0xb0,
    0x0, 0xaf, 0xff, 0x40, 0x2, 0xff, 0xff, 0x40,
    0x2, 0xef, 0xff, 0x80, 0x0, 0xe, 0xff, 0xd0,
    0x0, 0x6f, 0xff, 0x90, 0x0, 0x9f, 0xff, 0xd0,
    0x3, 0xaf, 0xfd, 0x0, 0x0, 0xd, 0xff, 0xe0,
    0x0, 0x2f, 0xfd, 0x80, 0x0, 0x1f, 0xff, 0xa1,
    0x0, 0x1, 0x72, 0x0, 0x0, 0x5, 0x42, 0x0,
    0x0, 0x3, 0x0, 0x0, 0x0, 0x6, 0x50, 0x0,

    /* U+79D2 "秒" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x59, 0x0, 0x0,
    0x0, 0x1, 0xff, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x37, 0xbf, 0xff, 0x70, 0x0,
    0x0, 0x1, 0xff, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x2, 0x68, 0xbf, 0xff, 0xff, 0xff, 0xf3, 0x0,
    0x0, 0x1, 0xff, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc5, 0x0,
    0x0, 0x1, 0xff, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xff, 0xff, 0xff, 0xfd, 0x51, 0x0, 0x0,
    0x0, 0x1, 0xff, 0xf9, 0x0, 0x2, 0x0, 0x0,
    0x0, 0xaa, 0x86, 0xff, 0xfa, 0x0, 0x0, 0x6,
    0x52, 0x1, 0xff, 0xf9, 0x28, 0xdf, 0x20, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xfa, 0x0, 0x0, 0xf,
    0xff, 0xa1, 0xff, 0xf9, 0x3f, 0xff, 0xa0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xfa, 0x0, 0x0, 0x1f,
    0xff, 0x81, 0xff, 0xf9, 0xc, 0xff, 0xf1, 0x0,
    0x0, 0x0, 0x2, 0xff, 0xfa, 0x0, 0x0, 0x4f,
    0xff, 0x51, 0xff, 0xf9, 0x6, 0xff, 0xf7, 0x0,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf9, 0x7f,
    0xff, 0x31, 0xff, 0xf9, 0x0, 0xff, 0xfd, 0x0,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf9, 0xaf,
    0xff, 0x1, 0xff, 0xf9, 0x0, 0xaf, 0xff, 0x30,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf9, 0xef,
    0xfb, 0x1, 0xff, 0xf9, 0x0, 0x5f, 0xff, 0x80,
    0x7, 0x88, 0x8d, 0xff, 0xfd, 0x88, 0x86, 0xff,
    0xf7, 0x1, 0xff, 0xf9, 0x0, 0xf, 0xff, 0xc0,
    0x0, 0x0, 0x1f, 0xff, 0xfe, 0x20, 0x7, 0xff,
    0xf3, 0x1, 0xff, 0xf9, 0x0, 0xc, 0xff, 0xa0,
    0x0, 0x0, 0x7f, 0xff, 0xff, 0xe2, 0xa, 0xff,
    0xe0, 0x1, 0xff, 0xf9, 0x0, 0x6, 0x60, 0x0,
    0x0, 0x0, 0xef, 0xff, 0xff, 0xfd, 0x10, 0x5c,
    0x90, 0x1, 0xff, 0xf9, 0x0, 0x20, 0x0, 0x0,
    0x0, 0x6, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x0,
    0x0, 0x1, 0xff, 0xf9, 0x0, 0xdf, 0xb6, 0x0,
    0x0, 0xe, 0xff, 0xff, 0xfc, 0xff, 0xf8, 0x0,
    0x0, 0x1, 0xff, 0xf9, 0x5, 0xff, 0xfc, 0x0,
    0x0, 0x8f, 0xff, 0xff, 0xfa, 0x9f, 0xe1, 0x0,
    0x0, 0x1, 0xff, 0xf9, 0xd, 0xff, 0xf5, 0x0,
    0x2, 0xff, 0xf8, 0xff, 0xfa, 0x1f, 0x40, 0x0,
    0x0, 0x0, 0xcc, 0xc7, 0x7f, 0xff, 0xd0, 0x0,
    0xd, 0xff, 0xe2, 0xff, 0xfa, 0x2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xff, 0xff, 0x50, 0x0,
    0x2f, 0xff, 0x71, 0xff, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5f, 0xff, 0xfb, 0x0, 0x0,
    0x9, 0xfe, 0x1, 0xff, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xff, 0xff, 0xe1, 0x0, 0x0,
    0x1, 0xf5, 0x1, 0xff, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xcf, 0xff, 0xfe, 0x20, 0x0, 0x0,
    0x0, 0x50, 0x1, 0xff, 0xfa, 0x0, 0x0, 0x0,
    0x4, 0xaf, 0xff, 0xff, 0xd2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xfa, 0x0, 0x1, 0x49,
    0xef, 0xff, 0xff, 0xfb, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xfa, 0x4, 0xdf, 0xff,
    0xff, 0xff, 0xfe, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xfa, 0x0, 0xcf, 0xff,
    0xff, 0xfe, 0x81, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xfa, 0x0, 0x3f, 0xff,
    0xfb, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xfa, 0x0, 0xa, 0x95,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+81EA "自" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xff, 0xd5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xff,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0xff, 0xa0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff,
    0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf1, 0x2f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x12,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf1, 0x2f, 0xff, 0xea, 0xaa,
    0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xff, 0xff,
    0x12, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xff, 0xf1, 0x2f, 0xff, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xff, 0x12, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xff, 0xf1, 0x2f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x12, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0x2f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x12, 0xff, 0xfd, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x88, 0x88, 0x8f, 0xff, 0xf1,
    0x2f, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xff, 0x12, 0xff, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff,
    0xf1, 0x2f, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0x12, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf1, 0x2f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x12, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf1, 0x2f, 0xff, 0xd8, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x88, 0x88, 0xff, 0xff, 0x12,
    0xff, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xff, 0xf1, 0x2f, 0xff, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff,
    0x12, 0xff, 0xfd, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x88, 0x88, 0x8f, 0xff, 0xf1, 0x2f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x12, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0x2f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x12, 0xff, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xf1, 0x2f,
    0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xff, 0x10, 0x11, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+8A00 "言" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x25,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xae, 0xff, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xe1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xe, 0xff, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xff, 0xff, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfc, 0x5f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfd, 0x5f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x14, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x43, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf9, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf9, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x1, 0x11, 0x11,
    0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xd, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0xd, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0xd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x14, 0x44,
    0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xff, 0xfe, 0xee, 0xee,
    0xee, 0xee, 0xee, 0xee, 0xff, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xff, 0xa3, 0x33, 0x33, 0x33, 0x33, 0x33,
    0x33, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0x4f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x1, 0x11, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+8BED "语" */
    0x0, 0x5, 0x30, 0x0, 0x0, 0x12, 0x22, 0x22,
    0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x20, 0x0,
    0x5, 0xff, 0x50, 0x0, 0xc, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0x4,
    0xff, 0xff, 0x70, 0x0, 0xcf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x0, 0x9,
    0xff, 0xff, 0x80, 0xc, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0x0, 0x9,
    0xff, 0xff, 0x70, 0x23, 0x33, 0x33, 0xff, 0xfe,
    0x33, 0x33, 0x33, 0x33, 0x20, 0x0, 0x0, 0x9,
    0xff, 0xfc, 0x0, 0x0, 0x0, 0x2f, 0xff, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xfc, 0x0, 0x3, 0x55, 0x58, 0xff, 0xfb, 0x55,
    0x55, 0x76, 0x40, 0x0, 0x0, 0x0, 0x0, 0x9,
    0x10, 0x0, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xe0, 0x0, 0x4, 0x44, 0x44, 0x44, 0x20,
    0x0, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfd, 0x0, 0x0, 0xef, 0xff, 0xff, 0xf8, 0x0,
    0x0, 0x0, 0x2f, 0xff, 0xb0, 0x0, 0xd, 0xff,
    0xc0, 0x0, 0xe, 0xff, 0xff, 0xff, 0x80, 0x0,
    0x0, 0x6, 0xff, 0xf7, 0x0, 0x0, 0xff, 0xfa,
    0x0, 0x0, 0xef, 0xff, 0xff, 0xf8, 0x2, 0x33,
    0x33, 0xbf, 0xff, 0x63, 0x33, 0x3f, 0xff, 0xa3,
    0x33, 0x4, 0x44, 0x5f, 0xff, 0x80, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x0, 0x1, 0xff, 0xf8, 0xe, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x0, 0x0, 0x1f, 0xff, 0x80, 0xef, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0x0, 0x1, 0xff, 0xf8, 0x3, 0x33, 0x33, 0x33,
    0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x0,
    0x0, 0x1f, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xff, 0xf8, 0x0, 0x8, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0x80, 0x0, 0x9f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x1,
    0xff, 0xf8, 0x0, 0x19, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x0, 0x0, 0x0, 0x1f,
    0xff, 0x80, 0x8d, 0x9f, 0xff, 0x65, 0x55, 0x55,
    0x55, 0x5e, 0xff, 0xf0, 0x0, 0x0, 0x1, 0xff,
    0xfa, 0xdf, 0xfa, 0xff, 0xf1, 0x0, 0x0, 0x0,
    0x0, 0xef, 0xff, 0x0, 0x0, 0x0, 0x1f, 0xff,
    0xff, 0xff, 0xef, 0xff, 0x10, 0x0, 0x0, 0x0,
    0xe, 0xff, 0xf0, 0x0, 0x0, 0x4, 0xff, 0xff,
    0xff, 0xdb, 0xff, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0xef, 0xff, 0x0, 0x0, 0x0, 0xcf, 0xff, 0xff,
    0x90, 0x9f, 0xff, 0x65, 0x55, 0x55, 0x55, 0x5e,
    0xff, 0xf0, 0x0, 0x0, 0x4f, 0xff, 0xff, 0x50,
    0x9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x0, 0x0, 0x0, 0xbf, 0xfc, 0x20, 0x0,
    0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x0, 0x0, 0x2, 0xf9, 0x0, 0x0, 0x9,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x0, 0x0, 0x0, 0x4, 0x0, 0x0, 0x0, 0x9f,
    0xff, 0x10, 0x0, 0x0, 0x0, 0xe, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x11,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+8DDD "距" */
    0x0, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xf9, 0xc,
    0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0x40,
    0x0, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xfa, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x60,
    0x0, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xfa, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x60,
    0x0, 0xcf, 0xfa, 0x55, 0x55, 0xff, 0xfa, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x60,
    0x0, 0xcf, 0xf7, 0x0, 0x0, 0xff, 0xfa, 0xf,
    0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0xf7, 0x0, 0x0, 0xff, 0xfa, 0xf,
    0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0xf7, 0x0, 0x0, 0xff, 0xfa, 0xf,
    0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0xfd, 0xcc, 0xcc, 0xff, 0xfa, 0xf,
    0xff, 0xf8, 0x88, 0x88, 0x88, 0x88, 0x80, 0x0,
    0x0, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xfa, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe0, 0x0,
    0x0, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xfa, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe0, 0x0,
    0x0, 0x57, 0x77, 0xbf, 0xff, 0x87, 0x74, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xff, 0x10, 0x0, 0xf,
    0xff, 0xe0, 0x0, 0x0, 0xa, 0xff, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xff, 0x10, 0x0, 0xf,
    0xff, 0xe0, 0x0, 0x0, 0xa, 0xff, 0xe0, 0x0,
    0x0, 0xab, 0xb3, 0x7f, 0xff, 0x10, 0x0, 0xf,
    0xff, 0xe0, 0x0, 0x0, 0xa, 0xff, 0xe0, 0x0,
    0x0, 0xef, 0xf4, 0x7f, 0xff, 0xbb, 0xb7, 0xf,
    0xff, 0xe0, 0x0, 0x0, 0xa, 0xff, 0xe0, 0x0,
    0x0, 0xef, 0xf4, 0x7f, 0xff, 0xff, 0xfb, 0xf,
    0xff, 0xe0, 0x0, 0x0, 0xa, 0xff, 0xe0, 0x0,
    0x0, 0xef, 0xf4, 0x7f, 0xff, 0xff, 0xfb, 0xf,
    0xff, 0xfa, 0xaa, 0xaa, 0xad, 0xff, 0xe0, 0x0,
    0x0, 0xef, 0xf4, 0x7f, 0xff, 0xcc, 0xc8, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe0, 0x0,
    0x0, 0xef, 0xf4, 0x7f, 0xff, 0x10, 0x0, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe0, 0x0,
    0x0, 0xef, 0xf4, 0x7f, 0xff, 0x10, 0x0, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe0, 0x0,
    0x0, 0xef, 0xf4, 0x7f, 0xff, 0x10, 0x0, 0xf,
    0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xef, 0xf4, 0x7f, 0xff, 0x46, 0xab, 0xf,
    0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xef, 0xf4, 0x8f, 0xff, 0xff, 0xff, 0xf,
    0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0x1f,
    0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa6, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0xf, 0xff, 0xff, 0xff, 0xd8, 0x40, 0x0, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0xc, 0xff, 0xfb, 0x62, 0x0, 0x0, 0x0, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0x7, 0x94, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xa0,

    /* U+901F "速" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xdf, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xc1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xdf, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xbf, 0xfe, 0x20, 0x0, 0x45, 0x55, 0x55,
    0x55, 0xef, 0xfe, 0x55, 0x55, 0x55, 0x55, 0x10,
    0x2, 0xef, 0xff, 0xe2, 0x0, 0xcf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x40,
    0x0, 0x2e, 0xff, 0xfe, 0x20, 0xcf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x40,
    0x0, 0x2, 0xef, 0xff, 0xd1, 0xae, 0xee, 0xee,
    0xee, 0xff, 0xff, 0xee, 0xee, 0xee, 0xee, 0x30,
    0x0, 0x0, 0x3f, 0xff, 0xd2, 0x0, 0x0, 0x0,
    0x0, 0xdf, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xfb, 0x10, 0x5, 0x55, 0x55,
    0x55, 0xef, 0xfe, 0x55, 0x55, 0x55, 0x51, 0x0,
    0x0, 0x0, 0x0, 0x40, 0x0, 0xe, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0xd9,
    0x99, 0xef, 0xff, 0x99, 0x9c, 0xff, 0xf3, 0x0,
    0x7, 0xaa, 0xaa, 0xaa, 0x90, 0xe, 0xff, 0xa0,
    0x0, 0xdf, 0xfe, 0x0, 0x8, 0xff, 0xf3, 0x0,
    0xc, 0xff, 0xff, 0xff, 0xe0, 0xe, 0xff, 0xa0,
    0x0, 0xdf, 0xfe, 0x0, 0x8, 0xff, 0xf3, 0x0,
    0xc, 0xff, 0xff, 0xff, 0xe0, 0xe, 0xff, 0xea,
    0xaa, 0xff, 0xff, 0xaa, 0xad, 0xff, 0xf3, 0x0,
    0xb, 0xff, 0xff, 0xff, 0xe0, 0xe, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0x0,
    0x0, 0x0, 0xc, 0xff, 0xe0, 0xe, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0x0,
    0x0, 0x0, 0xc, 0xff, 0xe0, 0x3, 0x44, 0x44,
    0xdf, 0xff, 0xff, 0xfc, 0x44, 0x44, 0x40, 0x0,
    0x0, 0x0, 0xc, 0xff, 0xe0, 0x0, 0x0, 0xa,
    0xff, 0xff, 0xff, 0xff, 0xd4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xff, 0xe0, 0x0, 0x1, 0xbf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xff, 0xe0, 0x0, 0x4d, 0xff,
    0xfb, 0xdf, 0xfe, 0x9f, 0xff, 0xfe, 0x30, 0x0,
    0x0, 0x0, 0xc, 0xff, 0xe0, 0x2a, 0xff, 0xff,
    0xc0, 0xdf, 0xfe, 0x5, 0xef, 0xff, 0xf6, 0x0,
    0x0, 0x0, 0xc, 0xff, 0xe3, 0xff, 0xff, 0xfb,
    0x0, 0xdf, 0xfe, 0x0, 0x1b, 0xff, 0xfd, 0x0,
    0x0, 0x0, 0xc, 0xff, 0xe0, 0x7f, 0xff, 0x90,
    0x0, 0xdf, 0xfe, 0x0, 0x0, 0x8f, 0xe1, 0x0,
    0x0, 0x0, 0x2d, 0xff, 0xf5, 0xa, 0xd4, 0x0,
    0x0, 0xdf, 0xfe, 0x0, 0x0, 0x7, 0x20, 0x0,
    0x0, 0x9, 0xff, 0xff, 0xff, 0xc6, 0x0, 0x0,
    0x0, 0xbd, 0xdb, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xea, 0x75,
    0x43, 0x22, 0x22, 0x33, 0x45, 0x66, 0x89, 0xa0,
    0xe, 0xff, 0xff, 0x62, 0x7f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x90,
    0x7, 0xff, 0xe3, 0x0, 0x1, 0x9f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x50,
    0x0, 0xcf, 0x30, 0x0, 0x0, 0x0, 0x6a, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x10,
    0x0, 0x15, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x12, 0x33, 0x33, 0x33, 0x22, 0x10, 0x0, 0x0,

    /* U+949F "钟" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbb, 0x61, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xa, 0xff, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2f, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xff, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xff, 0xff, 0xff, 0xff, 0xf9, 0x0, 0x0, 0x0,
    0xaf, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0xff, 0xff, 0xff, 0xff, 0x90, 0x0, 0x0, 0xa,
    0xff, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xff,
    0xff, 0xff, 0xff, 0xf9, 0x0, 0x0, 0x0, 0xaf,
    0xff, 0x10, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xf8,
    0x77, 0x77, 0x77, 0x49, 0xcc, 0xcc, 0xce, 0xff,
    0xfd, 0xcc, 0xcc, 0xc9, 0xe, 0xff, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xb0, 0x6f, 0xfd, 0x21, 0x11,
    0x11, 0x11, 0xc, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfb, 0x0, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0xcf, 0xff, 0xee, 0xff, 0xff, 0xee,
    0xef, 0xff, 0xb0, 0x7, 0x5f, 0xff, 0xff, 0xff,
    0xff, 0xc, 0xff, 0xa0, 0xa, 0xff, 0xf1, 0x0,
    0xff, 0xfb, 0x0, 0x2, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0xcf, 0xfa, 0x0, 0xaf, 0xff, 0x10, 0xf,
    0xff, 0xb0, 0x0, 0x5, 0x5f, 0xff, 0xc5, 0x54,
    0xc, 0xff, 0xa0, 0xa, 0xff, 0xf1, 0x0, 0xff,
    0xfb, 0x0, 0x0, 0x0, 0xff, 0xfb, 0x0, 0x0,
    0xcf, 0xfa, 0x0, 0xaf, 0xff, 0x10, 0xf, 0xff,
    0xb0, 0x0, 0x0, 0xf, 0xff, 0xb0, 0x0, 0xc,
    0xff, 0xa0, 0xa, 0xff, 0xf1, 0x0, 0xff, 0xfb,
    0x0, 0x77, 0x77, 0xff, 0xfd, 0x77, 0x73, 0xcf,
    0xfa, 0x0, 0xaf, 0xff, 0x10, 0xf, 0xff, 0xb0,
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x8c, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x1,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xcf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb0, 0x1f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x7c, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x0, 0x0,
    0x0, 0xff, 0xfb, 0x0, 0x0, 0xcf, 0xfd, 0xaa,
    0xef, 0xff, 0xaa, 0xaf, 0xff, 0xb0, 0x0, 0x0,
    0xf, 0xff, 0xb0, 0x0, 0xc, 0xff, 0xa0, 0xa,
    0xff, 0xf1, 0x0, 0xff, 0xfb, 0x0, 0x0, 0x0,
    0xff, 0xfb, 0x0, 0x0, 0x7a, 0xa6, 0x0, 0xaf,
    0xff, 0x10, 0x5, 0x55, 0x40, 0x0, 0x0, 0xf,
    0xff, 0xb0, 0x5, 0x60, 0x0, 0x0, 0xa, 0xff,
    0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xfb, 0x6d, 0xfb, 0x0, 0x0, 0x0, 0xaf, 0xff,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff,
    0xff, 0xff, 0xd0, 0x0, 0x0, 0xa, 0xff, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xff,
    0xff, 0xfc, 0x0, 0x0, 0x0, 0xaf, 0xff, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xff, 0xff,
    0xc5, 0x0, 0x0, 0x0, 0xa, 0xff, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xfd, 0x50,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0xff, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0xe5, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xff, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xff, 0x10, 0x0, 0x0,
    0x0,

    /* U+95ED "闭" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xd3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1b, 0xff, 0xf3,
    0x0, 0x8, 0x99, 0x99, 0x99, 0x99, 0x99, 0x99,
    0x99, 0x99, 0x99, 0x0, 0xcf, 0xff, 0xe1, 0x0,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0x1, 0xdf, 0xff, 0xd0, 0xe, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x0, 0x2, 0xff, 0xff, 0x80, 0xef, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x0,
    0x6, 0xff, 0xc2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xff, 0x0, 0x0, 0xb,
    0x70, 0x0, 0x0, 0x0, 0x0, 0x8b, 0xbb, 0x30,
    0x0, 0xb, 0xff, 0xf0, 0x44, 0x44, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xff, 0xf4, 0x0, 0x0,
    0xbf, 0xff, 0xd, 0xff, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xff, 0x40, 0x0, 0xb, 0xff,
    0xf0, 0xdf, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xff, 0xf4, 0x0, 0x0, 0xbf, 0xff, 0xd,
    0xff, 0xf0, 0x3b, 0xbb, 0xbb, 0xbb, 0xbb, 0xef,
    0xff, 0xcb, 0xba, 0xb, 0xff, 0xf0, 0xdf, 0xff,
    0x5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0xbf, 0xff, 0xd, 0xff, 0xf0, 0x5f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xb, 0xff, 0xf0, 0xdf, 0xff, 0x5, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe0, 0xbf,
    0xff, 0xd, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xff, 0xff, 0x40, 0x0, 0xb, 0xff, 0xf0,
    0xdf, 0xff, 0x0, 0x0, 0x0, 0x6, 0xff, 0xff,
    0xff, 0xf4, 0x0, 0x0, 0xbf, 0xff, 0xd, 0xff,
    0xf0, 0x0, 0x0, 0x6, 0xff, 0xff, 0xff, 0xff,
    0x40, 0x0, 0xb, 0xff, 0xf0, 0xdf, 0xff, 0x0,
    0x0, 0x7, 0xff, 0xff, 0xab, 0xff, 0xf4, 0x0,
    0x0, 0xbf, 0xff, 0xd, 0xff, 0xf0, 0x0, 0xa,
    0xff, 0xff, 0xc0, 0xbf, 0xff, 0x40, 0x0, 0xb,
    0xff, 0xf0, 0xdf, 0xff, 0x0, 0x3d, 0xff, 0xff,
    0xb0, 0xb, 0xff, 0xf4, 0x0, 0x0, 0xbf, 0xff,
    0xd, 0xff, 0xf1, 0xaf, 0xff, 0xff, 0xa0, 0x0,
    0xbf, 0xff, 0x40, 0x0, 0xb, 0xff, 0xf0, 0xdf,
    0xff, 0x5f, 0xff, 0xff, 0x70, 0x0, 0xb, 0xff,
    0xf4, 0x0, 0x0, 0xbf, 0xff, 0xd, 0xff, 0xf0,
    0x6f, 0xfe, 0x40, 0x0, 0x0, 0xbf, 0xff, 0x40,
    0x0, 0xb, 0xff, 0xf0, 0xdf, 0xff, 0x0, 0x88,
    0x0, 0x2a, 0x99, 0x9f, 0xff, 0xf3, 0x0, 0x0,
    0xbf, 0xff, 0xd, 0xff, 0xf0, 0x0, 0x0, 0x0,
    0xef, 0xff, 0xff, 0xff, 0x10, 0x0, 0xb, 0xff,
    0xf0, 0xdf, 0xff, 0x0, 0x0, 0x0, 0x9, 0xff,
    0xff, 0xff, 0xa0, 0x0, 0x0, 0xbf, 0xff, 0xd,
    0xff, 0xf0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xeb,
    0x60, 0x4, 0x66, 0x6e, 0xff, 0xf0, 0xdf, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0xff, 0xfd, 0xd, 0xff, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xff, 0x60, 0xdf, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xeb,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+9AD8 "高" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5b, 0xff, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0x33, 0x33,
    0x33, 0x33, 0x33, 0x3f, 0xff, 0xf8, 0x33, 0x33,
    0x33, 0x33, 0x33, 0x30, 0x3f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf2, 0x3f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf2, 0x3f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9, 0x99, 0x99, 0x99, 0x99, 0x99,
    0x99, 0x99, 0x99, 0x99, 0x93, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb,
    0xff, 0xf5, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff,
    0xe8, 0x88, 0x88, 0x88, 0x88, 0x88, 0x8d, 0xff,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc,
    0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0x50, 0x6,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x60, 0x6, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x60, 0x6, 0xff, 0xf9,
    0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55,
    0x55, 0xaf, 0xff, 0x60, 0x6, 0xff, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xff, 0x60, 0x6, 0xff, 0xf6, 0x3, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x0, 0x7f,
    0xff, 0x60, 0x6, 0xff, 0xf6, 0x3, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0x0, 0x7f, 0xff,
    0x60, 0x6, 0xff, 0xf6, 0x3, 0xff, 0xfb, 0x99,
    0x99, 0x99, 0xdf, 0xfc, 0x0, 0x7f, 0xff, 0x60,
    0x6, 0xff, 0xf6, 0x3, 0xff, 0xf5, 0x0, 0x0,
    0x0, 0x9f, 0xfc, 0x0, 0x7f, 0xff, 0x60, 0x6,
    0xff, 0xf6, 0x3, 0xff, 0xfc, 0xbb, 0xbb, 0xbb,
    0xdf, 0xfc, 0x0, 0x7f, 0xff, 0x60, 0x6, 0xff,
    0xf6, 0x3, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfc, 0x0, 0x7f, 0xff, 0x60, 0x6, 0xff, 0xf6,
    0x3, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0x0, 0x7f, 0xff, 0x60, 0x6, 0xff, 0xf6, 0x3,
    0xff, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x3e, 0xbb,
    0xef, 0xff, 0x50, 0x6, 0xff, 0xf6, 0x1, 0x77,
    0x72, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff, 0xff,
    0xff, 0x10, 0x6, 0xff, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xfe, 0xa3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0
};


/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 116, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 0, .adv_w = 190, .box_w = 6, .box_h = 26, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 78, .adv_w = 294, .box_w = 14, .box_h = 12, .ofs_x = 2, .ofs_y = 14},
    {.bitmap_index = 162, .adv_w = 302, .box_w = 17, .box_h = 25, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 375, .adv_w = 302, .box_w = 16, .box_h = 32, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 631, .adv_w = 493, .box_w = 29, .box_h = 25, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 994, .adv_w = 379, .box_w = 24, .box_h = 25, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1294, .adv_w = 167, .box_w = 6, .box_h = 12, .ofs_x = 2, .ofs_y = 14},
    {.bitmap_index = 1330, .adv_w = 194, .box_w = 9, .box_h = 35, .ofs_x = 2, .ofs_y = -7},
    {.bitmap_index = 1488, .adv_w = 194, .box_w = 9, .box_h = 35, .ofs_x = 1, .ofs_y = -7},
    {.bitmap_index = 1646, .adv_w = 260, .box_w = 14, .box_h = 13, .ofs_x = 1, .ofs_y = 13},
    {.bitmap_index = 1737, .adv_w = 302, .box_w = 17, .box_h = 18, .ofs_x = 1, .ofs_y = 4},
    {.bitmap_index = 1890, .adv_w = 167, .box_w = 8, .box_h = 14, .ofs_x = 1, .ofs_y = -8},
    {.bitmap_index = 1946, .adv_w = 190, .box_w = 10, .box_h = 4, .ofs_x = 1, .ofs_y = 8},
    {.bitmap_index = 1966, .adv_w = 167, .box_w = 7, .box_h = 7, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 1991, .adv_w = 198, .box_w = 12, .box_h = 33, .ofs_x = 0, .ofs_y = -7},
    {.bitmap_index = 2189, .adv_w = 302, .box_w = 17, .box_h = 25, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2402, .adv_w = 302, .box_w = 15, .box_h = 25, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 2590, .adv_w = 302, .box_w = 17, .box_h = 25, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2803, .adv_w = 302, .box_w = 18, .box_h = 25, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3028, .adv_w = 302, .box_w = 18, .box_h = 25, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3253, .adv_w = 302, .box_w = 18, .box_h = 25, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3478, .adv_w = 302, .box_w = 17, .box_h = 25, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3691, .adv_w = 302, .box_w = 17, .box_h = 25, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3904, .adv_w = 302, .box_w = 17, .box_h = 25, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4117, .adv_w = 302, .box_w = 17, .box_h = 25, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4330, .adv_w = 167, .box_w = 7, .box_h = 19, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 4397, .adv_w = 167, .box_w = 8, .box_h = 27, .ofs_x = 1, .ofs_y = -8},
    {.bitmap_index = 4505, .adv_w = 302, .box_w = 17, .box_h = 17, .ofs_x = 1, .ofs_y = 4},
    {.bitmap_index = 4650, .adv_w = 302, .box_w = 17, .box_h = 12, .ofs_x = 1, .ofs_y = 7},
    {.bitmap_index = 4752, .adv_w = 302, .box_w = 17, .box_h = 17, .ofs_x = 1, .ofs_y = 4},
    {.bitmap_index = 4897, .adv_w = 263, .box_w = 14, .box_h = 26, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 5079, .adv_w = 516, .box_w = 30, .box_h = 31, .ofs_x = 1, .ofs_y = -6},
    {.bitmap_index = 5544, .adv_w = 328, .box_w = 22, .box_h = 25, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 5819, .adv_w = 349, .box_w = 19, .box_h = 25, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 6057, .adv_w = 336, .box_w = 20, .box_h = 25, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 6307, .adv_w = 366, .box_w = 20, .box_h = 25, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 6557, .adv_w = 315, .box_w = 16, .box_h = 25, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 6757, .adv_w = 300, .box_w = 16, .box_h = 25, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 6957, .adv_w = 367, .box_w = 20, .box_h = 25, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 7207, .adv_w = 388, .box_w = 20, .box_h = 25, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 7457, .adv_w = 169, .box_w = 6, .box_h = 25, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 7532, .adv_w = 291, .box_w = 16, .box_h = 25, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7732, .adv_w = 351, .box_w = 21, .box_h = 25, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 7995, .adv_w = 296, .box_w = 16, .box_h = 25, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 8195, .adv_w = 437, .box_w = 23, .box_h = 25, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 8483, .adv_w = 384, .box_w = 20, .box_h = 25, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 8733, .adv_w = 394, .box_w = 22, .box_h = 25, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 9008, .adv_w = 342, .box_w = 18, .box_h = 25, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 9233, .adv_w = 394, .box_w = 23, .box_h = 32, .ofs_x = 1, .ofs_y = -7},
    {.bitmap_index = 9601, .adv_w = 349, .box_w = 20, .box_h = 25, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 9851, .adv_w = 320, .box_w = 18, .box_h = 25, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 10076, .adv_w = 320, .box_w = 20, .box_h = 25, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 10326, .adv_w = 383, .box_w = 20, .box_h = 25, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 10576, .adv_w = 317, .box_w = 21, .box_h = 25, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 10839, .adv_w = 469, .box_w = 29, .box_h = 25, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 11202, .adv_w = 321, .box_w = 20, .box_h = 25, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 11452, .adv_w = 297, .box_w = 20, .box_h = 25, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 11702, .adv_w = 314, .box_w = 18, .box_h = 25, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 11927, .adv_w = 194, .box_w = 8, .box_h = 32, .ofs_x = 3, .ofs_y = -6},
    {.bitmap_index = 12055, .adv_w = 198, .box_w = 12, .box_h = 33, .ofs_x = 0, .ofs_y = -7},
    {.bitmap_index = 12253, .adv_w = 194, .box_w = 8, .box_h = 32, .ofs_x = 1, .ofs_y = -6},
    {.bitmap_index = 12381, .adv_w = 302, .box_w = 16, .box_h = 15, .ofs_x = 1, .ofs_y = 10},
    {.bitmap_index = 12501, .adv_w = 290, .box_w = 18, .box_h = 3, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 12528, .adv_w = 321, .box_w = 10, .box_h = 10, .ofs_x = 3, .ofs_y = 21},
    {.bitmap_index = 12578, .adv_w = 303, .box_w = 16, .box_h = 19, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 12730, .adv_w = 330, .box_w = 18, .box_h = 26, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 12964, .adv_w = 270, .box_w = 15, .box_h = 19, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 13107, .adv_w = 330, .box_w = 18, .box_h = 26, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 13341, .adv_w = 298, .box_w = 17, .box_h = 19, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 13503, .adv_w = 191, .box_w = 13, .box_h = 26, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 13672, .adv_w = 306, .box_w = 18, .box_h = 26, .ofs_x = 1, .ofs_y = -7},
    {.bitmap_index = 13906, .adv_w = 328, .box_w = 17, .box_h = 26, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 14127, .adv_w = 156, .box_w = 6, .box_h = 26, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 14205, .adv_w = 157, .box_w = 10, .box_h = 33, .ofs_x = -2, .ofs_y = -7},
    {.bitmap_index = 14370, .adv_w = 309, .box_w = 18, .box_h = 26, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 14604, .adv_w = 161, .box_w = 8, .box_h = 26, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 14708, .adv_w = 494, .box_w = 27, .box_h = 19, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 14965, .adv_w = 328, .box_w = 17, .box_h = 19, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 15127, .adv_w = 321, .box_w = 18, .box_h = 19, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 15298, .adv_w = 330, .box_w = 18, .box_h = 26, .ofs_x = 2, .ofs_y = -7},
    {.bitmap_index = 15532, .adv_w = 330, .box_w = 18, .box_h = 26, .ofs_x = 1, .ofs_y = -7},
    {.bitmap_index = 15766, .adv_w = 223, .box_w = 12, .box_h = 19, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 15880, .adv_w = 254, .box_w = 15, .box_h = 19, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 16023, .adv_w = 216, .box_w = 13, .box_h = 24, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 16179, .adv_w = 326, .box_w = 16, .box_h = 19, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 16331, .adv_w = 295, .box_w = 18, .box_h = 19, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 16502, .adv_w = 442, .box_w = 27, .box_h = 19, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 16759, .adv_w = 288, .box_w = 18, .box_h = 19, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 16930, .adv_w = 294, .box_w = 18, .box_h = 26, .ofs_x = 0, .ofs_y = -7},
    {.bitmap_index = 17164, .adv_w = 262, .box_w = 15, .box_h = 19, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 17307, .adv_w = 194, .box_w = 10, .box_h = 32, .ofs_x = 1, .ofs_y = -6},
    {.bitmap_index = 17467, .adv_w = 152, .box_w = 4, .box_h = 38, .ofs_x = 3, .ofs_y = -9},
    {.bitmap_index = 17543, .adv_w = 194, .box_w = 10, .box_h = 32, .ofs_x = 1, .ofs_y = -6},
    {.bitmap_index = 17703, .adv_w = 302, .box_w = 17, .box_h = 7, .ofs_x = 1, .ofs_y = 9},
    {.bitmap_index = 17763, .adv_w = 512, .box_w = 32, .box_h = 31, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 18259, .adv_w = 512, .box_w = 31, .box_h = 32, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 18755, .adv_w = 512, .box_w = 30, .box_h = 32, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 19235, .adv_w = 512, .box_w = 30, .box_h = 31, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 19700, .adv_w = 512, .box_w = 28, .box_h = 30, .ofs_x = 2, .ofs_y = -4},
    {.bitmap_index = 20120, .adv_w = 512, .box_w = 31, .box_h = 32, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 20616, .adv_w = 512, .box_w = 28, .box_h = 31, .ofs_x = 2, .ofs_y = -3},
    {.bitmap_index = 21050, .adv_w = 512, .box_w = 30, .box_h = 31, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 21515, .adv_w = 512, .box_w = 32, .box_h = 31, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 22011, .adv_w = 512, .box_w = 32, .box_h = 31, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 22507, .adv_w = 512, .box_w = 25, .box_h = 32, .ofs_x = 4, .ofs_y = -4},
    {.bitmap_index = 22907, .adv_w = 512, .box_w = 30, .box_h = 32, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 23387, .adv_w = 512, .box_w = 31, .box_h = 31, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 23868, .adv_w = 512, .box_w = 32, .box_h = 28, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 24316, .adv_w = 512, .box_w = 32, .box_h = 31, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 24812, .adv_w = 512, .box_w = 31, .box_h = 31, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 25293, .adv_w = 512, .box_w = 29, .box_h = 32, .ofs_x = 2, .ofs_y = -4},
    {.bitmap_index = 25757, .adv_w = 512, .box_w = 30, .box_h = 32, .ofs_x = 1, .ofs_y = -4}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/

static const uint16_t unicode_list_1[] = {
    0x0, 0x93, 0xda, 0x135, 0x299, 0x29b, 0xca5, 0x19f2,
    0x1fb3, 0x285f, 0x3077, 0x388d, 0x3a7a, 0x3c6a, 0x3eac, 0x432c,
    0x447a, 0x4965
};

/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] =
{
    {
        .range_start = 32, .range_length = 95, .glyph_id_start = 1,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    },
    {
        .range_start = 20851, .range_length = 18790, .glyph_id_start = 96,
        .unicode_list = unicode_list_1, .glyph_id_ofs_list = NULL, .list_length = 18, .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY
    }
};

/*-----------------
 *    KERNING
 *----------------*/


/*Map glyph_ids to kern left classes*/
static const uint8_t kern_left_class_mapping[] =
{
    0, 0, 0, 1, 0, 0, 0, 0,
    1, 2, 0, 0, 0, 3, 4, 3,
    5, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 6, 6, 0, 0, 0,
    0, 0, 7, 8, 9, 10, 11, 12,
    13, 0, 0, 14, 15, 16, 0, 0,
    10, 17, 10, 18, 19, 20, 21, 22,
    23, 24, 25, 26, 2, 27, 0, 0,
    0, 0, 28, 29, 30, 0, 31, 32,
    33, 34, 0, 0, 35, 36, 34, 34,
    29, 29, 37, 38, 39, 40, 37, 41,
    42, 43, 44, 45, 2, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0
};

/*Map glyph_ids to kern right classes*/
static const uint8_t kern_right_class_mapping[] =
{
    0, 0, 1, 2, 0, 0, 0, 0,
    2, 0, 3, 4, 0, 5, 6, 7,
    8, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 9, 10, 0, 0, 0,
    11, 0, 12, 0, 13, 0, 0, 0,
    13, 0, 0, 14, 0, 0, 0, 0,
    13, 0, 13, 0, 15, 16, 17, 18,
    19, 20, 21, 22, 0, 23, 3, 0,
    0, 0, 24, 0, 25, 25, 25, 26,
    27, 0, 28, 29, 0, 0, 30, 30,
    25, 30, 25, 30, 31, 32, 33, 34,
    35, 36, 37, 38, 0, 0, 3, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0
};

/*Kern values between classes*/
static const int8_t kern_class_values[] =
{
    0, 0, 0, 0, -74, 0, -74, 0,
    0, 0, 0, -39, 0, -63, -6, 0,
    0, 0, 0, -6, 0, 0, 0, 0,
    -11, 0, 0, 0, 0, 0, -12, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -12, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 47, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -69, 0, -93,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -58, -15, -42, -21, 0,
    -62, 0, 0, 0, -6, 0, 0, 0,
    20, 0, 0, -34, 0, -31, -18, 0,
    -12, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -12,
    -14, -31, 0, -11, -6, -20, -42, -12,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -13, 0, -8, 0, -10, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -23, -6, -46, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -16,
    -12, 0, -6, 7, 7, 0, 0, 0,
    -12, 0, 0, 0, 0, 0, 0, 0,
    0, -20, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -19, 0, 0, 0, 0, 0,
    0, 0, 3, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -39, 0, -50,
    0, 0, 0, 0, 0, 0, -19, -1,
    -6, 0, 0, -39, -7, -10, 0, -2,
    -10, -2, -23, 10, 0, -6, 0, 0,
    0, 0, 10, -10, -1, -8, -4, -4,
    -8, 0, 0, 0, 0, -17, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -11,
    -10, -16, 0, -6, -4, -4, -10, -4,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -6, 0, -10, -6, -6, -10, 0,
    0, 0, 0, 0, 0, -19, 0, 0,
    0, 0, 0, 0, -20, -6, -16, -8,
    -10, -4, -4, -4, -8, -6, 0, 0,
    0, 0, -12, 0, 0, 0, 0, -20,
    -6, -10, -6, 0, -10, 0, 0, 0,
    0, -15, 0, 0, 0, -8, 0, 0,
    0, -6, 0, -29, 0, -16, 0, -6,
    -1, -14, -12, -12, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -10, 0, -8, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -6, 0, 0, 0,
    0, 0, 0, -16, 0, -6, 0, -23,
    -6, 0, 0, 0, 0, 0, -51, 0,
    -51, -31, 0, 0, 0, -22, -6, -78,
    -15, 0, 0, -2, -2, -16, -6, -17,
    0, -21, -10, 0, -16, 0, 0, -12,
    -15, -6, -11, -18, -14, -19, -14, -26,
    0, 0, 0, -14, 0, 0, 0, 0,
    0, 0, 0, -4, 0, 0, 0, -12,
    0, -10, -4, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -16, 0, -16, 0, 0, 0,
    0, 0, 0, -23, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -18, 0, -23,
    0, -26, 0, 0, 0, 0, -8, -6,
    -15, 0, -10, -14, -10, -10, -6, 0,
    -14, 0, 0, 0, -8, 0, 0, 0,
    -6, 0, 0, -28, -7, -18, -14, -14,
    -18, -10, 0, -73, 0, -97, 0, -27,
    0, 0, 0, 0, -27, -2, -19, 0,
    -15, -70, -20, -45, -34, 0, -48, 0,
    -46, 0, -8, -10, -4, 0, 0, 0,
    0, -15, -6, -28, -21, 0, -28, 0,
    0, 0, 0, 0, -74, -15, -74, -33,
    0, 0, 0, -29, 0, -83, -6, -10,
    0, 0, 0, -16, -6, -34, 0, -21,
    -13, 0, -12, 0, 0, 0, -6, 0,
    0, 0, 0, -10, 0, -12, 0, 0,
    0, -6, 0, -18, 0, 0, 0, 0,
    0, -4, 0, -8, -8, -10, 0, -4,
    2, -4, -6, -6, 0, -4, -6, 0,
    -4, 0, 0, 0, 0, 0, 0, 0,
    0, -8, 0, -8, 0, 0, 0, -8,
    0, 10, 0, 0, 0, 0, 0, 0,
    0, -10, -10, -12, 0, 0, 0, 0,
    -10, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -16, 0, 0, 0, 0,
    0, -6, 0, 0, 0, 0, -66, -38,
    -66, -43, -12, -12, 0, -23, -16, -72,
    -17, 0, 0, 0, 0, -12, -10, -27,
    0, -38, -42, -8, -38, 0, 0, -25,
    -34, -8, -25, -15, -15, -17, -15, -39,
    0, 0, 0, 0, -14, 0, -14, -12,
    0, 0, 0, -8, 0, -31, -6, 0,
    0, -4, 0, -6, -10, 0, 0, -4,
    0, 0, -6, 0, 0, 0, -4, 0,
    0, 0, 0, -8, 0, 0, 0, 0,
    0, 0, -42, -11, -42, -19, 0, 0,
    0, -10, -6, -38, -6, 0, -6, 6,
    0, 0, 0, -11, 0, -18, -10, 0,
    -12, 0, 0, -12, -10, 0, -17, -6,
    -6, -10, -6, -13, 0, 0, 0, 0,
    -21, -6, -21, -8, 0, 0, 0, 0,
    -1, -30, -1, 0, 0, 0, 0, 0,
    0, -1, 0, -8, 0, 0, -3, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -6, 0, -6, 0, -6, 0, -20,
    0, 0, 0, 0, 0, -2, -14, -6,
    -10, -12, -6, 0, 0, 0, 0, 0,
    0, -6, -8, -14, 0, 0, 0, 0,
    0, -14, -6, -14, -10, -6, -14, -10,
    0, 0, 0, 0, -61, -42, -61, -31,
    -19, -19, -8, -10, -10, -58, -11, -10,
    -6, 0, 0, 0, 0, -13, 0, -43,
    -30, 0, -35, 0, 0, -23, -30, -26,
    -21, -10, -16, -21, -10, -31, 0, 0,
    0, 0, 0, -15, 0, 0, 0, 0,
    0, -1, -12, -15, -17, 0, -6, -1,
    -1, 0, -10, -8, 0, -8, -9, -12,
    -7, 0, 0, 0, 0, -10, -10, -8,
    -8, -16, -8, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -42, -11, -26, -11, 0,
    -38, 0, 0, 0, 0, 0, 15, 0,
    39, 0, 0, 0, 0, -12, -6, 0,
    4, 0, 0, 0, 0, -27, 0, 0,
    0, 0, 0, 0, -16, 0, 0, 0,
    0, -16, 0, -14, -4, 0, -16, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -10, 0, 0, 0, 0, 0, 0,
    0, -11, 0, -7, -6, 2, -6, 0,
    0, 0, -16, 0, 0, 0, 0, -34,
    0, -11, 0, -4, -31, 0, -19, -7,
    0, -2, 0, 0, 0, 0, -2, -14,
    0, -4, -4, -14, -4, -6, 0, 0,
    0, 0, 0, -18, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -12, 0, -10,
    0, 0, -16, 0, 0, -6, -15, 0,
    -6, 0, 0, 0, 0, -6, 0, 2,
    2, 0, 2, 0, 0, 0, 0, -15,
    0, 6, 0, 0, 0, 0, -8, 0,
    0, -12, -12, -16, 0, -14, -6, 0,
    -19, 0, -18, -7, 0, -2, -6, 0,
    0, 0, 0, -6, 0, -2, -2, -8,
    -2, -3, 4, 23, 23, 0, -35, -10,
    -35, -3, 0, 0, 13, 0, 0, 0,
    0, 25, 0, 36, 25, 15, 29, 0,
    23, -12, -6, 0, -7, 0, -6, 0,
    -4, 0, 0, 4, 0, -4, 0, -10,
    0, 0, 4, -15, 0, 0, 0, 20,
    0, 0, -25, 0, 0, 0, 0, -19,
    0, 0, 0, 0, -10, 0, 0, -11,
    -10, 0, 0, 0, 26, 0, 0, 0,
    0, -4, -4, 0, 4, -10, 0, 0,
    0, -15, 0, 0, 0, 0, 0, 0,
    -8, 0, 0, 0, 0, -16, 0, -6,
    0, 0, -14, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -12,
    4, -42, 4, 0, 4, 4, -16, 0,
    0, 0, 0, -28, 0, 0, 0, 0,
    -9, 0, 0, -6, -15, 0, -6, 0,
    -6, 0, 0, -18, -10, 0, 0, -8,
    0, -8, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 6, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -10, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -12,
    0, -10, 0, 0, -17, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -46, -16, -46, -15, 7, 7,
    0, -10, 0, -39, 0, 0, 0, 0,
    0, 0, 0, -6, 4, -16, -6, 0,
    -6, 0, 0, 0, -4, 0, 0, 7,
    5, 0, 7, -4, 0, 0, 0, -20,
    0, 6, 0, 0, 0, 0, -12, 0,
    0, 0, 0, -16, 0, -6, 0, 0,
    -12, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -12, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -4, -19,
    -4, 0, 4, 4, -19, 0, 0, 0,
    0, -10, 0, 0, 0, 0, -4, 0,
    0, -12, -10, 0, -6, 0, 0, 0,
    -6, -12, 0, 0, 0, -8, 0, 0,
    0, 0, 0, -6, -31, -8, -31, -12,
    0, 0, 0, -7, 0, -23, 0, -12,
    0, -6, 0, 0, -10, -6, 0, -12,
    -4, 0, 0, 0, -6, 0, 0, 0,
    0, 0, 0, 0, 0, -16, 0, 0,
    0, -6, -42, 0, -42, -2, 0, 0,
    0, -4, 0, -17, 0, -16, 0, -6,
    0, -10, -16, 0, 0, -6, -4, 0,
    0, 0, -6, 0, 0, 0, 0, 0,
    0, 0, 0, -14, -10, 0, 0, -12,
    8, -10, -8, 0, 0, 8, 0, 0,
    -6, 0, -4, -15, 0, -10, 0, -6,
    -21, 0, 0, -6, -14, 0, 0, 0,
    0, 0, 0, -16, 0, 0, 0, 0,
    -6, 0, 0, 0, 0, 0, -31, 0,
    -31, -3, 0, 0, 0, 0, 0, -23,
    0, -12, 0, -4, 0, -4, -8, 0,
    0, -12, -4, 0, 0, 0, -6, 0,
    0, 0, 0, 0, 0, -10, 0, -16,
    0, 0, 0, 0, 0, -14, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -13,
    0, 0, 0, 0, -11, 0, 0, -10,
    -6, 0, -1, 0, 0, 0, 0, 0,
    -6, -4, 0, 0, -4, 0
};


/*Collect the kern class' data in one place*/
static const lv_font_fmt_txt_kern_classes_t kern_classes =
{
    .class_pair_values   = kern_class_values,
    .left_class_mapping  = kern_left_class_mapping,
    .right_class_mapping = kern_right_class_mapping,
    .left_class_cnt      = 45,
    .right_class_cnt     = 38,
};

/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LVGL_VERSION_MAJOR == 8
/*Store all the custom data of the font*/
static  lv_font_fmt_txt_glyph_cache_t cache;
#endif

#if LVGL_VERSION_MAJOR >= 8
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = &kern_classes,
    .kern_scale = 16,
    .cmap_num = 2,
    .bpp = 4,
    .kern_classes = 1,
    .bitmap_format = 0,
#if LVGL_VERSION_MAJOR == 8
    .cache = &cache
#endif
};



/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LVGL_VERSION_MAJOR >= 8
const lv_font_t font_source_han_sans_bold_32 = {
#else
lv_font_t font_source_han_sans_bold_32 = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 40,          /*The maximum line height required by the font*/
    .base_line = 9,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = -5,
    .underline_thickness = 2,
#endif
    .dsc = &font_dsc,          /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
#if LV_VERSION_CHECK(8, 2, 0) || LVGL_VERSION_MAJOR >= 9
    .fallback = NULL,
#endif
    .user_data = NULL,
};



#endif /*#if FONT_SOURCE_HAN_SANS_BOLD_32*/

