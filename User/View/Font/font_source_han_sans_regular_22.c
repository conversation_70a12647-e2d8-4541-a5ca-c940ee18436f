/*******************************************************************************
 * Size: 22 px
 * Bpp: 4
 * Opts: --bpp 4 --size 22 --no-compress --font SourceHanSansCN-Regular.ttf --range 32-127,20851,20998,21021,21270,21407,21487,21516,21542,22238,22312,22791,22823,22987,23548,23558,25152,25321,25353,25454,25509,25552,25910,25968,26159,26377,26597,27491,27493,28431,30005,30340,30475,31034,31163,31186,31471,33021,33267,33268,34987,35299,35774,35823,35831,36317,36824,36873,37327,38047,38145,38271,38378,38381,38899 --format lvgl -o font_source_han_sans_regular_22.c
 ******************************************************************************/

#ifdef LV_LVGL_H_INCLUDE_SIMPLE
#include "lvgl.h"
#else
#include "lvgl.h"
#endif

#ifndef FONT_SOURCE_HAN_SANS_REGULAR_22
#define FONT_SOURCE_HAN_SANS_REGULAR_22 1
#endif

#if FONT_SOURCE_HAN_SANS_REGULAR_22

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+0020 " " */

    /* U+0021 "!" */
    0x7f, 0x87, 0xf8, 0x6f, 0x86, 0xf7, 0x5f, 0x75,
    0xf6, 0x4f, 0x64, 0xf5, 0x3f, 0x53, 0xf4, 0x2f,
    0x40, 0x61, 0x0, 0x7, 0xf8, 0xdf, 0xf7, 0xf8,

    /* U+0022 "\"" */
    0xf, 0xf1, 0xb, 0xf6, 0xf, 0xf0, 0xa, 0xf6,
    0xf, 0xf0, 0xa, 0xf5, 0xd, 0xe0, 0x8, 0xf3,
    0xc, 0xd0, 0x6, 0xf2, 0xa, 0xb0, 0x5, 0xf0,
    0x1, 0x20, 0x0, 0x30,

    /* U+0023 "#" */
    0x0, 0x0, 0xe6, 0x0, 0x5f, 0x0, 0x0, 0x0,
    0xf4, 0x0, 0x7d, 0x0, 0x0, 0x2, 0xf1, 0x0,
    0x9b, 0x0, 0x0, 0x5, 0xf0, 0x0, 0xb9, 0x0,
    0xa, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x3, 0x5b,
    0xd5, 0x55, 0xf8, 0x52, 0x0, 0xa, 0xa0, 0x1,
    0xf3, 0x0, 0x0, 0xc, 0x80, 0x2, 0xf2, 0x0,
    0x0, 0xd, 0x60, 0x4, 0xf0, 0x0, 0x1, 0x1f,
    0x61, 0x16, 0xf1, 0x10, 0x2f, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0x4, 0x7f, 0x54, 0x4b, 0xc4, 0x40,
    0x0, 0x5f, 0x0, 0xc, 0x90, 0x0, 0x0, 0x7d,
    0x0, 0xe, 0x70, 0x0, 0x0, 0x9b, 0x0, 0xf,
    0x50, 0x0, 0x0, 0xb9, 0x0, 0x2f, 0x30, 0x0,

    /* U+0024 "$" */
    0x0, 0x0, 0x8e, 0x0, 0x0, 0x0, 0x0, 0x8e,
    0x0, 0x0, 0x0, 0x4, 0xcf, 0x72, 0x0, 0x0,
    0xbf, 0xff, 0xff, 0x80, 0x8, 0xfb, 0x20, 0x3b,
    0xf2, 0xe, 0xf1, 0x0, 0x0, 0x20, 0xf, 0xe0,
    0x0, 0x0, 0x0, 0xf, 0xf2, 0x0, 0x0, 0x0,
    0x8, 0xfd, 0x30, 0x0, 0x0, 0x0, 0xaf, 0xfa,
    0x20, 0x0, 0x0, 0x4, 0xdf, 0xf9, 0x0, 0x0,
    0x0, 0x6, 0xef, 0xc0, 0x0, 0x0, 0x0, 0x1e,
    0xf6, 0x0, 0x0, 0x0, 0x6, 0xfa, 0x0, 0x0,
    0x0, 0x4, 0xfb, 0x6, 0x0, 0x0, 0x7, 0xf9,
    0x7f, 0xc4, 0x11, 0x5f, 0xf2, 0x9, 0xff, 0xff,
    0xff, 0x50, 0x0, 0x16, 0xcf, 0x61, 0x0, 0x0,
    0x0, 0x8e, 0x0, 0x0, 0x0, 0x0, 0x8e, 0x0,
    0x0,

    /* U+0025 "%" */
    0x0, 0x5d, 0xfd, 0x60, 0x0, 0x0, 0x6, 0xe0,
    0x0, 0x0, 0x4, 0xf9, 0x49, 0xf5, 0x0, 0x0,
    0xe, 0x60, 0x0, 0x0, 0xc, 0xc0, 0x0, 0xcd,
    0x0, 0x0, 0x8d, 0x0, 0x0, 0x0, 0xf, 0x70,
    0x0, 0x7f, 0x10, 0x1, 0xf4, 0x0, 0x0, 0x0,
    0x1f, 0x60, 0x0, 0x6f, 0x20, 0xa, 0xb0, 0x0,
    0x0, 0x0, 0xf, 0x70, 0x0, 0x7f, 0x10, 0x3f,
    0x30, 0x0, 0x0, 0x0, 0xd, 0xb0, 0x0, 0xbe,
    0x0, 0xba, 0x2, 0xcf, 0xe8, 0x0, 0x6, 0xf6,
    0x6, 0xf7, 0x4, 0xf1, 0x1e, 0xc4, 0x7f, 0x80,
    0x0, 0x8f, 0xff, 0x90, 0xd, 0x80, 0x8f, 0x10,
    0x9, 0xf0, 0x0, 0x0, 0x20, 0x0, 0x6e, 0x0,
    0xcb, 0x0, 0x3, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0xe6, 0x0, 0xea, 0x0, 0x2, 0xf6, 0x0, 0x0,
    0x0, 0x8, 0xd0, 0x0, 0xea, 0x0, 0x2, 0xf6,
    0x0, 0x0, 0x0, 0x1f, 0x40, 0x0, 0xcb, 0x0,
    0x4, 0xf4, 0x0, 0x0, 0x0, 0xab, 0x0, 0x0,
    0x8f, 0x10, 0x9, 0xf0, 0x0, 0x0, 0x3, 0xf2,
    0x0, 0x0, 0x1e, 0xc4, 0x7f, 0x80, 0x0, 0x0,
    0xc, 0x90, 0x0, 0x0, 0x2, 0xbf, 0xe7, 0x0,

    /* U+0026 "&" */
    0x0, 0x0, 0x5d, 0xfd, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xc6, 0xbf, 0x50, 0x0, 0x0, 0x0,
    0xe, 0xe0, 0x0, 0xea, 0x0, 0x0, 0x0, 0x0,
    0xfa, 0x0, 0xe, 0xb0, 0x0, 0x0, 0x0, 0xf,
    0xc0, 0x4, 0xf6, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0x15, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x5, 0xfc,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xf7,
    0x0, 0x0, 0x5, 0x92, 0x0, 0x9f, 0xdf, 0xc0,
    0x0, 0x0, 0xee, 0x0, 0x8f, 0xb0, 0x7f, 0xa0,
    0x0, 0x4f, 0x80, 0xf, 0xf1, 0x0, 0xaf, 0xa0,
    0xd, 0xf2, 0x2, 0xfc, 0x0, 0x0, 0xbf, 0xb8,
    0xf8, 0x0, 0x1f, 0xe0, 0x0, 0x0, 0xaf, 0xfd,
    0x0, 0x0, 0xcf, 0x90, 0x0, 0x7, 0xff, 0xf9,
    0x10, 0x2, 0xef, 0xea, 0xae, 0xfc, 0x6d, 0xff,
    0x60, 0x1, 0x9d, 0xfe, 0xb6, 0x0, 0x6, 0xc3,

    /* U+0027 "'" */
    0xf, 0xf1, 0xf, 0xf0, 0xf, 0xf0, 0xd, 0xe0,
    0xc, 0xd0, 0xa, 0xb0, 0x1, 0x20,

    /* U+0028 "(" */
    0x0, 0x7, 0x10, 0x5, 0xf3, 0x0, 0xea, 0x0,
    0x5f, 0x30, 0xb, 0xd0, 0x1, 0xf8, 0x0, 0x5f,
    0x40, 0x9, 0xf1, 0x0, 0xce, 0x0, 0xd, 0xd0,
    0x0, 0xec, 0x0, 0xf, 0xb0, 0x0, 0xec, 0x0,
    0xd, 0xd0, 0x0, 0xbf, 0x0, 0x8, 0xf2, 0x0,
    0x4f, 0x50, 0x0, 0xfa, 0x0, 0x9, 0xf0, 0x0,
    0x3f, 0x50, 0x0, 0xbd, 0x0, 0x2, 0xf4, 0x0,
    0x1, 0x0,

    /* U+0029 ")" */
    0x5, 0x30, 0x0, 0xc, 0xc0, 0x0, 0x4, 0xf5,
    0x0, 0x0, 0xcc, 0x0, 0x0, 0x7f, 0x20, 0x0,
    0x1f, 0x80, 0x0, 0xd, 0xc0, 0x0, 0xa, 0xf0,
    0x0, 0x7, 0xf3, 0x0, 0x5, 0xf4, 0x0, 0x4,
    0xf5, 0x0, 0x4, 0xf6, 0x0, 0x5, 0xf5, 0x0,
    0x6, 0xf4, 0x0, 0x8, 0xf2, 0x0, 0xb, 0xe0,
    0x0, 0xe, 0xb0, 0x0, 0x3f, 0x60, 0x0, 0x9f,
    0x10, 0x0, 0xea, 0x0, 0x7, 0xf2, 0x0, 0xd,
    0x90, 0x0, 0x0, 0x0, 0x0,

    /* U+002A "*" */
    0x0, 0x8, 0xc0, 0x0, 0x0, 0x9, 0xd0, 0x0,
    0x5d, 0x9c, 0xf8, 0xc9, 0x18, 0xef, 0xff, 0xa3,
    0x0, 0x7f, 0xfb, 0x0, 0x1, 0xfa, 0x6f, 0x40,
    0x4, 0xb0, 0x8, 0x70,

    /* U+002B "+" */
    0x0, 0x0, 0x9, 0xd0, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xe0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xe0, 0x0, 0x0,
    0x2, 0x22, 0x2b, 0xe2, 0x22, 0x20, 0x2f, 0xff,
    0xff, 0xff, 0xff, 0xf6, 0x5, 0x55, 0x5c, 0xe5,
    0x55, 0x52, 0x0, 0x0, 0xb, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xe0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xe0,
    0x0, 0x0,

    /* U+002C "," */
    0x9, 0xa2, 0x4f, 0xfa, 0x1d, 0xfd, 0x0, 0xbb,
    0x2, 0xf6, 0x4e, 0xb0, 0x89, 0x0, 0x0, 0x0,

    /* U+002D "-" */
    0x78, 0x88, 0x85, 0xff, 0xff, 0xfa,

    /* U+002E "." */
    0x1d, 0xe2, 0x5f, 0xf7, 0x1d, 0xe2,

    /* U+002F "/" */
    0x0, 0x0, 0x0, 0x5f, 0x20, 0x0, 0x0, 0xa,
    0xd0, 0x0, 0x0, 0x0, 0xe8, 0x0, 0x0, 0x0,
    0x4f, 0x30, 0x0, 0x0, 0x9, 0xe0, 0x0, 0x0,
    0x0, 0xe9, 0x0, 0x0, 0x0, 0x3f, 0x40, 0x0,
    0x0, 0x8, 0xe0, 0x0, 0x0, 0x0, 0xda, 0x0,
    0x0, 0x0, 0x2f, 0x50, 0x0, 0x0, 0x7, 0xf0,
    0x0, 0x0, 0x0, 0xcb, 0x0, 0x0, 0x0, 0x1f,
    0x60, 0x0, 0x0, 0x6, 0xf1, 0x0, 0x0, 0x0,
    0xbc, 0x0, 0x0, 0x0, 0xf, 0x70, 0x0, 0x0,
    0x5, 0xf2, 0x0, 0x0, 0x0, 0xad, 0x0, 0x0,
    0x0, 0xf, 0x80, 0x0, 0x0, 0x4, 0xf3, 0x0,
    0x0, 0x0, 0x9d, 0x0, 0x0, 0x0, 0x0,

    /* U+0030 "0" */
    0x0, 0x2a, 0xef, 0xc4, 0x0, 0x0, 0x3f, 0xfb,
    0xaf, 0xf5, 0x0, 0xd, 0xf5, 0x0, 0x2f, 0xf1,
    0x4, 0xfb, 0x0, 0x0, 0x7f, 0x70, 0x9f, 0x60,
    0x0, 0x2, 0xfc, 0xb, 0xf3, 0x0, 0x0, 0xf,
    0xf0, 0xdf, 0x10, 0x0, 0x0, 0xdf, 0xe, 0xf0,
    0x0, 0x0, 0xd, 0xf1, 0xef, 0x10, 0x0, 0x0,
    0xdf, 0x1d, 0xf1, 0x0, 0x0, 0xd, 0xf0, 0xbf,
    0x30, 0x0, 0x0, 0xfe, 0x8, 0xf6, 0x0, 0x0,
    0x2f, 0xc0, 0x4f, 0xb0, 0x0, 0x8, 0xf7, 0x0,
    0xdf, 0x50, 0x2, 0xff, 0x10, 0x3, 0xff, 0xba,
    0xff, 0x50, 0x0, 0x2, 0xae, 0xfc, 0x40, 0x0,

    /* U+0031 "1" */
    0x0, 0x5, 0xcf, 0x80, 0x0, 0x4, 0xff, 0xff,
    0x80, 0x0, 0x2, 0x66, 0xaf, 0x80, 0x0, 0x0,
    0x0, 0x7f, 0x80, 0x0, 0x0, 0x0, 0x7f, 0x80,
    0x0, 0x0, 0x0, 0x7f, 0x80, 0x0, 0x0, 0x0,
    0x7f, 0x80, 0x0, 0x0, 0x0, 0x7f, 0x80, 0x0,
    0x0, 0x0, 0x7f, 0x80, 0x0, 0x0, 0x0, 0x7f,
    0x80, 0x0, 0x0, 0x0, 0x7f, 0x80, 0x0, 0x0,
    0x0, 0x7f, 0x80, 0x0, 0x0, 0x0, 0x7f, 0x80,
    0x0, 0x0, 0x0, 0x7f, 0x80, 0x0, 0xa, 0xaa,
    0xcf, 0xda, 0xa8, 0x1f, 0xff, 0xff, 0xff, 0xfc,

    /* U+0032 "2" */
    0x0, 0x19, 0xdf, 0xea, 0x30, 0x0, 0x4, 0xff,
    0xba, 0xcf, 0xf4, 0x0, 0xc, 0xd3, 0x0, 0x7,
    0xfe, 0x0, 0x1, 0x10, 0x0, 0x0, 0xdf, 0x30,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0x50, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0x30, 0x0, 0x0, 0x0, 0x0,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x6, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0x1e, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0x80, 0x0, 0x0, 0x0, 0x8, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0xd1, 0x0, 0x0,
    0x0, 0x7, 0xfe, 0x10, 0x0, 0x0, 0x0, 0x8f,
    0xd1, 0x0, 0x0, 0x0, 0xa, 0xff, 0xaa, 0xbb,
    0xbb, 0xb1, 0xf, 0xff, 0xff, 0xff, 0xff, 0xf1,

    /* U+0033 "3" */
    0x0, 0x18, 0xdf, 0xeb, 0x50, 0x0, 0x5f, 0xfc,
    0xab, 0xff, 0x80, 0x3, 0xd4, 0x0, 0x3, 0xff,
    0x20, 0x0, 0x0, 0x0, 0xb, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0x50, 0x0, 0x0, 0x0, 0x1f,
    0xf1, 0x0, 0x0, 0x2, 0x6e, 0xf5, 0x0, 0x0,
    0x1f, 0xff, 0xe3, 0x0, 0x0, 0x0, 0x89, 0xcf,
    0xe5, 0x0, 0x0, 0x0, 0x0, 0x3e, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xc0, 0x0, 0x0, 0x0,
    0x1, 0xff, 0x2, 0x0, 0x0, 0x0, 0x4f, 0xd1,
    0xec, 0x10, 0x0, 0x2d, 0xf8, 0x9, 0xff, 0xca,
    0xcf, 0xfb, 0x0, 0x3, 0xae, 0xff, 0xc6, 0x0,

    /* U+0034 "4" */
    0x0, 0x0, 0x0, 0x3f, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0xf6, 0x0, 0x0, 0x0, 0x7, 0xfc,
    0xf6, 0x0, 0x0, 0x0, 0x2f, 0xc7, 0xf6, 0x0,
    0x0, 0x0, 0xcf, 0x38, 0xf6, 0x0, 0x0, 0x7,
    0xf8, 0x8, 0xf6, 0x0, 0x0, 0x2f, 0xd0, 0x8,
    0xf6, 0x0, 0x0, 0xbf, 0x30, 0x8, 0xf6, 0x0,
    0x6, 0xf8, 0x0, 0x8, 0xf6, 0x0, 0x1f, 0xd0,
    0x0, 0x8, 0xf6, 0x0, 0x8f, 0xff, 0xff, 0xff,
    0xff, 0xf8, 0x59, 0x99, 0x99, 0x9c, 0xfb, 0x94,
    0x0, 0x0, 0x0, 0x8, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xf6, 0x0, 0x0, 0x0, 0x0, 0x8, 0xf6, 0x0,

    /* U+0035 "5" */
    0x0, 0x9f, 0xff, 0xff, 0xff, 0x40, 0x0, 0xaf,
    0xbb, 0xbb, 0xbb, 0x20, 0x0, 0xcf, 0x10, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xee, 0x0, 0x0, 0x0, 0x0, 0x0, 0xfd,
    0x0, 0x10, 0x0, 0x0, 0x0, 0xfe, 0xdf, 0xff,
    0xa1, 0x0, 0x0, 0xee, 0x86, 0x7d, 0xfe, 0x10,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xf0,
    0x1, 0x0, 0x0, 0x0, 0x6f, 0xc0, 0x2e, 0x91,
    0x0, 0x3, 0xff, 0x40, 0xa, 0xff, 0xca, 0xcf,
    0xf7, 0x0, 0x0, 0x4a, 0xef, 0xeb, 0x40, 0x0,

    /* U+0036 "6" */
    0x0, 0x4, 0xbe, 0xfd, 0x70, 0x0, 0x8, 0xff,
    0xba, 0xdf, 0xa0, 0x5, 0xfd, 0x10, 0x0, 0x72,
    0x0, 0xef, 0x10, 0x0, 0x0, 0x0, 0x4f, 0xa0,
    0x0, 0x0, 0x0, 0x7, 0xf5, 0x0, 0x11, 0x0,
    0x0, 0xaf, 0x36, 0xef, 0xfd, 0x50, 0xb, 0xfb,
    0xe8, 0x58, 0xff, 0x50, 0xbf, 0xd1, 0x0, 0x4,
    0xfd, 0xa, 0xf3, 0x0, 0x0, 0xd, 0xf2, 0x9f,
    0x40, 0x0, 0x0, 0xbf, 0x35, 0xf8, 0x0, 0x0,
    0xb, 0xf3, 0x1f, 0xd0, 0x0, 0x0, 0xef, 0x0,
    0x8f, 0x80, 0x0, 0x8f, 0x90, 0x0, 0xcf, 0xc9,
    0xcf, 0xd0, 0x0, 0x0, 0x7d, 0xfe, 0x80, 0x0,

    /* U+0037 "7" */
    0xef, 0xff, 0xff, 0xff, 0xff, 0x2a, 0xbb, 0xbb,
    0xbb, 0xcf, 0xe0, 0x0, 0x0, 0x0, 0xa, 0xf3,
    0x0, 0x0, 0x0, 0x4, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0xde, 0x0, 0x0, 0x0, 0x0, 0x6f, 0x70,
    0x0, 0x0, 0x0, 0xd, 0xf1, 0x0, 0x0, 0x0,
    0x3, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x8f, 0x60,
    0x0, 0x0, 0x0, 0xb, 0xf3, 0x0, 0x0, 0x0,
    0x0, 0xff, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xc0,
    0x0, 0x0, 0x0, 0x5, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0x90, 0x0, 0x0, 0x0, 0x8, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0x70, 0x0, 0x0,

    /* U+0038 "8" */
    0x0, 0x3b, 0xef, 0xd7, 0x0, 0x0, 0x5f, 0xf9,
    0x8c, 0xfb, 0x0, 0xe, 0xf2, 0x0, 0xb, 0xf4,
    0x2, 0xfb, 0x0, 0x0, 0x3f, 0x80, 0x2f, 0xa0,
    0x0, 0x2, 0xf9, 0x0, 0xdf, 0x20, 0x0, 0x5f,
    0x40, 0x3, 0xfe, 0x50, 0xd, 0xb0, 0x0, 0x4,
    0xff, 0xdc, 0xc0, 0x0, 0x5, 0xf9, 0x6d, 0xff,
    0x50, 0x3, 0xf9, 0x0, 0x5, 0xef, 0x40, 0xbf,
    0x10, 0x0, 0x2, 0xfe, 0xf, 0xd0, 0x0, 0x0,
    0xc, 0xf2, 0xee, 0x0, 0x0, 0x0, 0xdf, 0x19,
    0xf9, 0x0, 0x0, 0x5f, 0xd0, 0xc, 0xfd, 0x88,
    0xbf, 0xe3, 0x0, 0x6, 0xcf, 0xfd, 0x81, 0x0,

    /* U+0039 "9" */
    0x0, 0x7, 0xdf, 0xd9, 0x10, 0x0, 0x0, 0xcf,
    0xd9, 0xaf, 0xe2, 0x0, 0x7, 0xfa, 0x0, 0x3,
    0xfd, 0x0, 0xd, 0xf1, 0x0, 0x0, 0x7f, 0x50,
    0xf, 0xe0, 0x0, 0x0, 0x2f, 0xa0, 0xf, 0xf0,
    0x0, 0x0, 0xf, 0xd0, 0xc, 0xf4, 0x0, 0x0,
    0x6f, 0xf0, 0x5, 0xfe, 0x63, 0x4a, 0xef, 0xf0,
    0x0, 0x6f, 0xff, 0xfb, 0x2e, 0xf0, 0x0, 0x0,
    0x34, 0x20, 0xf, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0x70,
    0x0, 0x0, 0x0, 0x0, 0xef, 0x10, 0x1, 0xb2,
    0x0, 0x1b, 0xf8, 0x0, 0x7, 0xff, 0xbb, 0xff,
    0xb0, 0x0, 0x0, 0x4b, 0xff, 0xc6, 0x0, 0x0,

    /* U+003A ":" */
    0x1d, 0xe2, 0x5f, 0xf7, 0x1d, 0xe2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1d, 0xe2, 0x5f, 0xf7, 0x1d, 0xe2,

    /* U+003B ";" */
    0x1d, 0xe2, 0x5f, 0xf7, 0x1d, 0xe2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9, 0xa2, 0x4f, 0xfa, 0x1d, 0xfd,
    0x0, 0xbb, 0x2, 0xf6, 0x4e, 0xb0, 0x89, 0x0,
    0x0, 0x0,

    /* U+003C "<" */
    0x0, 0x0, 0x0, 0x0, 0x1, 0x74, 0x0, 0x0,
    0x0, 0x5, 0xbf, 0xf5, 0x0, 0x0, 0x39, 0xef,
    0xe8, 0x20, 0x1, 0x6c, 0xff, 0xa4, 0x0, 0x0,
    0x2f, 0xfb, 0x50, 0x0, 0x0, 0x0, 0x2f, 0xfb,
    0x50, 0x0, 0x0, 0x0, 0x1, 0x6c, 0xff, 0xa4,
    0x0, 0x0, 0x0, 0x0, 0x39, 0xef, 0xe8, 0x30,
    0x0, 0x0, 0x0, 0x5, 0xbf, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0x2, 0x84,

    /* U+003D "=" */
    0x2f, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x17, 0x77,
    0x77, 0x77, 0x77, 0x72, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0x22, 0x22, 0x22, 0x22, 0x20, 0x2f, 0xff,
    0xff, 0xff, 0xff, 0xf6, 0x5, 0x55, 0x55, 0x55,
    0x55, 0x52,

    /* U+003E ">" */
    0x29, 0x30, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xfd,
    0x61, 0x0, 0x0, 0x0, 0x1, 0x7d, 0xff, 0xa4,
    0x0, 0x0, 0x0, 0x0, 0x38, 0xef, 0xe8, 0x20,
    0x0, 0x0, 0x0, 0x4, 0xaf, 0xf5, 0x0, 0x0,
    0x0, 0x4, 0xaf, 0xf5, 0x0, 0x0, 0x38, 0xef,
    0xe8, 0x20, 0x1, 0x7d, 0xff, 0xa4, 0x0, 0x0,
    0x2f, 0xfd, 0x71, 0x0, 0x0, 0x0, 0x29, 0x30,
    0x0, 0x0, 0x0, 0x0,

    /* U+003F "?" */
    0x0, 0x4b, 0xef, 0xc6, 0x0, 0x8, 0xfe, 0xbb,
    0xff, 0x90, 0x6, 0x90, 0x0, 0x3f, 0xf1, 0x0,
    0x0, 0x0, 0xc, 0xf3, 0x0, 0x0, 0x0, 0xd,
    0xf2, 0x0, 0x0, 0x0, 0x5f, 0xb0, 0x0, 0x0,
    0x2, 0xfe, 0x10, 0x0, 0x0, 0x1d, 0xf3, 0x0,
    0x0, 0x0, 0x9f, 0x70, 0x0, 0x0, 0x0, 0xfe,
    0x0, 0x0, 0x0, 0x1, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xec, 0x10, 0x0, 0x0, 0x8,
    0xff, 0x40, 0x0, 0x0, 0x3, 0xec, 0x10, 0x0,

    /* U+0040 "@" */
    0x0, 0x0, 0x0, 0x28, 0xce, 0xfe, 0xc7, 0x10,
    0x0, 0x0, 0x0, 0x1, 0xaf, 0xd8, 0x65, 0x79,
    0xef, 0x70, 0x0, 0x0, 0x4, 0xed, 0x50, 0x0,
    0x0, 0x0, 0x8f, 0x90, 0x0, 0x3, 0xfb, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0x50, 0x1, 0xec,
    0x0, 0x0, 0x0, 0x0, 0x10, 0x0, 0xbd, 0x0,
    0x8f, 0x20, 0x0, 0x4d, 0xff, 0x5f, 0x50, 0x4,
    0xf3, 0xf, 0x90, 0x0, 0x5f, 0xc5, 0x7f, 0xf2,
    0x0, 0xf, 0x65, 0xf3, 0x0, 0x1f, 0xb0, 0x0,
    0xaf, 0x0, 0x0, 0xe8, 0x9e, 0x0, 0x7, 0xf2,
    0x0, 0xc, 0xc0, 0x0, 0xe, 0x8a, 0xc0, 0x0,
    0xcd, 0x0, 0x0, 0xf9, 0x0, 0x0, 0xf6, 0xbb,
    0x0, 0xe, 0xb0, 0x0, 0x1f, 0x60, 0x0, 0x5f,
    0x2b, 0xc0, 0x0, 0xcd, 0x0, 0x6, 0xf5, 0x0,
    0xd, 0xb0, 0x9e, 0x0, 0x8, 0xf7, 0x17, 0xdd,
    0xb2, 0x3c, 0xe1, 0x5, 0xf3, 0x0, 0xb, 0xff,
    0xc1, 0x5f, 0xff, 0xc2, 0x0, 0x1f, 0xa0, 0x0,
    0x2, 0x30, 0x0, 0x13, 0x20, 0x0, 0x0, 0x7f,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xd7, 0x31, 0x13, 0x7e,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x3a, 0xff, 0xff,
    0xfe, 0x81, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x23, 0x31, 0x0, 0x0, 0x0, 0x0,

    /* U+0041 "A" */
    0x0, 0x0, 0xa, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xef, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0x6f, 0xa0, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0x1c, 0xf0, 0x0, 0x0, 0x0, 0x0, 0xfc, 0x7,
    0xf5, 0x0, 0x0, 0x0, 0x5, 0xf7, 0x2, 0xfb,
    0x0, 0x0, 0x0, 0xa, 0xf2, 0x0, 0xdf, 0x0,
    0x0, 0x0, 0xf, 0xd0, 0x0, 0x8f, 0x50, 0x0,
    0x0, 0x5f, 0x80, 0x0, 0x3f, 0xb0, 0x0, 0x0,
    0xbf, 0xa9, 0x99, 0x9f, 0xf1, 0x0, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xf6, 0x0, 0x6, 0xf8, 0x0,
    0x0, 0x4, 0xfb, 0x0, 0xb, 0xf3, 0x0, 0x0,
    0x0, 0xef, 0x10, 0x1f, 0xe0, 0x0, 0x0, 0x0,
    0xaf, 0x60, 0x6f, 0x90, 0x0, 0x0, 0x0, 0x5f,
    0xc0, 0xbf, 0x40, 0x0, 0x0, 0x0, 0xf, 0xf1,

    /* U+0042 "B" */
    0xcf, 0xff, 0xff, 0xea, 0x40, 0x0, 0xcf, 0xa9,
    0x9a, 0xcf, 0xf9, 0x0, 0xcf, 0x40, 0x0, 0x3,
    0xff, 0x30, 0xcf, 0x40, 0x0, 0x0, 0x9f, 0x70,
    0xcf, 0x40, 0x0, 0x0, 0x8f, 0x70, 0xcf, 0x40,
    0x0, 0x0, 0xcf, 0x30, 0xcf, 0x40, 0x0, 0x3a,
    0xfa, 0x0, 0xcf, 0xff, 0xff, 0xff, 0xa0, 0x0,
    0xcf, 0xa8, 0x89, 0xae, 0xfd, 0x20, 0xcf, 0x40,
    0x0, 0x0, 0x6f, 0xd0, 0xcf, 0x40, 0x0, 0x0,
    0xa, 0xf4, 0xcf, 0x40, 0x0, 0x0, 0x8, 0xf6,
    0xcf, 0x40, 0x0, 0x0, 0xc, 0xf4, 0xcf, 0x40,
    0x0, 0x0, 0x8f, 0xe0, 0xcf, 0xb9, 0x9a, 0xbf,
    0xfe, 0x30, 0xcf, 0xff, 0xff, 0xec, 0x71, 0x0,

    /* U+0043 "C" */
    0x0, 0x0, 0x5b, 0xef, 0xe9, 0x20, 0x0, 0x1,
    0xcf, 0xfd, 0xce, 0xff, 0x60, 0x1, 0xef, 0xb2,
    0x0, 0x4, 0xd5, 0x0, 0xaf, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0x70, 0x0, 0x0, 0x0, 0x0, 0xa, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x9, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0xa, 0xfb,
    0x0, 0x0, 0x0, 0x12, 0x0, 0x1e, 0xfb, 0x20,
    0x0, 0x4d, 0xe1, 0x0, 0x2d, 0xff, 0xdc, 0xef,
    0xf7, 0x0, 0x0, 0x6, 0xce, 0xfe, 0x92, 0x0,

    /* U+0044 "D" */
    0xcf, 0xff, 0xfe, 0xb7, 0x0, 0x0, 0xcf, 0xb9,
    0xac, 0xff, 0xe4, 0x0, 0xcf, 0x40, 0x0, 0x8,
    0xff, 0x30, 0xcf, 0x40, 0x0, 0x0, 0x8f, 0xd0,
    0xcf, 0x40, 0x0, 0x0, 0xe, 0xf4, 0xcf, 0x40,
    0x0, 0x0, 0x8, 0xf9, 0xcf, 0x40, 0x0, 0x0,
    0x5, 0xfb, 0xcf, 0x40, 0x0, 0x0, 0x4, 0xfc,
    0xcf, 0x40, 0x0, 0x0, 0x4, 0xfc, 0xcf, 0x40,
    0x0, 0x0, 0x6, 0xfb, 0xcf, 0x40, 0x0, 0x0,
    0x9, 0xf8, 0xcf, 0x40, 0x0, 0x0, 0xe, 0xf4,
    0xcf, 0x40, 0x0, 0x0, 0x9f, 0xd0, 0xcf, 0x40,
    0x0, 0x19, 0xff, 0x30, 0xcf, 0xba, 0xac, 0xff,
    0xe4, 0x0, 0xcf, 0xff, 0xfe, 0xb7, 0x0, 0x0,

    /* U+0045 "E" */
    0xcf, 0xff, 0xff, 0xff, 0xf8, 0xcf, 0xcb, 0xbb,
    0xbb, 0xb5, 0xcf, 0x40, 0x0, 0x0, 0x0, 0xcf,
    0x40, 0x0, 0x0, 0x0, 0xcf, 0x40, 0x0, 0x0,
    0x0, 0xcf, 0x40, 0x0, 0x0, 0x0, 0xcf, 0x40,
    0x0, 0x0, 0x0, 0xcf, 0xff, 0xff, 0xff, 0x50,
    0xcf, 0xcb, 0xbb, 0xbb, 0x40, 0xcf, 0x40, 0x0,
    0x0, 0x0, 0xcf, 0x40, 0x0, 0x0, 0x0, 0xcf,
    0x40, 0x0, 0x0, 0x0, 0xcf, 0x40, 0x0, 0x0,
    0x0, 0xcf, 0x40, 0x0, 0x0, 0x0, 0xcf, 0xcb,
    0xbb, 0xbb, 0xb8, 0xcf, 0xff, 0xff, 0xff, 0xfc,

    /* U+0046 "F" */
    0xcf, 0xff, 0xff, 0xff, 0xf8, 0xcf, 0xcb, 0xbb,
    0xbb, 0xb5, 0xcf, 0x40, 0x0, 0x0, 0x0, 0xcf,
    0x40, 0x0, 0x0, 0x0, 0xcf, 0x40, 0x0, 0x0,
    0x0, 0xcf, 0x40, 0x0, 0x0, 0x0, 0xcf, 0x40,
    0x0, 0x0, 0x0, 0xcf, 0xcb, 0xbb, 0xbb, 0x40,
    0xcf, 0xff, 0xff, 0xff, 0x60, 0xcf, 0x40, 0x0,
    0x0, 0x0, 0xcf, 0x40, 0x0, 0x0, 0x0, 0xcf,
    0x40, 0x0, 0x0, 0x0, 0xcf, 0x40, 0x0, 0x0,
    0x0, 0xcf, 0x40, 0x0, 0x0, 0x0, 0xcf, 0x40,
    0x0, 0x0, 0x0, 0xcf, 0x40, 0x0, 0x0, 0x0,

    /* U+0047 "G" */
    0x0, 0x0, 0x4a, 0xdf, 0xeb, 0x50, 0x0, 0x1,
    0xbf, 0xfe, 0xcd, 0xff, 0xb0, 0x0, 0xdf, 0xc3,
    0x0, 0x1, 0xab, 0x0, 0x9f, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0x70, 0x0, 0x0, 0x0, 0x0, 0xa, 0xf6, 0x0,
    0x0, 0x8a, 0xaa, 0xa5, 0xaf, 0x60, 0x0, 0xc,
    0xff, 0xff, 0x89, 0xf7, 0x0, 0x0, 0x0, 0x5,
    0xf8, 0x6f, 0xb0, 0x0, 0x0, 0x0, 0x5f, 0x81,
    0xff, 0x20, 0x0, 0x0, 0x5, 0xf8, 0xa, 0xfb,
    0x0, 0x0, 0x0, 0x5f, 0x80, 0x1e, 0xfc, 0x30,
    0x0, 0x1a, 0xf8, 0x0, 0x1c, 0xff, 0xec, 0xdf,
    0xfe, 0x30, 0x0, 0x5, 0xbe, 0xfe, 0xc7, 0x0,

    /* U+0048 "H" */
    0xcf, 0x40, 0x0, 0x0, 0x3, 0xfd, 0xcf, 0x40,
    0x0, 0x0, 0x3, 0xfd, 0xcf, 0x40, 0x0, 0x0,
    0x3, 0xfd, 0xcf, 0x40, 0x0, 0x0, 0x3, 0xfd,
    0xcf, 0x40, 0x0, 0x0, 0x3, 0xfd, 0xcf, 0x40,
    0x0, 0x0, 0x3, 0xfd, 0xcf, 0x40, 0x0, 0x0,
    0x3, 0xfd, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0xcf, 0xcb, 0xbb, 0xbb, 0xbc, 0xfd, 0xcf, 0x40,
    0x0, 0x0, 0x3, 0xfd, 0xcf, 0x40, 0x0, 0x0,
    0x3, 0xfd, 0xcf, 0x40, 0x0, 0x0, 0x3, 0xfd,
    0xcf, 0x40, 0x0, 0x0, 0x3, 0xfd, 0xcf, 0x40,
    0x0, 0x0, 0x3, 0xfd, 0xcf, 0x40, 0x0, 0x0,
    0x3, 0xfd, 0xcf, 0x40, 0x0, 0x0, 0x3, 0xfd,

    /* U+0049 "I" */
    0xcf, 0x4c, 0xf4, 0xcf, 0x4c, 0xf4, 0xcf, 0x4c,
    0xf4, 0xcf, 0x4c, 0xf4, 0xcf, 0x4c, 0xf4, 0xcf,
    0x4c, 0xf4, 0xcf, 0x4c, 0xf4, 0xcf, 0x4c, 0xf4,

    /* U+004A "J" */
    0x0, 0x0, 0x0, 0x6, 0xfa, 0x0, 0x0, 0x0,
    0x6, 0xfa, 0x0, 0x0, 0x0, 0x6, 0xfa, 0x0,
    0x0, 0x0, 0x6, 0xfa, 0x0, 0x0, 0x0, 0x6,
    0xfa, 0x0, 0x0, 0x0, 0x6, 0xfa, 0x0, 0x0,
    0x0, 0x6, 0xfa, 0x0, 0x0, 0x0, 0x6, 0xfa,
    0x0, 0x0, 0x0, 0x6, 0xfa, 0x0, 0x0, 0x0,
    0x6, 0xfa, 0x0, 0x0, 0x0, 0x6, 0xfa, 0x0,
    0x0, 0x0, 0x6, 0xf9, 0x1, 0x30, 0x0, 0x9,
    0xf7, 0xe, 0xe2, 0x0, 0x3f, 0xf3, 0x7, 0xff,
    0xdd, 0xff, 0x90, 0x0, 0x4b, 0xef, 0xd7, 0x0,

    /* U+004B "K" */
    0xcf, 0x40, 0x0, 0x0, 0x6f, 0xd1, 0xc, 0xf4,
    0x0, 0x0, 0x3f, 0xf2, 0x0, 0xcf, 0x40, 0x0,
    0x2e, 0xf4, 0x0, 0xc, 0xf4, 0x0, 0xd, 0xf6,
    0x0, 0x0, 0xcf, 0x40, 0xb, 0xf9, 0x0, 0x0,
    0xc, 0xf4, 0x9, 0xfb, 0x0, 0x0, 0x0, 0xcf,
    0x46, 0xff, 0x80, 0x0, 0x0, 0xc, 0xf8, 0xff,
    0xff, 0x20, 0x0, 0x0, 0xcf, 0xff, 0x58, 0xfb,
    0x0, 0x0, 0xc, 0xff, 0x70, 0xe, 0xf5, 0x0,
    0x0, 0xcf, 0x90, 0x0, 0x5f, 0xd0, 0x0, 0xc,
    0xf4, 0x0, 0x0, 0xcf, 0x70, 0x0, 0xcf, 0x40,
    0x0, 0x3, 0xff, 0x10, 0xc, 0xf4, 0x0, 0x0,
    0x9, 0xfa, 0x0, 0xcf, 0x40, 0x0, 0x0, 0x1e,
    0xf3, 0xc, 0xf4, 0x0, 0x0, 0x0, 0x6f, 0xd0,

    /* U+004C "L" */
    0xcf, 0x40, 0x0, 0x0, 0x0, 0xcf, 0x40, 0x0,
    0x0, 0x0, 0xcf, 0x40, 0x0, 0x0, 0x0, 0xcf,
    0x40, 0x0, 0x0, 0x0, 0xcf, 0x40, 0x0, 0x0,
    0x0, 0xcf, 0x40, 0x0, 0x0, 0x0, 0xcf, 0x40,
    0x0, 0x0, 0x0, 0xcf, 0x40, 0x0, 0x0, 0x0,
    0xcf, 0x40, 0x0, 0x0, 0x0, 0xcf, 0x40, 0x0,
    0x0, 0x0, 0xcf, 0x40, 0x0, 0x0, 0x0, 0xcf,
    0x40, 0x0, 0x0, 0x0, 0xcf, 0x40, 0x0, 0x0,
    0x0, 0xcf, 0x40, 0x0, 0x0, 0x0, 0xcf, 0xcb,
    0xbb, 0xbb, 0xb3, 0xcf, 0xff, 0xff, 0xff, 0xf5,

    /* U+004D "M" */
    0xcf, 0xd0, 0x0, 0x0, 0x0, 0xf, 0xfa, 0xcf,
    0xf3, 0x0, 0x0, 0x0, 0x5f, 0xfa, 0xce, 0xf8,
    0x0, 0x0, 0x0, 0xbe, 0xfa, 0xcd, 0xbe, 0x0,
    0x0, 0x1, 0xf9, 0xfa, 0xce, 0x6f, 0x40, 0x0,
    0x6, 0xf5, 0xfa, 0xcf, 0x1f, 0xa0, 0x0, 0xc,
    0xd3, 0xfa, 0xcf, 0xa, 0xf0, 0x0, 0x2f, 0x73,
    0xfa, 0xcf, 0x4, 0xf5, 0x0, 0x7f, 0x14, 0xfa,
    0xcf, 0x0, 0xeb, 0x0, 0xdb, 0x4, 0xfa, 0xcf,
    0x0, 0x9f, 0x12, 0xf6, 0x4, 0xfa, 0xcf, 0x0,
    0x3f, 0x68, 0xf0, 0x4, 0xfa, 0xcf, 0x0, 0xd,
    0xcd, 0xa0, 0x4, 0xfa, 0xcf, 0x0, 0x7, 0xff,
    0x40, 0x4, 0xfa, 0xcf, 0x0, 0x1, 0xfe, 0x0,
    0x4, 0xfa, 0xcf, 0x0, 0x0, 0x54, 0x0, 0x4,
    0xfa, 0xcf, 0x0, 0x0, 0x0, 0x0, 0x4, 0xfa,

    /* U+004E "N" */
    0xcf, 0x90, 0x0, 0x0, 0x4, 0xfb, 0xcf, 0xf2,
    0x0, 0x0, 0x4, 0xfb, 0xcf, 0xfb, 0x0, 0x0,
    0x4, 0xfb, 0xcf, 0x8f, 0x50, 0x0, 0x4, 0xfb,
    0xcf, 0x1f, 0xe0, 0x0, 0x4, 0xfb, 0xcf, 0x7,
    0xf8, 0x0, 0x4, 0xfb, 0xcf, 0x10, 0xef, 0x10,
    0x4, 0xfb, 0xcf, 0x20, 0x5f, 0xa0, 0x4, 0xfb,
    0xcf, 0x20, 0xc, 0xf4, 0x3, 0xfb, 0xcf, 0x20,
    0x3, 0xfd, 0x3, 0xfb, 0xcf, 0x20, 0x0, 0x9f,
    0x62, 0xfb, 0xcf, 0x20, 0x0, 0x1f, 0xe2, 0xfb,
    0xcf, 0x20, 0x0, 0x7, 0xf7, 0xfb, 0xcf, 0x20,
    0x0, 0x0, 0xde, 0xfb, 0xcf, 0x20, 0x0, 0x0,
    0x4f, 0xfb, 0xcf, 0x20, 0x0, 0x0, 0xb, 0xfb,

    /* U+004F "O" */
    0x0, 0x0, 0x6c, 0xef, 0xd9, 0x20, 0x0, 0x0,
    0x2, 0xef, 0xfd, 0xcf, 0xff, 0x60, 0x0, 0x1,
    0xff, 0xa1, 0x0, 0x6, 0xff, 0x50, 0x0, 0xbf,
    0xa0, 0x0, 0x0, 0x5, 0xfe, 0x10, 0x1f, 0xf1,
    0x0, 0x0, 0x0, 0xb, 0xf6, 0x6, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xc0, 0x9f, 0x70, 0x0,
    0x0, 0x0, 0x2, 0xfe, 0xa, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xf0, 0xaf, 0x60, 0x0, 0x0,
    0x0, 0x1, 0xff, 0x9, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x2f, 0xe0, 0x6f, 0xb0, 0x0, 0x0, 0x0,
    0x6, 0xfb, 0x1, 0xff, 0x10, 0x0, 0x0, 0x0,
    0xcf, 0x60, 0xa, 0xfb, 0x0, 0x0, 0x0, 0x5f,
    0xe1, 0x0, 0x1e, 0xfa, 0x10, 0x0, 0x7f, 0xf5,
    0x0, 0x0, 0x2d, 0xff, 0xdc, 0xff, 0xf5, 0x0,
    0x0, 0x0, 0x6, 0xce, 0xfd, 0x92, 0x0, 0x0,

    /* U+0050 "P" */
    0xcf, 0xff, 0xff, 0xda, 0x40, 0xc, 0xfb, 0x99,
    0xac, 0xff, 0xa0, 0xcf, 0x40, 0x0, 0x2, 0xdf,
    0x5c, 0xf4, 0x0, 0x0, 0x5, 0xfb, 0xcf, 0x40,
    0x0, 0x0, 0x3f, 0xcc, 0xf4, 0x0, 0x0, 0x4,
    0xfb, 0xcf, 0x40, 0x0, 0x0, 0xaf, 0x8c, 0xf4,
    0x0, 0x3, 0xaf, 0xe1, 0xcf, 0xff, 0xff, 0xff,
    0xd2, 0xc, 0xfb, 0x99, 0x97, 0x30, 0x0, 0xcf,
    0x40, 0x0, 0x0, 0x0, 0xc, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0x40, 0x0, 0x0, 0x0, 0xc,
    0xf4, 0x0, 0x0, 0x0, 0x0, 0xcf, 0x40, 0x0,
    0x0, 0x0, 0xc, 0xf4, 0x0, 0x0, 0x0, 0x0,

    /* U+0051 "Q" */
    0x0, 0x0, 0x6c, 0xef, 0xd8, 0x20, 0x0, 0x0,
    0x2, 0xdf, 0xfd, 0xcf, 0xff, 0x50, 0x0, 0x1,
    0xef, 0xa1, 0x0, 0x7, 0xff, 0x50, 0x0, 0xaf,
    0xb0, 0x0, 0x0, 0x6, 0xfe, 0x10, 0x1f, 0xf1,
    0x0, 0x0, 0x0, 0xc, 0xf6, 0x6, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xb0, 0x9f, 0x70, 0x0,
    0x0, 0x0, 0x2, 0xfe, 0xa, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xf0, 0xaf, 0x60, 0x0, 0x0,
    0x0, 0x1, 0xff, 0x9, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x2f, 0xe0, 0x7f, 0xa0, 0x0, 0x0, 0x0,
    0x5, 0xfc, 0x2, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0x70, 0xc, 0xf7, 0x0, 0x0, 0x0, 0x2f,
    0xf2, 0x0, 0x3f, 0xf4, 0x0, 0x0, 0x1d, 0xf8,
    0x0, 0x0, 0x5f, 0xfb, 0x66, 0x9f, 0xfa, 0x0,
    0x0, 0x0, 0x3c, 0xff, 0xff, 0xe6, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xbf, 0xa0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xef, 0xfc, 0xbc, 0x10, 0x0,
    0x0, 0x0, 0x1, 0x8d, 0xff, 0xd3,

    /* U+0052 "R" */
    0xcf, 0xff, 0xff, 0xeb, 0x50, 0x0, 0xcf, 0xb9,
    0x9a, 0xcf, 0xfb, 0x0, 0xcf, 0x40, 0x0, 0x2,
    0xdf, 0x60, 0xcf, 0x40, 0x0, 0x0, 0x4f, 0xc0,
    0xcf, 0x40, 0x0, 0x0, 0x2f, 0xd0, 0xcf, 0x40,
    0x0, 0x0, 0x3f, 0xc0, 0xcf, 0x40, 0x0, 0x0,
    0x9f, 0x80, 0xcf, 0x40, 0x1, 0x3a, 0xff, 0x10,
    0xcf, 0xff, 0xff, 0xff, 0xd2, 0x0, 0xcf, 0xb9,
    0x9e, 0xf8, 0x0, 0x0, 0xcf, 0x40, 0x4, 0xfe,
    0x0, 0x0, 0xcf, 0x40, 0x0, 0xaf, 0x90, 0x0,
    0xcf, 0x40, 0x0, 0x1f, 0xf3, 0x0, 0xcf, 0x40,
    0x0, 0x7, 0xfc, 0x0, 0xcf, 0x40, 0x0, 0x0,
    0xdf, 0x60, 0xcf, 0x40, 0x0, 0x0, 0x3f, 0xf1,

    /* U+0053 "S" */
    0x0, 0x18, 0xdf, 0xfc, 0x70, 0x0, 0x2, 0xef,
    0xfc, 0xcf, 0xfe, 0x30, 0xc, 0xf9, 0x0, 0x0,
    0x7e, 0x20, 0x1f, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xe7, 0x10,
    0x0, 0x0, 0x0, 0x2b, 0xff, 0xf9, 0x20, 0x0,
    0x0, 0x0, 0x3a, 0xff, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x18, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf2,
    0x6, 0x0, 0x0, 0x0, 0x1f, 0xf0, 0xaf, 0xb3,
    0x0, 0x1, 0xbf, 0xa0, 0x2c, 0xff, 0xec, 0xdf,
    0xfc, 0x10, 0x0, 0x5a, 0xef, 0xec, 0x60, 0x0,

    /* U+0054 "T" */
    0x5f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x83, 0xbb,
    0xbb, 0xdf, 0xeb, 0xbb, 0xb5, 0x0, 0x0, 0x7,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0x90, 0x0, 0x0, 0x0, 0x0, 0x7, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f,
    0x90, 0x0, 0x0, 0x0, 0x0, 0x7, 0xf9, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0x90, 0x0, 0x0,

    /* U+0055 "U" */
    0xdf, 0x20, 0x0, 0x0, 0x3, 0xfb, 0xdf, 0x20,
    0x0, 0x0, 0x3, 0xfb, 0xdf, 0x20, 0x0, 0x0,
    0x3, 0xfb, 0xdf, 0x20, 0x0, 0x0, 0x3, 0xfb,
    0xdf, 0x20, 0x0, 0x0, 0x3, 0xfb, 0xdf, 0x20,
    0x0, 0x0, 0x3, 0xfb, 0xdf, 0x20, 0x0, 0x0,
    0x3, 0xfb, 0xdf, 0x20, 0x0, 0x0, 0x3, 0xfb,
    0xdf, 0x20, 0x0, 0x0, 0x3, 0xfb, 0xdf, 0x30,
    0x0, 0x0, 0x3, 0xfb, 0xcf, 0x40, 0x0, 0x0,
    0x5, 0xfa, 0xaf, 0x70, 0x0, 0x0, 0x8, 0xf8,
    0x6f, 0xd0, 0x0, 0x0, 0xd, 0xf4, 0xd, 0xfa,
    0x0, 0x1, 0xbf, 0xc0, 0x3, 0xef, 0xfc, 0xcf,
    0xfd, 0x20, 0x0, 0x18, 0xdf, 0xfc, 0x70, 0x0,

    /* U+0056 "V" */
    0xdf, 0x40, 0x0, 0x0, 0x0, 0x9f, 0x78, 0xf9,
    0x0, 0x0, 0x0, 0xd, 0xf2, 0x2f, 0xe0, 0x0,
    0x0, 0x2, 0xfd, 0x0, 0xdf, 0x30, 0x0, 0x0,
    0x7f, 0x80, 0x8, 0xf8, 0x0, 0x0, 0xc, 0xf3,
    0x0, 0x3f, 0xc0, 0x0, 0x1, 0xfe, 0x0, 0x0,
    0xef, 0x10, 0x0, 0x5f, 0x90, 0x0, 0x9, 0xf6,
    0x0, 0xa, 0xf3, 0x0, 0x0, 0x3f, 0xb0, 0x0,
    0xfe, 0x0, 0x0, 0x0, 0xef, 0x0, 0x4f, 0x90,
    0x0, 0x0, 0x9, 0xf4, 0x8, 0xf4, 0x0, 0x0,
    0x0, 0x4f, 0x90, 0xde, 0x0, 0x0, 0x0, 0x0,
    0xee, 0x2f, 0xa0, 0x0, 0x0, 0x0, 0xa, 0xfa,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xa0, 0x0, 0x0,

    /* U+0057 "W" */
    0x5f, 0xc0, 0x0, 0x0, 0x5f, 0xc0, 0x0, 0x0,
    0x4f, 0x91, 0xff, 0x0, 0x0, 0x9, 0xff, 0x0,
    0x0, 0x8, 0xf6, 0xe, 0xf2, 0x0, 0x0, 0xdf,
    0xf4, 0x0, 0x0, 0xbf, 0x30, 0xbf, 0x50, 0x0,
    0x1f, 0x9f, 0x80, 0x0, 0xe, 0xf0, 0x7, 0xf8,
    0x0, 0x6, 0xf3, 0xec, 0x0, 0x1, 0xfc, 0x0,
    0x4f, 0xb0, 0x0, 0xaf, 0xb, 0xf1, 0x0, 0x4f,
    0x90, 0x0, 0xfe, 0x0, 0xe, 0xc0, 0x7f, 0x50,
    0x8, 0xf5, 0x0, 0xd, 0xf1, 0x2, 0xf8, 0x3,
    0xf9, 0x0, 0xbf, 0x20, 0x0, 0x9f, 0x50, 0x6f,
    0x40, 0xf, 0xd0, 0xe, 0xf0, 0x0, 0x6, 0xf8,
    0xa, 0xf0, 0x0, 0xbf, 0x11, 0xfb, 0x0, 0x0,
    0x3f, 0xb0, 0xdc, 0x0, 0x7, 0xf4, 0x4f, 0x80,
    0x0, 0x0, 0xfe, 0x1f, 0x80, 0x0, 0x3f, 0x76,
    0xf5, 0x0, 0x0, 0xc, 0xf5, 0xf4, 0x0, 0x0,
    0xfb, 0x9f, 0x20, 0x0, 0x0, 0x8f, 0xcf, 0x0,
    0x0, 0xb, 0xfc, 0xe0, 0x0, 0x0, 0x5, 0xff,
    0xc0, 0x0, 0x0, 0x7f, 0xfb, 0x0, 0x0, 0x0,
    0x1f, 0xf8, 0x0, 0x0, 0x3, 0xff, 0x80, 0x0,

    /* U+0058 "X" */
    0x1f, 0xf3, 0x0, 0x0, 0x7, 0xfa, 0x0, 0x7f,
    0xb0, 0x0, 0x1, 0xff, 0x10, 0x0, 0xdf, 0x40,
    0x0, 0x8f, 0x80, 0x0, 0x5, 0xfc, 0x0, 0x1f,
    0xe0, 0x0, 0x0, 0xc, 0xf5, 0x9, 0xf6, 0x0,
    0x0, 0x0, 0x3f, 0xd1, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0xdf, 0x40, 0x0, 0x0, 0x0, 0x1,
    0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0xe, 0xe9, 0xf7, 0x0,
    0x0, 0x0, 0x7, 0xf7, 0x1f, 0xf1, 0x0, 0x0,
    0x1, 0xfe, 0x0, 0x7f, 0xa0, 0x0, 0x0, 0xaf,
    0x60, 0x0, 0xef, 0x30, 0x0, 0x3f, 0xd0, 0x0,
    0x6, 0xfc, 0x0, 0xc, 0xf5, 0x0, 0x0, 0xc,
    0xf6, 0x5, 0xfc, 0x0, 0x0, 0x0, 0x4f, 0xe0,

    /* U+0059 "Y" */
    0xc, 0xf5, 0x0, 0x0, 0x0, 0xaf, 0x70, 0x4f,
    0xd0, 0x0, 0x0, 0x1f, 0xe0, 0x0, 0xcf, 0x40,
    0x0, 0x8, 0xf7, 0x0, 0x4, 0xfb, 0x0, 0x1,
    0xfe, 0x0, 0x0, 0xc, 0xf3, 0x0, 0x7f, 0x70,
    0x0, 0x0, 0x4f, 0xa0, 0xe, 0xe0, 0x0, 0x0,
    0x0, 0xcf, 0x15, 0xf7, 0x0, 0x0, 0x0, 0x4,
    0xf9, 0xde, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff,
    0x70, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xd0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xd0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0xd0, 0x0, 0x0,

    /* U+005A "Z" */
    0x2f, 0xff, 0xff, 0xff, 0xff, 0xf2, 0x1b, 0xbb,
    0xbb, 0xbb, 0xdf, 0xd0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0x40, 0x0, 0x0, 0x0, 0x9, 0xf9, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xe1, 0x0, 0x0, 0x0,
    0x0, 0xdf, 0x50, 0x0, 0x0, 0x0, 0x8, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0xe1, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0x50, 0x0, 0x0, 0x0, 0x7,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0x60, 0x0, 0x0, 0x0,
    0x6, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xeb, 0xbb, 0xbb,
    0xbb, 0xb2, 0xef, 0xff, 0xff, 0xff, 0xff, 0xf3,

    /* U+005B "[" */
    0xaf, 0xff, 0xba, 0xd2, 0x21, 0xad, 0x0, 0xa,
    0xd0, 0x0, 0xad, 0x0, 0xa, 0xd0, 0x0, 0xad,
    0x0, 0xa, 0xd0, 0x0, 0xad, 0x0, 0xa, 0xd0,
    0x0, 0xad, 0x0, 0xa, 0xd0, 0x0, 0xad, 0x0,
    0xa, 0xd0, 0x0, 0xad, 0x0, 0xa, 0xd0, 0x0,
    0xad, 0x0, 0xa, 0xd0, 0x0, 0xad, 0x0, 0xa,
    0xd0, 0x0, 0xaf, 0xff, 0xa1, 0x22, 0x21,

    /* U+005C "\\" */
    0x8e, 0x0, 0x0, 0x0, 0x3, 0xf4, 0x0, 0x0,
    0x0, 0xe, 0x90, 0x0, 0x0, 0x0, 0x9d, 0x0,
    0x0, 0x0, 0x4, 0xf3, 0x0, 0x0, 0x0, 0xe,
    0x80, 0x0, 0x0, 0x0, 0xad, 0x0, 0x0, 0x0,
    0x5, 0xf2, 0x0, 0x0, 0x0, 0xf, 0x70, 0x0,
    0x0, 0x0, 0xbc, 0x0, 0x0, 0x0, 0x6, 0xf1,
    0x0, 0x0, 0x0, 0x1f, 0x60, 0x0, 0x0, 0x0,
    0xcb, 0x0, 0x0, 0x0, 0x7, 0xf0, 0x0, 0x0,
    0x0, 0x2f, 0x50, 0x0, 0x0, 0x0, 0xda, 0x0,
    0x0, 0x0, 0x8, 0xe0, 0x0, 0x0, 0x0, 0x3f,
    0x40, 0x0, 0x0, 0x0, 0xe9, 0x0, 0x0, 0x0,
    0x9, 0xe0, 0x0, 0x0, 0x0, 0x4f, 0x30,

    /* U+005D "]" */
    0x4f, 0xff, 0xf2, 0x2, 0x27, 0xf2, 0x0, 0x6,
    0xf2, 0x0, 0x6, 0xf2, 0x0, 0x6, 0xf2, 0x0,
    0x6, 0xf2, 0x0, 0x6, 0xf2, 0x0, 0x6, 0xf2,
    0x0, 0x6, 0xf2, 0x0, 0x6, 0xf2, 0x0, 0x6,
    0xf2, 0x0, 0x6, 0xf2, 0x0, 0x6, 0xf2, 0x0,
    0x6, 0xf2, 0x0, 0x6, 0xf2, 0x0, 0x6, 0xf2,
    0x0, 0x6, 0xf2, 0x0, 0x6, 0xf2, 0x0, 0x6,
    0xf2, 0x0, 0x6, 0xf2, 0x3f, 0xff, 0xf2, 0x2,
    0x22, 0x20,

    /* U+005E "^" */
    0x0, 0x0, 0x78, 0x0, 0x0, 0x0, 0x2, 0xff,
    0x60, 0x0, 0x0, 0x9, 0xfd, 0xc0, 0x0, 0x0,
    0xe, 0xa6, 0xf2, 0x0, 0x0, 0x5f, 0x41, 0xf9,
    0x0, 0x0, 0xbe, 0x0, 0xbe, 0x0, 0x2, 0xf8,
    0x0, 0x5f, 0x50, 0x8, 0xf2, 0x0, 0xe, 0xc0,
    0xe, 0xc0, 0x0, 0x9, 0xf2, 0x4f, 0x60, 0x0,
    0x3, 0xf8,

    /* U+005F "_" */
    0x1, 0x11, 0x11, 0x11, 0x11, 0x10, 0xbf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x23, 0x33, 0x33, 0x33,
    0x33, 0x33,

    /* U+0060 "`" */
    0x8, 0x0, 0x0, 0x9f, 0xb0, 0x0, 0xb, 0xf9,
    0x0, 0x0, 0xbf, 0x70, 0x0, 0xb, 0xb0, 0x0,
    0x0, 0x0,

    /* U+0061 "a" */
    0x0, 0x5b, 0xef, 0xe9, 0x0, 0xd, 0xfe, 0xba,
    0xef, 0xb0, 0x8, 0x60, 0x0, 0x1e, 0xf3, 0x0,
    0x0, 0x0, 0x8, 0xf7, 0x0, 0x0, 0x2, 0x5a,
    0xf9, 0x0, 0x4b, 0xff, 0xee, 0xfa, 0xa, 0xfc,
    0x61, 0x6, 0xfa, 0x6f, 0xa0, 0x0, 0x6, 0xfa,
    0xaf, 0x40, 0x0, 0x6, 0xfa, 0x9f, 0x70, 0x0,
    0x4e, 0xfa, 0x3f, 0xfb, 0x9c, 0xf9, 0xfa, 0x4,
    0xcf, 0xea, 0x21, 0xfa,

    /* U+0062 "b" */
    0xff, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xf0, 0x0, 0x0, 0x0, 0x0, 0xff, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xf0, 0x5c, 0xfe, 0xa2,
    0x0, 0xff, 0xbf, 0xcb, 0xef, 0xf2, 0xf, 0xfc,
    0x30, 0x0, 0xaf, 0xc0, 0xff, 0x10, 0x0, 0x1,
    0xff, 0x2f, 0xf0, 0x0, 0x0, 0xc, 0xf5, 0xff,
    0x0, 0x0, 0x0, 0xaf, 0x7f, 0xf0, 0x0, 0x0,
    0xb, 0xf6, 0xff, 0x0, 0x0, 0x0, 0xdf, 0x4f,
    0xf0, 0x0, 0x0, 0x4f, 0xf0, 0xff, 0x80, 0x0,
    0x2d, 0xf8, 0xf, 0xee, 0xeb, 0xbf, 0xfb, 0x0,
    0xfa, 0x18, 0xef, 0xc6, 0x0, 0x0,

    /* U+0063 "c" */
    0x0, 0x7, 0xcf, 0xfb, 0x30, 0x2, 0xdf, 0xfb,
    0xbf, 0xf1, 0xc, 0xfa, 0x0, 0x2, 0x50, 0x5f,
    0xd0, 0x0, 0x0, 0x0, 0xaf, 0x70, 0x0, 0x0,
    0x0, 0xcf, 0x40, 0x0, 0x0, 0x0, 0xcf, 0x40,
    0x0, 0x0, 0x0, 0xbf, 0x60, 0x0, 0x0, 0x0,
    0x6f, 0xc0, 0x0, 0x0, 0x0, 0xe, 0xf9, 0x0,
    0x1, 0x80, 0x3, 0xef, 0xeb, 0xbf, 0xf5, 0x0,
    0x18, 0xdf, 0xeb, 0x40,

    /* U+0064 "d" */
    0x0, 0x0, 0x0, 0x0, 0x6f, 0x90, 0x0, 0x0,
    0x0, 0x6, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0x90, 0x0, 0x0, 0x0, 0x6, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0x90, 0x1, 0x9e, 0xfd, 0x65,
    0xf9, 0x2, 0xef, 0xeb, 0xcf, 0xef, 0x90, 0xdf,
    0xa0, 0x0, 0x2c, 0xf9, 0x5f, 0xd0, 0x0, 0x0,
    0x6f, 0x9a, 0xf7, 0x0, 0x0, 0x6, 0xf9, 0xcf,
    0x40, 0x0, 0x0, 0x6f, 0x9c, 0xf4, 0x0, 0x0,
    0x6, 0xf9, 0xbf, 0x60, 0x0, 0x0, 0x6f, 0x97,
    0xfc, 0x0, 0x0, 0x6, 0xf9, 0x1f, 0xf6, 0x0,
    0x4, 0xef, 0x90, 0x6f, 0xfd, 0xad, 0xfa, 0xf9,
    0x0, 0x4b, 0xff, 0xb3, 0x1f, 0x90,

    /* U+0065 "e" */
    0x0, 0x18, 0xdf, 0xe9, 0x10, 0x0, 0x2d, 0xfc,
    0x9c, 0xfe, 0x20, 0xd, 0xf5, 0x0, 0x5, 0xfb,
    0x5, 0xf8, 0x0, 0x0, 0xb, 0xf0, 0xaf, 0x41,
    0x11, 0x11, 0x9f, 0x3c, 0xff, 0xff, 0xff, 0xff,
    0xf3, 0xcf, 0x64, 0x44, 0x44, 0x44, 0xb, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0xdf, 0x80, 0x0, 0x3, 0x10, 0x2,
    0xef, 0xea, 0xad, 0xf9, 0x0, 0x1, 0x8d, 0xff,
    0xc6, 0x0,

    /* U+0066 "f" */
    0x0, 0x0, 0x58, 0x84, 0x0, 0xc, 0xff, 0xf9,
    0x0, 0x5f, 0xd2, 0x1, 0x0, 0x8f, 0x70, 0x0,
    0x0, 0xaf, 0x50, 0x0, 0x0, 0xaf, 0x50, 0x0,
    0x4f, 0xff, 0xff, 0xe0, 0x28, 0xdf, 0xb8, 0x70,
    0x0, 0xaf, 0x50, 0x0, 0x0, 0xaf, 0x50, 0x0,
    0x0, 0xaf, 0x50, 0x0, 0x0, 0xaf, 0x50, 0x0,
    0x0, 0xaf, 0x50, 0x0, 0x0, 0xaf, 0x50, 0x0,
    0x0, 0xaf, 0x50, 0x0, 0x0, 0xaf, 0x50, 0x0,
    0x0, 0xaf, 0x50, 0x0, 0x0, 0xaf, 0x50, 0x0,

    /* U+0067 "g" */
    0x0, 0x4b, 0xff, 0xff, 0xff, 0xe0, 0x6, 0xfe,
    0x88, 0xef, 0xc8, 0x70, 0x1f, 0xe1, 0x0, 0x1e,
    0xf1, 0x0, 0x5f, 0x90, 0x0, 0x8, 0xf5, 0x0,
    0x6f, 0x80, 0x0, 0x7, 0xf5, 0x0, 0x2f, 0xd0,
    0x0, 0xc, 0xf2, 0x0, 0x9, 0xfa, 0x22, 0x9f,
    0xa0, 0x0, 0x2, 0xff, 0xff, 0xf9, 0x0, 0x0,
    0xd, 0xb0, 0x33, 0x0, 0x0, 0x0, 0x2f, 0x90,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xfc, 0x99, 0x99,
    0x73, 0x0, 0x6, 0xfe, 0xff, 0xff, 0xff, 0x80,
    0x4f, 0x60, 0x0, 0x1, 0x6f, 0xf0, 0xce, 0x0,
    0x0, 0x0, 0xe, 0xf0, 0xcf, 0x30, 0x0, 0x0,
    0x6f, 0xa0, 0x5f, 0xf9, 0x66, 0x7c, 0xfb, 0x0,
    0x3, 0x9d, 0xff, 0xea, 0x50, 0x0,

    /* U+0068 "h" */
    0xff, 0x0, 0x0, 0x0, 0x0, 0xff, 0x0, 0x0,
    0x0, 0x0, 0xff, 0x0, 0x0, 0x0, 0x0, 0xff,
    0x0, 0x0, 0x0, 0x0, 0xff, 0x0, 0x0, 0x0,
    0x0, 0xff, 0x3, 0xbf, 0xfa, 0x10, 0xff, 0x7f,
    0xdc, 0xff, 0xd0, 0xff, 0xe4, 0x0, 0x1e, 0xf4,
    0xff, 0x20, 0x0, 0x9, 0xf7, 0xff, 0x0, 0x0,
    0x7, 0xf8, 0xff, 0x0, 0x0, 0x6, 0xf9, 0xff,
    0x0, 0x0, 0x6, 0xf9, 0xff, 0x0, 0x0, 0x6,
    0xf9, 0xff, 0x0, 0x0, 0x6, 0xf9, 0xff, 0x0,
    0x0, 0x6, 0xf9, 0xff, 0x0, 0x0, 0x6, 0xf9,
    0xff, 0x0, 0x0, 0x6, 0xf9,

    /* U+0069 "i" */
    0x1d, 0xe2, 0x3f, 0xf5, 0x7, 0x70, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xf0, 0xf, 0xf0, 0xf, 0xf0,
    0xf, 0xf0, 0xf, 0xf0, 0xf, 0xf0, 0xf, 0xf0,
    0xf, 0xf0, 0xf, 0xf0, 0xf, 0xf0, 0xf, 0xf0,
    0xf, 0xf0,

    /* U+006A "j" */
    0x0, 0x1d, 0xe2, 0x0, 0x3f, 0xf5, 0x0, 0x7,
    0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xf0, 0x0, 0xf, 0xf0, 0x0, 0xf, 0xf0,
    0x0, 0xf, 0xf0, 0x0, 0xf, 0xf0, 0x0, 0xf,
    0xf0, 0x0, 0xf, 0xf0, 0x0, 0xf, 0xf0, 0x0,
    0xf, 0xf0, 0x0, 0xf, 0xf0, 0x0, 0xf, 0xf0,
    0x0, 0xf, 0xf0, 0x0, 0xf, 0xf0, 0x0, 0xf,
    0xf0, 0x0, 0x2f, 0xd0, 0x79, 0xdf, 0x80, 0xbf,
    0xe9, 0x0,

    /* U+006B "k" */
    0xff, 0x0, 0x0, 0x0, 0x0, 0xff, 0x0, 0x0,
    0x0, 0x0, 0xff, 0x0, 0x0, 0x0, 0x0, 0xff,
    0x0, 0x0, 0x0, 0x0, 0xff, 0x0, 0x0, 0x0,
    0x0, 0xff, 0x0, 0x0, 0x4f, 0xe1, 0xff, 0x0,
    0x1, 0xef, 0x30, 0xff, 0x0, 0xc, 0xf5, 0x0,
    0xff, 0x0, 0xaf, 0x80, 0x0, 0xff, 0x7, 0xfb,
    0x0, 0x0, 0xff, 0x4f, 0xfc, 0x0, 0x0, 0xff,
    0xee, 0xaf, 0x70, 0x0, 0xff, 0xf3, 0xe, 0xf2,
    0x0, 0xff, 0x40, 0x5, 0xfb, 0x0, 0xff, 0x0,
    0x0, 0xcf, 0x50, 0xff, 0x0, 0x0, 0x2f, 0xe1,
    0xff, 0x0, 0x0, 0x8, 0xf9,

    /* U+006C "l" */
    0xff, 0x0, 0xff, 0x0, 0xff, 0x0, 0xff, 0x0,
    0xff, 0x0, 0xff, 0x0, 0xff, 0x0, 0xff, 0x0,
    0xff, 0x0, 0xff, 0x0, 0xff, 0x0, 0xff, 0x0,
    0xff, 0x0, 0xff, 0x0, 0xff, 0x0, 0xdf, 0xb0,
    0x4e, 0xf3,

    /* U+006D "m" */
    0xfb, 0x4, 0xcf, 0xe8, 0x0, 0x2a, 0xff, 0xa1,
    0xf, 0xd8, 0xfd, 0xcf, 0xf8, 0x5f, 0xec, 0xff,
    0xc0, 0xff, 0xe4, 0x0, 0x5f, 0xff, 0x60, 0x2,
    0xff, 0x3f, 0xf2, 0x0, 0x0, 0xef, 0x60, 0x0,
    0xa, 0xf6, 0xff, 0x0, 0x0, 0xc, 0xf4, 0x0,
    0x0, 0x8f, 0x7f, 0xf0, 0x0, 0x0, 0xbf, 0x40,
    0x0, 0x8, 0xf8, 0xff, 0x0, 0x0, 0xb, 0xf4,
    0x0, 0x0, 0x8f, 0x8f, 0xf0, 0x0, 0x0, 0xbf,
    0x40, 0x0, 0x8, 0xf8, 0xff, 0x0, 0x0, 0xb,
    0xf4, 0x0, 0x0, 0x8f, 0x8f, 0xf0, 0x0, 0x0,
    0xbf, 0x40, 0x0, 0x8, 0xf8, 0xff, 0x0, 0x0,
    0xb, 0xf4, 0x0, 0x0, 0x8f, 0x8f, 0xf0, 0x0,
    0x0, 0xbf, 0x40, 0x0, 0x8, 0xf8,

    /* U+006E "n" */
    0xfb, 0x4, 0xbf, 0xfa, 0x10, 0xfd, 0x8f, 0xdc,
    0xff, 0xd0, 0xff, 0xe4, 0x0, 0x1e, 0xf4, 0xff,
    0x20, 0x0, 0x9, 0xf7, 0xff, 0x0, 0x0, 0x7,
    0xf8, 0xff, 0x0, 0x0, 0x6, 0xf9, 0xff, 0x0,
    0x0, 0x6, 0xf9, 0xff, 0x0, 0x0, 0x6, 0xf9,
    0xff, 0x0, 0x0, 0x6, 0xf9, 0xff, 0x0, 0x0,
    0x6, 0xf9, 0xff, 0x0, 0x0, 0x6, 0xf9, 0xff,
    0x0, 0x0, 0x6, 0xf9,

    /* U+006F "o" */
    0x0, 0x18, 0xdf, 0xea, 0x20, 0x0, 0x2, 0xef,
    0xeb, 0xcf, 0xf6, 0x0, 0xd, 0xf8, 0x0, 0x4,
    0xff, 0x30, 0x6f, 0xd0, 0x0, 0x0, 0x7f, 0xb0,
    0xaf, 0x60, 0x0, 0x0, 0x1f, 0xf0, 0xcf, 0x40,
    0x0, 0x0, 0xe, 0xf2, 0xcf, 0x40, 0x0, 0x0,
    0xe, 0xf2, 0xbf, 0x60, 0x0, 0x0, 0x1f, 0xf0,
    0x6f, 0xc0, 0x0, 0x0, 0x7f, 0xb0, 0xd, 0xf8,
    0x0, 0x3, 0xff, 0x30, 0x2, 0xef, 0xda, 0xcf,
    0xf6, 0x0, 0x0, 0x18, 0xdf, 0xea, 0x30, 0x0,

    /* U+0070 "p" */
    0xfb, 0x5, 0xcf, 0xea, 0x20, 0xf, 0xeb, 0xfc,
    0xbe, 0xff, 0x20, 0xff, 0xc3, 0x0, 0xa, 0xfc,
    0xf, 0xf1, 0x0, 0x0, 0x1f, 0xf2, 0xff, 0x0,
    0x0, 0x0, 0xcf, 0x5f, 0xf0, 0x0, 0x0, 0xa,
    0xf7, 0xff, 0x0, 0x0, 0x0, 0xbf, 0x6f, 0xf0,
    0x0, 0x0, 0xd, 0xf4, 0xff, 0x0, 0x0, 0x4,
    0xff, 0xf, 0xf8, 0x0, 0x2, 0xdf, 0x80, 0xff,
    0xee, 0xbb, 0xff, 0xb0, 0xf, 0xf1, 0x9e, 0xfc,
    0x60, 0x0, 0xff, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0xff, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0071 "q" */
    0x0, 0x19, 0xef, 0xd7, 0x1f, 0x90, 0x2e, 0xfe,
    0xbc, 0xfe, 0xf9, 0xd, 0xfa, 0x0, 0x2, 0xcf,
    0x95, 0xfd, 0x0, 0x0, 0x6, 0xf9, 0xaf, 0x70,
    0x0, 0x0, 0x6f, 0x9c, 0xf4, 0x0, 0x0, 0x6,
    0xf9, 0xcf, 0x40, 0x0, 0x0, 0x6f, 0x9b, 0xf6,
    0x0, 0x0, 0x6, 0xf9, 0x7f, 0xc0, 0x0, 0x0,
    0x6f, 0x91, 0xff, 0x60, 0x0, 0x4e, 0xf9, 0x6,
    0xff, 0xda, 0xdf, 0xbf, 0x90, 0x4, 0xbf, 0xfb,
    0x35, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x6f, 0x90,
    0x0, 0x0, 0x0, 0x6, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0x90, 0x0, 0x0, 0x0, 0x6, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0x90,

    /* U+0072 "r" */
    0xfb, 0x5, 0xdf, 0x7f, 0xc6, 0xfe, 0xd4, 0xff,
    0xf6, 0x0, 0xf, 0xf9, 0x0, 0x0, 0xff, 0x10,
    0x0, 0xf, 0xf0, 0x0, 0x0, 0xff, 0x0, 0x0,
    0xf, 0xf0, 0x0, 0x0, 0xff, 0x0, 0x0, 0xf,
    0xf0, 0x0, 0x0, 0xff, 0x0, 0x0, 0xf, 0xf0,
    0x0, 0x0,

    /* U+0073 "s" */
    0x0, 0x19, 0xdf, 0xe9, 0x20, 0x1, 0xef, 0xb8,
    0xbf, 0xe0, 0x8, 0xf8, 0x0, 0x2, 0x30, 0x9,
    0xf5, 0x0, 0x0, 0x0, 0x4, 0xfe, 0x60, 0x0,
    0x0, 0x0, 0x6e, 0xff, 0x92, 0x0, 0x0, 0x0,
    0x6c, 0xff, 0x70, 0x0, 0x0, 0x0, 0x4e, 0xf3,
    0x0, 0x0, 0x0, 0x7, 0xf7, 0x8, 0x60, 0x0,
    0xb, 0xf5, 0x1e, 0xfd, 0x99, 0xcf, 0xc0, 0x0,
    0x7c, 0xff, 0xd8, 0x0,

    /* U+0074 "t" */
    0x0, 0x28, 0x30, 0x0, 0x0, 0x5f, 0x60, 0x0,
    0x0, 0x6f, 0x60, 0x0, 0x0, 0x7f, 0x60, 0x0,
    0x6f, 0xff, 0xff, 0xfa, 0x38, 0xdf, 0xb8, 0x85,
    0x0, 0xaf, 0x60, 0x0, 0x0, 0xaf, 0x60, 0x0,
    0x0, 0xaf, 0x60, 0x0, 0x0, 0xaf, 0x60, 0x0,
    0x0, 0xaf, 0x60, 0x0, 0x0, 0xaf, 0x60, 0x0,
    0x0, 0x9f, 0x60, 0x0, 0x0, 0x7f, 0xa0, 0x0,
    0x0, 0x1f, 0xfc, 0xa9, 0x0, 0x4, 0xdf, 0xea,

    /* U+0075 "u" */
    0x2f, 0xe0, 0x0, 0x0, 0xaf, 0x52, 0xfe, 0x0,
    0x0, 0xa, 0xf5, 0x2f, 0xe0, 0x0, 0x0, 0xaf,
    0x52, 0xfe, 0x0, 0x0, 0xa, 0xf5, 0x2f, 0xe0,
    0x0, 0x0, 0xaf, 0x52, 0xfe, 0x0, 0x0, 0xa,
    0xf5, 0x2f, 0xe0, 0x0, 0x0, 0xaf, 0x52, 0xfe,
    0x0, 0x0, 0xa, 0xf5, 0x1f, 0xf0, 0x0, 0x0,
    0xdf, 0x50, 0xef, 0x70, 0x1, 0xcf, 0xf5, 0x7,
    0xff, 0xdc, 0xfc, 0x7f, 0x50, 0x7, 0xef, 0xd7,
    0x5, 0xf5,

    /* U+0076 "v" */
    0x8f, 0x80, 0x0, 0x0, 0xf, 0xf0, 0x2f, 0xd0,
    0x0, 0x0, 0x4f, 0xa0, 0xc, 0xf3, 0x0, 0x0,
    0xaf, 0x40, 0x7, 0xf8, 0x0, 0x0, 0xfe, 0x0,
    0x1, 0xfd, 0x0, 0x4, 0xf9, 0x0, 0x0, 0xbf,
    0x20, 0xa, 0xf3, 0x0, 0x0, 0x6f, 0x80, 0xf,
    0xe0, 0x0, 0x0, 0xf, 0xd0, 0x4f, 0x80, 0x0,
    0x0, 0xa, 0xf2, 0x9f, 0x20, 0x0, 0x0, 0x4,
    0xf7, 0xed, 0x0, 0x0, 0x0, 0x0, 0xee, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xf2, 0x0, 0x0,

    /* U+0077 "w" */
    0x4f, 0xc0, 0x0, 0x3, 0xff, 0x0, 0x0, 0xf,
    0xe0, 0xf, 0xf0, 0x0, 0x8, 0xff, 0x40, 0x0,
    0x3f, 0xa0, 0xb, 0xf4, 0x0, 0xc, 0xbf, 0x80,
    0x0, 0x7f, 0x60, 0x7, 0xf8, 0x0, 0xf, 0x6d,
    0xc0, 0x0, 0xbf, 0x10, 0x2, 0xfc, 0x0, 0x5f,
    0x39, 0xf1, 0x0, 0xfd, 0x0, 0x0, 0xef, 0x10,
    0x9f, 0x5, 0xf5, 0x3, 0xf9, 0x0, 0x0, 0x9f,
    0x40, 0xdb, 0x1, 0xf9, 0x8, 0xf5, 0x0, 0x0,
    0x5f, 0x81, 0xf6, 0x0, 0xcd, 0xb, 0xf1, 0x0,
    0x0, 0x1f, 0xb5, 0xf2, 0x0, 0x8f, 0x1e, 0xc0,
    0x0, 0x0, 0xc, 0xf9, 0xe0, 0x0, 0x4f, 0x8f,
    0x80, 0x0, 0x0, 0x7, 0xff, 0xa0, 0x0, 0xf,
    0xff, 0x30, 0x0, 0x0, 0x3, 0xff, 0x60, 0x0,
    0xb, 0xff, 0x0, 0x0,

    /* U+0078 "x" */
    0x1f, 0xf2, 0x0, 0x1, 0xfe, 0x10, 0x6f, 0xb0,
    0x0, 0x9f, 0x60, 0x0, 0xcf, 0x50, 0x2f, 0xc0,
    0x0, 0x2, 0xfe, 0xb, 0xf3, 0x0, 0x0, 0x8,
    0xfb, 0xf9, 0x0, 0x0, 0x0, 0xe, 0xfe, 0x0,
    0x0, 0x0, 0x3, 0xff, 0xf1, 0x0, 0x0, 0x0,
    0xde, 0x6f, 0xb0, 0x0, 0x0, 0x7f, 0x60, 0xbf,
    0x50, 0x0, 0x2f, 0xd0, 0x2, 0xfe, 0x10, 0xb,
    0xf4, 0x0, 0x8, 0xfa, 0x5, 0xfb, 0x0, 0x0,
    0xd, 0xf4,

    /* U+0079 "y" */
    0x8f, 0x80, 0x0, 0x0, 0xe, 0xf0, 0x1f, 0xe0,
    0x0, 0x0, 0x4f, 0xa0, 0xb, 0xf4, 0x0, 0x0,
    0x9f, 0x40, 0x4, 0xfa, 0x0, 0x0, 0xee, 0x0,
    0x0, 0xef, 0x0, 0x4, 0xf9, 0x0, 0x0, 0x7f,
    0x60, 0x9, 0xf4, 0x0, 0x0, 0x1f, 0xc0, 0xe,
    0xe0, 0x0, 0x0, 0xb, 0xf1, 0x3f, 0x80, 0x0,
    0x0, 0x4, 0xf7, 0x7f, 0x30, 0x0, 0x0, 0x0,
    0xed, 0xcd, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xd0, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0x60, 0x0, 0x0, 0x0, 0x2, 0xfe, 0x0,
    0x0, 0x0, 0xa, 0xaf, 0xf4, 0x0, 0x0, 0x0,
    0x1e, 0xfc, 0x40, 0x0, 0x0, 0x0,

    /* U+007A "z" */
    0x8, 0xff, 0xff, 0xff, 0xf9, 0x5, 0x99, 0x99,
    0xaf, 0xf5, 0x0, 0x0, 0x0, 0x8f, 0xa0, 0x0,
    0x0, 0x3, 0xfe, 0x10, 0x0, 0x0, 0xd, 0xf4,
    0x0, 0x0, 0x0, 0x9f, 0x90, 0x0, 0x0, 0x3,
    0xfe, 0x0, 0x0, 0x0, 0xd, 0xf4, 0x0, 0x0,
    0x0, 0x9f, 0x90, 0x0, 0x0, 0x4, 0xfd, 0x0,
    0x0, 0x0, 0xd, 0xfc, 0x99, 0x99, 0x97, 0x3f,
    0xff, 0xff, 0xff, 0xfd,

    /* U+007B "{" */
    0x0, 0x4, 0xdf, 0xb0, 0x0, 0xfd, 0x31, 0x0,
    0x3f, 0x70, 0x0, 0x4, 0xf6, 0x0, 0x0, 0x3f,
    0x60, 0x0, 0x3, 0xf7, 0x0, 0x0, 0x2f, 0x70,
    0x0, 0x1, 0xf8, 0x0, 0x0, 0x2f, 0x70, 0x0,
    0x1a, 0xf3, 0x0, 0x2f, 0xf7, 0x0, 0x0, 0x4c,
    0xf2, 0x0, 0x0, 0x3f, 0x60, 0x0, 0x1, 0xf8,
    0x0, 0x0, 0x2f, 0x70, 0x0, 0x2, 0xf7, 0x0,
    0x0, 0x3f, 0x60, 0x0, 0x4, 0xf6, 0x0, 0x0,
    0x3f, 0x70, 0x0, 0x1, 0xfc, 0x0, 0x0, 0x6,
    0xff, 0xa0, 0x0, 0x0, 0x11,

    /* U+007C "|" */
    0xba, 0xba, 0xba, 0xba, 0xba, 0xba, 0xba, 0xba,
    0xba, 0xba, 0xba, 0xba, 0xba, 0xba, 0xba, 0xba,
    0xba, 0xba, 0xba, 0xba, 0xba, 0xba, 0xba, 0xba,

    /* U+007D "}" */
    0x4f, 0xe9, 0x0, 0x0, 0x28, 0xf7, 0x0, 0x0,
    0xf, 0xa0, 0x0, 0x0, 0xfb, 0x0, 0x0, 0xf,
    0xb0, 0x0, 0x0, 0xfa, 0x0, 0x0, 0xf, 0x90,
    0x0, 0x0, 0xf8, 0x0, 0x0, 0xf, 0x90, 0x0,
    0x0, 0xce, 0x40, 0x0, 0x2, 0xef, 0x90, 0x0,
    0xaf, 0x72, 0x0, 0xf, 0xa0, 0x0, 0x0, 0xf8,
    0x0, 0x0, 0xf, 0x90, 0x0, 0x0, 0xfa, 0x0,
    0x0, 0xf, 0xb0, 0x0, 0x0, 0xfb, 0x0, 0x0,
    0xf, 0xb0, 0x0, 0x6, 0xf8, 0x0, 0x3f, 0xfc,
    0x10, 0x0, 0x20, 0x0, 0x0,

    /* U+007E "~" */
    0x0, 0x1, 0x10, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0xfa, 0x10, 0x0, 0x40, 0xb, 0xe6, 0x7f, 0xe6,
    0x3a, 0xf1, 0x6, 0x30, 0x1, 0xbf, 0xfe, 0x40,
    0x0, 0x0, 0x0, 0x3, 0x40, 0x0,

    /* U+5173 "关" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xe1, 0x0, 0x0,
    0x0, 0x7, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xb0, 0x0, 0x0, 0x0, 0xee, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0x50, 0x0, 0x0, 0x7f, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf8, 0x0, 0x0,
    0x1f, 0xc0, 0x0, 0x0, 0x0, 0xa, 0xbb, 0xbd,
    0xbb, 0xbb, 0xbd, 0xfc, 0xbb, 0xb6, 0x0, 0x0,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x29, 0x99,
    0x99, 0x99, 0xaf, 0xfe, 0x99, 0x99, 0x99, 0x97,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xef,
    0x5e, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xa0, 0x5f, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xcf, 0xd0, 0x0, 0x8f, 0xc1,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xef, 0xc1, 0x0,
    0x0, 0x9f, 0xe6, 0x0, 0x0, 0x0, 0x5d, 0xff,
    0x80, 0x0, 0x0, 0x0, 0x7f, 0xfc, 0x61, 0x5,
    0xef, 0xfa, 0x20, 0x0, 0x0, 0x0, 0x0, 0x2b,
    0xff, 0xe1, 0xb, 0x81, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0x85, 0x0,

    /* U+5206 "分" */
    0x0, 0x0, 0x0, 0x1c, 0x70, 0x0, 0x5, 0xc1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0x70,
    0x0, 0x4, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xfe, 0x0, 0x0, 0x0, 0xbf, 0x30, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xf6, 0x0, 0x0, 0x0,
    0x2f, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xd0,
    0x0, 0x0, 0x0, 0x6, 0xfa, 0x0, 0x0, 0x0,
    0x2, 0xef, 0x20, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0x70, 0x0, 0x0, 0x1d, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1e, 0xf6, 0x0, 0x2, 0xdf, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff, 0x70,
    0xb, 0xf9, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfd, 0x3f, 0xa0, 0x0, 0x60, 0x89, 0x99, 0xdf,
    0x99, 0x99, 0x99, 0xfc, 0x2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xde, 0x0, 0x0, 0x0, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xfb, 0x0, 0x0,
    0x0, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xf7, 0x0, 0x0, 0x1, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9, 0xf3, 0x0, 0x0, 0x2, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xd0, 0x0,
    0x0, 0x3, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0x50, 0x0, 0x0, 0x5, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x9, 0xfa, 0x0, 0x0, 0x0, 0x7,
    0xf4, 0x0, 0x0, 0x0, 0x2, 0xbf, 0xc0, 0x0,
    0x0, 0x0, 0xb, 0xf1, 0x0, 0x0, 0x2, 0x9f,
    0xfa, 0x0, 0x0, 0x1a, 0x99, 0xaf, 0xc0, 0x0,
    0x0, 0x1, 0xfc, 0x40, 0x0, 0x0, 0xc, 0xff,
    0xfc, 0x20, 0x0, 0x0, 0x0, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+521D "初" */
    0x0, 0x0, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0x70, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0x20, 0x7, 0x99, 0x99, 0x99, 0x99, 0x99, 0x20,
    0x0, 0x1, 0xf7, 0x0, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xf4, 0x6, 0x88, 0x8a, 0x88, 0x20, 0x0,
    0x3f, 0x80, 0x0, 0x6f, 0x30, 0xaf, 0xff, 0xff,
    0xf9, 0x0, 0x4, 0xf6, 0x0, 0x6, 0xf3, 0x0,
    0x0, 0x0, 0x9f, 0x20, 0x0, 0x5f, 0x50, 0x0,
    0x6f, 0x30, 0x0, 0x0, 0x2f, 0xa0, 0x0, 0x6,
    0xf4, 0x0, 0x7, 0xf2, 0x0, 0x0, 0xb, 0xf1,
    0x0, 0x0, 0x7f, 0x20, 0x0, 0x7f, 0x20, 0x0,
    0x6, 0xf7, 0x1d, 0x60, 0x9, 0xf0, 0x0, 0x8,
    0xf1, 0x0, 0x3, 0xff, 0x6b, 0xd1, 0x0, 0xbe,
    0x0, 0x0, 0x8f, 0x0, 0x1, 0xef, 0xff, 0xd1,
    0x0, 0xe, 0xc0, 0x0, 0x9, 0xf0, 0x1, 0xdf,
    0xdf, 0xbf, 0x30, 0x2, 0xf8, 0x0, 0x0, 0xaf,
    0x0, 0xdf, 0x69, 0xf1, 0xde, 0x20, 0x5f, 0x50,
    0x0, 0xb, 0xe0, 0x9, 0x60, 0x9f, 0x12, 0xd1,
    0xb, 0xf1, 0x0, 0x0, 0xcd, 0x0, 0x0, 0x9,
    0xf1, 0x0, 0x1, 0xfb, 0x0, 0x0, 0xd, 0xc0,
    0x0, 0x0, 0x9f, 0x10, 0x0, 0x9f, 0x50, 0x0,
    0x0, 0xfa, 0x0, 0x0, 0x9, 0xf1, 0x0, 0x3f,
    0xc0, 0x0, 0x0, 0x1f, 0x80, 0x0, 0x0, 0x9f,
    0x10, 0x1d, 0xf3, 0x0, 0x0, 0x5, 0xf6, 0x0,
    0x0, 0x9, 0xf1, 0x1d, 0xf7, 0x0, 0x39, 0x88,
    0xef, 0x10, 0x0, 0x0, 0x9f, 0x10, 0xb8, 0x0,
    0x1, 0xff, 0xfe, 0x50, 0x0, 0x0, 0x1, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+5316 "化" */
    0x0, 0x0, 0x0, 0x69, 0x10, 0x5, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xe0, 0x0,
    0xde, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xf7, 0x0, 0xd, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xee, 0x0, 0x0, 0xde, 0x0, 0x0,
    0x3, 0x30, 0x0, 0x0, 0xaf, 0x50, 0x0, 0xd,
    0xe0, 0x0, 0x1, 0xef, 0x20, 0x0, 0x5f, 0xf1,
    0x0, 0x0, 0xde, 0x0, 0x0, 0xbf, 0x90, 0x0,
    0x1e, 0xff, 0x10, 0x0, 0xd, 0xe0, 0x0, 0x9f,
    0xc0, 0x0, 0x1d, 0xfe, 0xf1, 0x0, 0x0, 0xde,
    0x0, 0xaf, 0xc0, 0x0, 0x1c, 0xf9, 0xaf, 0x10,
    0x0, 0xd, 0xe0, 0xbf, 0xc1, 0x0, 0x0, 0xdb,
    0xa, 0xf1, 0x0, 0x0, 0xdf, 0xcf, 0xb0, 0x0,
    0x0, 0x2, 0x0, 0xaf, 0x10, 0x0, 0xd, 0xff,
    0x90, 0x0, 0x0, 0x0, 0x0, 0xa, 0xf1, 0x0,
    0x8, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0x10, 0x4d, 0xff, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xf1, 0xbf, 0xf7, 0xde, 0x0,
    0x0, 0x0, 0x10, 0x0, 0x0, 0xaf, 0x13, 0x91,
    0xd, 0xe0, 0x0, 0x0, 0xc, 0xa0, 0x0, 0xa,
    0xf1, 0x0, 0x0, 0xde, 0x0, 0x0, 0x0, 0xdd,
    0x0, 0x0, 0xaf, 0x10, 0x0, 0xd, 0xe0, 0x0,
    0x0, 0xe, 0xb0, 0x0, 0xa, 0xf1, 0x0, 0x0,
    0xcf, 0x0, 0x0, 0x2, 0xf9, 0x0, 0x0, 0xaf,
    0x10, 0x0, 0xa, 0xfc, 0xaa, 0xaa, 0xdf, 0x40,
    0x0, 0xa, 0xf1, 0x0, 0x0, 0x2c, 0xff, 0xff,
    0xfe, 0x80,

    /* U+539F "原" */
    0x0, 0x8, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x88, 0x88, 0x60, 0x0, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0x0, 0xf, 0xa0,
    0x0, 0x0, 0x4, 0x94, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xfa, 0x0, 0x0, 0x0, 0xaf, 0x40, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xa0, 0x35, 0x55, 0x5f,
    0xe5, 0x55, 0x55, 0x50, 0x0, 0x0, 0xfa, 0x9,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x10, 0x0,
    0xf, 0x90, 0x9f, 0x10, 0x0, 0x0, 0x0, 0x9,
    0xf1, 0x0, 0x0, 0xf9, 0x9, 0xf2, 0x11, 0x11,
    0x11, 0x11, 0x9f, 0x10, 0x0, 0xf, 0x90, 0x9f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0x0, 0x1,
    0xf8, 0x9, 0xf3, 0x22, 0x22, 0x22, 0x22, 0xaf,
    0x10, 0x0, 0x2f, 0x70, 0x9f, 0x10, 0x0, 0x0,
    0x0, 0x9, 0xf1, 0x0, 0x3, 0xf6, 0x9, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x10, 0x0, 0x5f,
    0x40, 0x36, 0x66, 0x66, 0xfc, 0x66, 0x66, 0x60,
    0x0, 0x7, 0xf2, 0x0, 0x4, 0x0, 0xf, 0x90,
    0x3, 0x0, 0x0, 0x0, 0xaf, 0x0, 0x6, 0xf7,
    0x0, 0xf9, 0x3, 0xf9, 0x0, 0x0, 0xe, 0xb0,
    0x2, 0xfb, 0x0, 0xf, 0x90, 0x8, 0xf7, 0x0,
    0x2, 0xf8, 0x1, 0xde, 0x10, 0x0, 0xf9, 0x0,
    0xb, 0xf4, 0x0, 0x8f, 0x30, 0xcf, 0x30, 0x0,
    0xf, 0x90, 0x0, 0x1d, 0xf2, 0xe, 0xd0, 0x2d,
    0x50, 0x16, 0x67, 0xf9, 0x0, 0x0, 0x2f, 0x60,
    0x23, 0x0, 0x0, 0x0, 0xef, 0xfd, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+53EF "可" */
    0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0x7b, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb,
    0xbb, 0xbd, 0xfc, 0xba, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xf3, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xf3, 0x0, 0x0, 0x7f, 0xff, 0xff, 0xff, 0xf8,
    0x0, 0x8, 0xf3, 0x0, 0x0, 0x7f, 0x98, 0x88,
    0x89, 0xf8, 0x0, 0x8, 0xf3, 0x0, 0x0, 0x7f,
    0x20, 0x0, 0x1, 0xf8, 0x0, 0x8, 0xf3, 0x0,
    0x0, 0x7f, 0x20, 0x0, 0x1, 0xf8, 0x0, 0x8,
    0xf3, 0x0, 0x0, 0x7f, 0x20, 0x0, 0x1, 0xf8,
    0x0, 0x8, 0xf3, 0x0, 0x0, 0x7f, 0x20, 0x0,
    0x1, 0xf8, 0x0, 0x8, 0xf3, 0x0, 0x0, 0x7f,
    0x98, 0x88, 0x89, 0xf8, 0x0, 0x8, 0xf3, 0x0,
    0x0, 0x7f, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x8,
    0xf3, 0x0, 0x0, 0x7f, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xf3, 0x0, 0x0, 0x6f, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xf3, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb,
    0xbb, 0xbe, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xff, 0xfd, 0x70, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+540C "同" */
    0x8, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x88, 0x80, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf1, 0x1f, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9, 0xf1, 0x1f, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xf1,
    0x1f, 0x80, 0x27, 0x77, 0x77, 0x77, 0x77, 0x76,
    0x8, 0xf1, 0x1f, 0x80, 0x5f, 0xff, 0xff, 0xff,
    0xff, 0xfd, 0x8, 0xf1, 0x1f, 0x80, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xf1, 0x1f, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xf1,
    0x1f, 0x80, 0x3, 0xff, 0xff, 0xff, 0xff, 0x90,
    0x8, 0xf1, 0x1f, 0x80, 0x3, 0xfa, 0x77, 0x77,
    0x7f, 0xa0, 0x8, 0xf1, 0x1f, 0x80, 0x3, 0xf5,
    0x0, 0x0, 0xe, 0xa0, 0x8, 0xf1, 0x1f, 0x80,
    0x3, 0xf5, 0x0, 0x0, 0xe, 0xa0, 0x8, 0xf1,
    0x1f, 0x80, 0x3, 0xf5, 0x0, 0x0, 0xe, 0xa0,
    0x8, 0xf1, 0x1f, 0x80, 0x3, 0xf6, 0x22, 0x22,
    0x2e, 0xa0, 0x8, 0xf1, 0x1f, 0x80, 0x3, 0xff,
    0xff, 0xff, 0xff, 0xa0, 0x8, 0xf1, 0x1f, 0x80,
    0x3, 0xf7, 0x44, 0x44, 0x44, 0x20, 0x8, 0xf1,
    0x1f, 0x80, 0x2, 0xa3, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xf1, 0x1f, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xf1, 0x1f, 0x80, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x68, 0x8d, 0xf0, 0x1f, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xfe, 0x70,
    0x1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+5426 "否" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf5, 0x6, 0x88, 0x88,
    0x88, 0x88, 0xaf, 0xf8, 0x88, 0x88, 0x88, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x2e, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xf9,
    0x2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xaf, 0xef, 0x92, 0xfe, 0x81, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xa2, 0xf9, 0x3, 0xbf, 0xf7,
    0x0, 0x0, 0x1, 0x7e, 0xfd, 0x40, 0x1f, 0x90,
    0x0, 0x3c, 0xfe, 0x50, 0x2b, 0xff, 0xe6, 0x0,
    0x1, 0xf9, 0x0, 0x0, 0x5, 0xef, 0x80, 0xbc,
    0x60, 0x0, 0x0, 0x1f, 0x90, 0x0, 0x0, 0x1,
    0xa2, 0x0, 0x0, 0x0, 0x0, 0x1, 0xf9, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x27, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x77, 0x70, 0x0, 0x0, 0x5,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x10,
    0x0, 0x0, 0x5f, 0x60, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xf1, 0x0, 0x0, 0x5, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0x10, 0x0, 0x0, 0x5f,
    0x60, 0x0, 0x0, 0x0, 0x0, 0xb, 0xf1, 0x0,
    0x0, 0x5, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0x10, 0x0, 0x0, 0x5f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf1, 0x0, 0x0, 0x5, 0xfb,
    0x88, 0x88, 0x88, 0x88, 0x88, 0xdf, 0x10, 0x0,
    0x0, 0x5f, 0x60, 0x0, 0x0, 0x0, 0x0, 0xb,
    0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+56DE "回" */
    0x9, 0x99, 0x99, 0x99, 0x99, 0x99, 0x99, 0x99,
    0x99, 0x91, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf1, 0x1f, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xf1, 0x1f, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xf1,
    0x1f, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xf1, 0x1f, 0x90, 0x0, 0x88, 0x88, 0x88,
    0x88, 0x40, 0xa, 0xf1, 0x1f, 0x90, 0x1, 0xff,
    0xff, 0xff, 0xff, 0x80, 0xa, 0xf1, 0x1f, 0x90,
    0x1, 0xf7, 0x0, 0x0, 0x1f, 0x80, 0xa, 0xf1,
    0x1f, 0x90, 0x1, 0xf7, 0x0, 0x0, 0x1f, 0x80,
    0xa, 0xf1, 0x1f, 0x90, 0x1, 0xf7, 0x0, 0x0,
    0x1f, 0x80, 0xa, 0xf1, 0x1f, 0x90, 0x1, 0xf7,
    0x0, 0x0, 0x1f, 0x80, 0xa, 0xf1, 0x1f, 0x90,
    0x1, 0xf7, 0x0, 0x0, 0x1f, 0x80, 0xa, 0xf1,
    0x1f, 0x90, 0x1, 0xff, 0xff, 0xff, 0xff, 0x80,
    0xa, 0xf1, 0x1f, 0x90, 0x0, 0x88, 0x88, 0x88,
    0x88, 0x40, 0xa, 0xf1, 0x1f, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xf1, 0x1f, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xf1,
    0x1f, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xf1, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf1, 0x1f, 0xd8, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x88, 0x8d, 0xf1, 0x1f, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xf1,

    /* U+5728 "在" */
    0x0, 0x0, 0x0, 0x1, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0x38, 0x88, 0x8a, 0xfd, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x87, 0x0, 0x0, 0x9, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xc0, 0x0, 0x0, 0x59, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0x30, 0x0, 0x0, 0xaf, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xf9, 0x0, 0x0, 0x0,
    0xaf, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xf2, 0x0,
    0x0, 0x0, 0xaf, 0x0, 0x0, 0x0, 0x4, 0xff,
    0xf2, 0x9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0x7f, 0xea, 0xf2, 0x4, 0x88, 0x88, 0xdf, 0x88,
    0x88, 0x80, 0x9e, 0x28, 0xf2, 0x0, 0x0, 0x0,
    0xaf, 0x0, 0x0, 0x0, 0x11, 0x8, 0xf2, 0x0,
    0x0, 0x0, 0xaf, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xf2, 0x0, 0x0, 0x0, 0xaf, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xf2, 0x0, 0x0, 0x0, 0xaf, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xf2, 0x0, 0x0, 0x0,
    0xaf, 0x0, 0x0, 0x0, 0x0, 0x8, 0xf2, 0x0,
    0x0, 0x0, 0xaf, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xf2, 0x38, 0x88, 0x88, 0xdf, 0x88, 0x88, 0x87,
    0x0, 0x8, 0xf2, 0x7f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfd,

    /* U+5907 "备" */
    0x0, 0x0, 0x0, 0x0, 0x53, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xf9, 0x88, 0x88, 0x88, 0x84, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xfc,
    0x0, 0x0, 0x0, 0x5f, 0xd1, 0x0, 0x0, 0x0,
    0x4c, 0xfb, 0x7f, 0xd2, 0x0, 0x9, 0xfc, 0x10,
    0x0, 0x0, 0x0, 0xae, 0x60, 0x4, 0xef, 0x96,
    0xef, 0x90, 0x0, 0x0, 0x0, 0x0, 0x1, 0x0,
    0x0, 0x3e, 0xff, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0x7c, 0xff, 0xc9, 0xff, 0xe9,
    0x51, 0x0, 0x0, 0x5, 0x8b, 0xef, 0xfe, 0x92,
    0x0, 0x6, 0xbf, 0xff, 0xfc, 0xa4, 0xc, 0xff,
    0xc8, 0x30, 0x0, 0x0, 0x0, 0x0, 0x47, 0xbd,
    0xe1, 0x2, 0x21, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x70, 0x0, 0x0, 0x1, 0xfd, 0x88,
    0x88, 0xdf, 0x88, 0x88, 0xaf, 0x70, 0x0, 0x0,
    0x1, 0xfa, 0x0, 0x0, 0xbf, 0x0, 0x0, 0x4f,
    0x70, 0x0, 0x0, 0x1, 0xfb, 0x22, 0x22, 0xbf,
    0x22, 0x22, 0x6f, 0x70, 0x0, 0x0, 0x1, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0x0,
    0x0, 0x1, 0xfb, 0x22, 0x22, 0xbf, 0x22, 0x22,
    0x6f, 0x70, 0x0, 0x0, 0x1, 0xfa, 0x0, 0x0,
    0xbf, 0x0, 0x0, 0x4f, 0x70, 0x0, 0x0, 0x1,
    0xfc, 0x66, 0x66, 0xcf, 0x66, 0x66, 0x9f, 0x70,
    0x0, 0x0, 0x1, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x70, 0x0, 0x0, 0x1, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0x70, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+5927 "大" */
    0x0, 0x0, 0x0, 0x0, 0x3, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xa, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xde, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe0, 0x4a, 0xaa, 0xaa, 0xaa,
    0xcf, 0xfe, 0xaa, 0xaa, 0xaa, 0xa9, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0x9f, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xb1, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xf5, 0xa, 0xf3, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xfe, 0x0, 0x3f, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0x60,
    0x0, 0x9f, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xc0, 0x0, 0x0, 0xef, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xe1, 0x0, 0x0, 0x4, 0xff,
    0x30, 0x0, 0x0, 0x0, 0x7f, 0xf3, 0x0, 0x0,
    0x0, 0x7, 0xff, 0x50, 0x0, 0x2, 0xcf, 0xe2,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xa2, 0x7,
    0xff, 0xa1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xef, 0xe1, 0xa, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0x84, 0x0,

    /* U+59CB "始" */
    0x0, 0x0, 0x52, 0x0, 0x0, 0x0, 0x2, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xf5, 0x0, 0x0,
    0x0, 0xb, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xf3, 0x0, 0x0, 0x0, 0x2f, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xf0, 0x0, 0x0, 0x0, 0xaf,
    0x30, 0x1, 0x0, 0x0, 0x0, 0xa, 0xd0, 0x0,
    0x0, 0x2, 0xfa, 0x0, 0x7f, 0x20, 0x0, 0xf,
    0xff, 0xff, 0xff, 0x70, 0xb, 0xf2, 0x0, 0xe,
    0xd0, 0x0, 0x9, 0x9f, 0xb9, 0x9f, 0x60, 0x5f,
    0x70, 0x0, 0x5, 0xf7, 0x0, 0x0, 0x4f, 0x40,
    0x2f, 0x51, 0xec, 0x0, 0x0, 0x12, 0xdf, 0x10,
    0x0, 0x7f, 0x0, 0x4f, 0x4d, 0xfe, 0xde, 0xff,
    0xff, 0xff, 0x90, 0x0, 0xbc, 0x0, 0x7f, 0x1d,
    0xfd, 0xca, 0x98, 0x65, 0x4a, 0xf1, 0x0, 0xe9,
    0x0, 0xae, 0x1, 0x0, 0x0, 0x0, 0x0, 0x2,
    0x70, 0x3, 0xf5, 0x0, 0xda, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xfa, 0x2, 0xf7,
    0x0, 0xef, 0xff, 0xff, 0xff, 0xfe, 0x0, 0x0,
    0x7f, 0xd9, 0xf2, 0x0, 0xec, 0x77, 0x77, 0x77,
    0xde, 0x0, 0x0, 0x4, 0xff, 0xd0, 0x0, 0xe9,
    0x0, 0x0, 0x0, 0xae, 0x0, 0x0, 0x0, 0x6f,
    0xf4, 0x0, 0xe9, 0x0, 0x0, 0x0, 0xae, 0x0,
    0x0, 0x0, 0xde, 0xdf, 0x40, 0xe9, 0x0, 0x0,
    0x0, 0xae, 0x0, 0x0, 0xa, 0xf5, 0x2e, 0xc0,
    0xe9, 0x0, 0x0, 0x0, 0xae, 0x0, 0x0, 0x9f,
    0x90, 0x3, 0x20, 0xec, 0x88, 0x88, 0x88, 0xde,
    0x0, 0xc, 0xfb, 0x0, 0x0, 0x0, 0xef, 0xff,
    0xff, 0xff, 0xfe, 0x0, 0x6, 0x80, 0x0, 0x0,
    0x0, 0xe9, 0x0, 0x0, 0x0, 0xae, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+5BFC "导" */
    0x0, 0x78, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x82, 0x0, 0x0, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf5, 0x0, 0x0, 0xed, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xf5, 0x0, 0x0, 0xed,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xf5, 0x0,
    0x0, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf5, 0x0, 0x0, 0xee, 0x88, 0x88, 0x88, 0x88,
    0x88, 0x88, 0x82, 0x0, 0x0, 0xed, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xc3, 0x0, 0xcf,
    0x41, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1b, 0xf3,
    0x0, 0x5f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xa0, 0x0, 0x0, 0x34, 0x55, 0x55, 0x55,
    0x56, 0x75, 0x42, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9, 0xf1, 0x0, 0x0, 0x38, 0x88,
    0x88, 0x88, 0x88, 0x88, 0x8c, 0xf8, 0x88, 0x87,
    0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0x0, 0x1, 0xb9, 0x0, 0x0, 0x0,
    0x9, 0xf2, 0x0, 0x0, 0x0, 0x0, 0xaf, 0x80,
    0x0, 0x0, 0x9, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xf6, 0x0, 0x0, 0x9, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0x30, 0x0, 0x9, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0x40, 0x0,
    0x9, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x4, 0x66, 0x6d, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xff, 0xfe, 0x70, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+5C06 "将" */
    0x0, 0x0, 0x3, 0x20, 0x0, 0x0, 0x1, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xfa, 0x0, 0x0,
    0x1, 0xde, 0x20, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xa0, 0x0, 0x2, 0xdf, 0xff, 0xff, 0xfe, 0x30,
    0x34, 0x0, 0xfa, 0x0, 0x6, 0xfb, 0x66, 0x66,
    0x7f, 0xe1, 0xc, 0xf3, 0xf, 0xa0, 0x4c, 0xf8,
    0x10, 0x0, 0xa, 0xf6, 0x0, 0x1e, 0xe1, 0xfa,
    0xc, 0xd3, 0x8e, 0x20, 0x8, 0xf9, 0x0, 0x0,
    0x4f, 0xbf, 0xa0, 0x10, 0x1, 0xee, 0x2a, 0xf9,
    0x0, 0x0, 0x0, 0x9c, 0xfa, 0x0, 0x0, 0x3,
    0xff, 0xf7, 0x0, 0x0, 0x0, 0x1, 0xf, 0xa0,
    0x0, 0x6, 0xcf, 0xb2, 0x2, 0x0, 0x0, 0x0,
    0x0, 0xfa, 0x7, 0xbf, 0xfb, 0x40, 0x6, 0xf4,
    0x0, 0x0, 0x0, 0xf, 0xa0, 0x8b, 0x71, 0x0,
    0x0, 0x6f, 0x40, 0x0, 0x0, 0x6, 0xfa, 0x29,
    0x99, 0x99, 0x99, 0x9b, 0xfb, 0x98, 0x0, 0x7,
    0xff, 0xa4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xe0, 0x9, 0xf9, 0xfa, 0x0, 0x3, 0x0, 0x0,
    0x6, 0xf4, 0x0, 0xb, 0xf9, 0xf, 0xa0, 0x6,
    0xf6, 0x0, 0x0, 0x6f, 0x40, 0x0, 0xb8, 0x0,
    0xfa, 0x0, 0xb, 0xf3, 0x0, 0x6, 0xf4, 0x0,
    0x0, 0x0, 0xf, 0xa0, 0x0, 0x1e, 0xe0, 0x0,
    0x6f, 0x40, 0x0, 0x0, 0x0, 0xfa, 0x0, 0x0,
    0x5f, 0x50, 0x6, 0xf4, 0x0, 0x0, 0x0, 0xf,
    0xa0, 0x0, 0x0, 0x30, 0x0, 0x6f, 0x40, 0x0,
    0x0, 0x0, 0xfa, 0x0, 0x0, 0x0, 0x57, 0x7b,
    0xf3, 0x0, 0x0, 0x0, 0xf, 0xa0, 0x0, 0x0,
    0x6, 0xff, 0xea, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+6240 "所" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0x0, 0x0, 0x0, 0x0, 0x4, 0x8d,
    0x30, 0x0, 0x0, 0x26, 0xcf, 0x40, 0x0, 0x58,
    0xad, 0xff, 0xfd, 0x60, 0x58, 0xbe, 0xff, 0xfa,
    0x40, 0x0, 0xdf, 0xda, 0x85, 0x10, 0x0, 0xff,
    0xca, 0x63, 0x0, 0x0, 0x0, 0xdc, 0x0, 0x0,
    0x0, 0x0, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xdc, 0x0, 0x0, 0x0, 0x0, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0xff, 0xff, 0xfa, 0x0,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0xde, 0x88,
    0x88, 0xfa, 0x0, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xdc, 0x0, 0x0, 0xea, 0x0, 0xff, 0xff,
    0xff, 0xff, 0xf4, 0x0, 0xdc, 0x0, 0x0, 0xea,
    0x0, 0xfd, 0x99, 0x9f, 0xc9, 0x92, 0x0, 0xdc,
    0x0, 0x0, 0xea, 0x0, 0xfa, 0x0, 0x1f, 0x90,
    0x0, 0x0, 0xef, 0xff, 0xff, 0xfa, 0x1, 0xf9,
    0x0, 0x1f, 0x90, 0x0, 0x0, 0xed, 0x88, 0x88,
    0x85, 0x2, 0xf8, 0x0, 0x1f, 0x90, 0x0, 0x0,
    0xfa, 0x0, 0x0, 0x0, 0x4, 0xf6, 0x0, 0x1f,
    0x90, 0x0, 0x0, 0xf9, 0x0, 0x0, 0x0, 0x6,
    0xf4, 0x0, 0x1f, 0x90, 0x0, 0x2, 0xf8, 0x0,
    0x0, 0x0, 0x9, 0xf1, 0x0, 0x1f, 0x90, 0x0,
    0x4, 0xf5, 0x0, 0x0, 0x0, 0xe, 0xd0, 0x0,
    0x1f, 0x90, 0x0, 0x7, 0xf2, 0x0, 0x0, 0x0,
    0x5f, 0x70, 0x0, 0x1f, 0x90, 0x0, 0xb, 0xf0,
    0x0, 0x0, 0x0, 0xef, 0x10, 0x0, 0x1f, 0x90,
    0x0, 0x2f, 0xa0, 0x0, 0x0, 0xb, 0xf7, 0x0,
    0x0, 0x1f, 0x90, 0x0, 0xa, 0x40, 0x0, 0x0,
    0x8, 0xa0, 0x0, 0x0, 0x1f, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+62E9 "择" */
    0x0, 0x0, 0x73, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xf8, 0x0, 0x15,
    0x55, 0x55, 0x55, 0x55, 0x51, 0x0, 0x0, 0x1,
    0xf8, 0x0, 0x5f, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0x0, 0x0, 0x1, 0xf8, 0x0, 0x2, 0xbe, 0x32,
    0x22, 0x2c, 0xf2, 0x0, 0x0, 0x2, 0xf8, 0x0,
    0x0, 0x2f, 0xa0, 0x0, 0x8f, 0x60, 0x0, 0xf,
    0xff, 0xff, 0xff, 0x20, 0x5, 0xfa, 0x9, 0xf7,
    0x0, 0x0, 0x7, 0x88, 0xfc, 0x88, 0x10, 0x0,
    0x6f, 0xef, 0x60, 0x0, 0x0, 0x0, 0x1, 0xf8,
    0x0, 0x0, 0x1, 0x8f, 0xff, 0x70, 0x0, 0x0,
    0x0, 0x1, 0xf8, 0x0, 0x2, 0x8f, 0xf9, 0x3b,
    0xfe, 0x72, 0x0, 0x0, 0x1, 0xf8, 0x0, 0xcf,
    0xfa, 0x20, 0x0, 0x4c, 0xff, 0xd1, 0x0, 0x1,
    0xfa, 0x7b, 0x77, 0x10, 0x6, 0xe2, 0x0, 0x28,
    0x70, 0x0, 0x59, 0xff, 0xfb, 0x0, 0x0, 0x7,
    0xf3, 0x0, 0x0, 0x0, 0x1f, 0xff, 0xfa, 0x10,
    0xe, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x0, 0x9,
    0x52, 0xf8, 0x0, 0x6, 0x77, 0x7b, 0xf9, 0x77,
    0x72, 0x0, 0x0, 0x1, 0xf8, 0x0, 0x0, 0x0,
    0x7, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x1, 0xf8,
    0x0, 0x55, 0x55, 0x5a, 0xf7, 0x55, 0x55, 0x50,
    0x0, 0x1, 0xf8, 0x0, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0x0, 0x1, 0xf8, 0x0, 0x12,
    0x22, 0x28, 0xf4, 0x22, 0x22, 0x10, 0x0, 0x1,
    0xf8, 0x0, 0x0, 0x0, 0x7, 0xf3, 0x0, 0x0,
    0x0, 0x3, 0x8a, 0xf7, 0x0, 0x0, 0x0, 0x7,
    0xf3, 0x0, 0x0, 0x0, 0x3, 0xff, 0xc1, 0x0,
    0x0, 0x0, 0x7, 0xf3, 0x0, 0x0, 0x0,

    /* U+6309 "按" */
    0x0, 0x0, 0x73, 0x0, 0x0, 0x0, 0x5, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0x70, 0x0, 0x0,
    0x0, 0xcf, 0x0, 0x0, 0x0, 0x0, 0x1, 0xf7,
    0x0, 0x0, 0x0, 0x7, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0x70, 0x5, 0xaa, 0xaa, 0xbf, 0xda,
    0xaa, 0xa6, 0x0, 0x2, 0xf8, 0x0, 0x9f, 0xdd,
    0xdd, 0xdd, 0xdd, 0xdf, 0xa1, 0xff, 0xff, 0xff,
    0xd9, 0xf0, 0x0, 0x30, 0x0, 0x0, 0xea, 0x8,
    0x89, 0xfc, 0x87, 0x9f, 0x0, 0x6f, 0x50, 0x0,
    0xe, 0xa0, 0x0, 0x1f, 0x70, 0x5, 0x90, 0xb,
    0xf0, 0x0, 0x0, 0x86, 0x0, 0x1, 0xf7, 0x0,
    0x0, 0x2, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0x70, 0x2f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0x0, 0x1, 0xf7, 0x5, 0x98, 0x9f, 0xd8,
    0x88, 0x9f, 0xc8, 0x80, 0x0, 0x1f, 0xde, 0xf3,
    0x6, 0xf6, 0x0, 0x5, 0xf6, 0x0, 0x6, 0xaf,
    0xff, 0xc6, 0x0, 0xde, 0x0, 0x0, 0x9f, 0x20,
    0x3, 0xff, 0xcf, 0x70, 0x0, 0x6f, 0x80, 0x0,
    0xe, 0xc0, 0x0, 0x6, 0x11, 0xf7, 0x0, 0x8,
    0xff, 0x70, 0x7, 0xf6, 0x0, 0x0, 0x0, 0x1f,
    0x70, 0x0, 0x2, 0xbf, 0xe7, 0xfd, 0x0, 0x0,
    0x0, 0x1, 0xf7, 0x0, 0x0, 0x0, 0x4d, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x1f, 0x70, 0x0, 0x0,
    0x3, 0xef, 0xff, 0x80, 0x0, 0x0, 0x1, 0xf7,
    0x0, 0x0, 0x3a, 0xfe, 0x42, 0xcf, 0xd3, 0x0,
    0x47, 0x9f, 0x60, 0x9, 0xdf, 0xf9, 0x10, 0x0,
    0x6f, 0xf7, 0x5, 0xff, 0xb1, 0x0, 0x8c, 0x61,
    0x0, 0x0, 0x0, 0x2c, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+636E "据" */
    0x0, 0x2, 0x61, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xf3, 0x0, 0x27,
    0x77, 0x77, 0x77, 0x77, 0x77, 0x20, 0x0, 0x5,
    0xf3, 0x0, 0x5f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x40, 0x0, 0x5, 0xf3, 0x0, 0x5f, 0x40, 0x0,
    0x0, 0x0, 0x4f, 0x40, 0x0, 0x5, 0xf3, 0x0,
    0x5f, 0x40, 0x0, 0x0, 0x0, 0x4f, 0x40, 0x1f,
    0xff, 0xff, 0xfb, 0x5f, 0x74, 0x44, 0x44, 0x44,
    0x7f, 0x40, 0x8, 0x8a, 0xf9, 0x85, 0x5f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x40, 0x0, 0x5, 0xf3,
    0x0, 0x5f, 0x63, 0x33, 0x9f, 0x43, 0x33, 0x0,
    0x0, 0x5, 0xf3, 0x0, 0x5f, 0x40, 0x0, 0x7f,
    0x10, 0x0, 0x0, 0x0, 0x5, 0xf3, 0x0, 0x5f,
    0x86, 0x66, 0xaf, 0x76, 0x66, 0x60, 0x0, 0x5,
    0xf6, 0x87, 0x6f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf1, 0x0, 0x5b, 0xff, 0xf8, 0x7f, 0x20, 0x0,
    0x7f, 0x10, 0x0, 0x0, 0x3f, 0xff, 0xf7, 0x10,
    0x8f, 0x0, 0x0, 0x7f, 0x10, 0x0, 0x0, 0xa,
    0x66, 0xf3, 0x0, 0xae, 0x13, 0x33, 0x9f, 0x43,
    0x33, 0x10, 0x0, 0x5, 0xf3, 0x0, 0xcd, 0x5f,
    0xff, 0xff, 0xff, 0xff, 0x60, 0x0, 0x5, 0xf3,
    0x0, 0xfa, 0x5f, 0x32, 0x22, 0x22, 0x4f, 0x60,
    0x0, 0x5, 0xf3, 0x4, 0xf6, 0x5f, 0x10, 0x0,
    0x0, 0x2f, 0x60, 0x0, 0x5, 0xf3, 0x9, 0xf2,
    0x5f, 0x10, 0x0, 0x0, 0x2f, 0x60, 0x0, 0x5,
    0xf3, 0xf, 0xc0, 0x5f, 0x76, 0x66, 0x66, 0x7f,
    0x60, 0x6, 0x9c, 0xf2, 0x8f, 0x60, 0x5f, 0xff,
    0xff, 0xff, 0xff, 0x60, 0x5, 0xed, 0x70, 0x2b,
    0x0, 0x5f, 0x10, 0x0, 0x0, 0x2f, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+63A5 "接" */
    0x0, 0x3, 0x70, 0x0, 0x0, 0x0, 0x13, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0x10, 0x0, 0x0,
    0x6, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x7, 0xf1,
    0x0, 0x22, 0x22, 0x3e, 0xd2, 0x22, 0x22, 0x0,
    0x0, 0x7f, 0x10, 0xc, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf2, 0x0, 0x7, 0xf1, 0x0, 0x34, 0x86,
    0x44, 0x44, 0x78, 0x44, 0x1, 0xff, 0xff, 0xff,
    0x40, 0xd, 0xb0, 0x0, 0xc, 0xe0, 0x0, 0x8,
    0x8b, 0xf8, 0x82, 0x0, 0x4f, 0x40, 0x4, 0xf5,
    0x0, 0x0, 0x0, 0x7f, 0x10, 0x0, 0x0, 0xb5,
    0x0, 0xdc, 0x0, 0x0, 0x0, 0x7, 0xf1, 0x6,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb0, 0x0,
    0x7f, 0x10, 0x26, 0x66, 0x6b, 0xb6, 0x66, 0x66,
    0x64, 0x0, 0x7, 0xf5, 0x90, 0x0, 0x1, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xcf, 0xff, 0x42,
    0x22, 0x9f, 0x52, 0x22, 0x22, 0x22, 0x4f, 0xff,
    0xf6, 0x1a, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf1, 0xb5, 0x8f, 0x10, 0x24, 0x4d, 0xf4, 0x44,
    0x4a, 0xf5, 0x43, 0x0, 0x7, 0xf1, 0x0, 0x4,
    0xf6, 0x0, 0x0, 0xec, 0x0, 0x0, 0x0, 0x7f,
    0x10, 0x1, 0xef, 0x60, 0x0, 0x7f, 0x60, 0x0,
    0x0, 0x7, 0xf1, 0x0, 0x17, 0xdf, 0xf9, 0x6f,
    0xc0, 0x0, 0x0, 0x0, 0x7f, 0x10, 0x0, 0x0,
    0x39, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x7, 0xf1,
    0x0, 0x0, 0x16, 0xdf, 0xde, 0xfc, 0x40, 0x0,
    0x58, 0xcf, 0x0, 0x69, 0xdf, 0xfc, 0x50, 0x7,
    0xef, 0xb1, 0x6, 0xfe, 0x70, 0x8, 0xda, 0x62,
    0x0, 0x0, 0x1, 0x9c, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+63D0 "提" */
    0x0, 0x3, 0xa1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xf3, 0x0, 0xe,
    0xff, 0xff, 0xff, 0xff, 0xf7, 0x0, 0x0, 0x5,
    0xf3, 0x0, 0xf, 0xb4, 0x44, 0x44, 0x45, 0xf8,
    0x0, 0x0, 0x5, 0xf3, 0x0, 0xf, 0x90, 0x0,
    0x0, 0x1, 0xf8, 0x0, 0x3, 0x37, 0xf6, 0x32,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x1f,
    0xff, 0xff, 0xfa, 0xf, 0xa3, 0x33, 0x33, 0x34,
    0xf8, 0x0, 0x5, 0x59, 0xf7, 0x53, 0xf, 0x90,
    0x0, 0x0, 0x1, 0xf8, 0x0, 0x0, 0x5, 0xf3,
    0x0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0x0, 0x5, 0xf3, 0x0, 0x5, 0x55, 0x55, 0x55,
    0x55, 0x52, 0x0, 0x0, 0x5, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xf6, 0x85, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xb0, 0x0, 0x4b, 0xff, 0xf7, 0x66, 0x66, 0x6b,
    0xf6, 0x66, 0x66, 0x40, 0x2f, 0xff, 0xf7, 0x10,
    0x4, 0x60, 0x9, 0xf0, 0x0, 0x0, 0x0, 0xa,
    0x66, 0xf3, 0x0, 0xa, 0xe0, 0x9, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xf3, 0x0, 0xd, 0xb0,
    0x9, 0xff, 0xff, 0xfa, 0x0, 0x0, 0x5, 0xf3,
    0x0, 0x1f, 0xb0, 0x9, 0xf6, 0x66, 0x63, 0x0,
    0x0, 0x5, 0xf3, 0x0, 0x6f, 0xf2, 0x9, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xf3, 0x0, 0xcd,
    0xab, 0x9, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xf3, 0x5, 0xf6, 0x1e, 0xba, 0xf0, 0x0, 0x0,
    0x0, 0x3, 0x59, 0xf2, 0x2e, 0xd0, 0x3, 0xef,
    0xf8, 0x77, 0x77, 0x72, 0x8, 0xff, 0xb0, 0x7f,
    0x30, 0x0, 0x17, 0xce, 0xff, 0xff, 0xf0, 0x1,
    0x21, 0x0, 0x1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+6536 "收" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xa0, 0x0,
    0x8f, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xeb, 0x0, 0xc, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0xc9, 0x0, 0xe, 0xb0, 0x0, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0xe, 0xb0, 0x0, 0xeb, 0x0, 0x4f,
    0x80, 0x0, 0x0, 0x0, 0x0, 0xeb, 0x0, 0xe,
    0xb0, 0x8, 0xff, 0xff, 0xff, 0xff, 0xf0, 0xe,
    0xb0, 0x0, 0xeb, 0x0, 0xee, 0x88, 0x88, 0xbf,
    0xa7, 0x0, 0xeb, 0x0, 0xe, 0xb0, 0x5f, 0xf0,
    0x0, 0x8, 0xf1, 0x0, 0xe, 0xb0, 0x0, 0xeb,
    0xd, 0xff, 0x40, 0x0, 0xce, 0x0, 0x0, 0xeb,
    0x0, 0xe, 0xb7, 0xf8, 0xe9, 0x0, 0xf, 0xa0,
    0x0, 0xe, 0xb0, 0x0, 0xeb, 0xde, 0x9, 0xe0,
    0x4, 0xf5, 0x0, 0x0, 0xeb, 0x0, 0xe, 0xb2,
    0x40, 0x3f, 0x50, 0xaf, 0x10, 0x0, 0xe, 0xb0,
    0x0, 0xeb, 0x0, 0x0, 0xdd, 0x1f, 0xa0, 0x0,
    0x0, 0xeb, 0x3, 0x9f, 0xb0, 0x0, 0x5, 0xfc,
    0xf4, 0x0, 0x0, 0xf, 0xee, 0xff, 0xfb, 0x0,
    0x0, 0xd, 0xfc, 0x0, 0x0, 0x8, 0xff, 0xd7,
    0x1e, 0xb0, 0x0, 0x0, 0xbf, 0x90, 0x0, 0x0,
    0x3b, 0x30, 0x0, 0xeb, 0x0, 0x0, 0x8f, 0xef,
    0x50, 0x0, 0x0, 0x0, 0x0, 0xe, 0xb0, 0x0,
    0x8f, 0xb1, 0xdf, 0x40, 0x0, 0x0, 0x0, 0x0,
    0xeb, 0x1, 0xbf, 0xb0, 0x2, 0xef, 0x70, 0x0,
    0x0, 0x0, 0xe, 0xb5, 0xff, 0x90, 0x0, 0x2,
    0xdf, 0xc0, 0x0, 0x0, 0x0, 0xeb, 0x1c, 0x50,
    0x0, 0x0, 0x0, 0x94, 0x0,

    /* U+6570 "数" */
    0x0, 0x0, 0x3, 0x91, 0x0, 0x0, 0x1, 0x40,
    0x0, 0x0, 0x0, 0x0, 0xb6, 0x6, 0xf2, 0x9,
    0xb0, 0x5, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x8e,
    0x6, 0xf2, 0x2f, 0x70, 0x8, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0x56, 0xf2, 0xab, 0x0, 0xb,
    0xd0, 0x0, 0x0, 0x0, 0x6, 0x7a, 0x7a, 0xf8,
    0x88, 0x74, 0xe, 0xb4, 0x44, 0x44, 0x30, 0xd,
    0xee, 0xef, 0xff, 0xee, 0xe9, 0x3f, 0xff, 0xff,
    0xff, 0xd0, 0x0, 0x0, 0x6f, 0xff, 0x70, 0x0,
    0x8f, 0x74, 0x47, 0xf7, 0x30, 0x0, 0x6, 0xfc,
    0xf7, 0xed, 0x30, 0xdf, 0x80, 0x7, 0xf1, 0x0,
    0x0, 0x9f, 0x76, 0xf2, 0x2c, 0xc4, 0xff, 0xb0,
    0xa, 0xf0, 0x0, 0xd, 0xf6, 0x6, 0xf2, 0x0,
    0x1c, 0xe7, 0xf0, 0xd, 0xb0, 0x0, 0x4, 0x20,
    0x8, 0x30, 0x0, 0x3f, 0x63, 0xf4, 0x1f, 0x70,
    0x0, 0x0, 0x11, 0x6f, 0x51, 0x11, 0x4, 0x0,
    0xe9, 0x6f, 0x20, 0x0, 0x7, 0xff, 0xff, 0xff,
    0xff, 0xb0, 0x0, 0x9e, 0xcd, 0x0, 0x0, 0x2,
    0x48, 0xf6, 0x44, 0x6f, 0x60, 0x0, 0x2f, 0xf6,
    0x0, 0x0, 0x0, 0xd, 0xb0, 0x0, 0xbe, 0x0,
    0x0, 0xd, 0xf1, 0x0, 0x0, 0x0, 0x5f, 0xe7,
    0x6, 0xf4, 0x0, 0x0, 0x5f, 0xf7, 0x0, 0x0,
    0x0, 0x2, 0x9f, 0xef, 0x80, 0x0, 0x3, 0xfc,
    0xbf, 0x30, 0x0, 0x0, 0x0, 0x2b, 0xff, 0xe5,
    0x0, 0x3e, 0xe1, 0x1e, 0xe2, 0x0, 0x0, 0x39,
    0xff, 0x62, 0xba, 0x7, 0xfe, 0x20, 0x4, 0xfe,
    0x40, 0xc, 0xfe, 0x81, 0x0, 0x0, 0xcf, 0xc1,
    0x0, 0x0, 0x4e, 0xe1, 0x4, 0x40, 0x0, 0x0,
    0x0, 0x56, 0x0, 0x0, 0x0, 0x1, 0x30,

    /* U+662F "是" */
    0x0, 0x1, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55,
    0x55, 0x10, 0x0, 0x0, 0x4, 0xfe, 0xee, 0xee,
    0xee, 0xee, 0xee, 0xef, 0x50, 0x0, 0x0, 0x4,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0x50,
    0x0, 0x0, 0x4, 0xf8, 0x55, 0x55, 0x55, 0x55,
    0x55, 0x8f, 0x50, 0x0, 0x0, 0x4, 0xfe, 0xdd,
    0xdd, 0xdd, 0xdd, 0xdd, 0xef, 0x50, 0x0, 0x0,
    0x4, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f,
    0x50, 0x0, 0x0, 0x4, 0xf8, 0x55, 0x55, 0x55,
    0x55, 0x55, 0x8f, 0x50, 0x0, 0x0, 0x3, 0xee,
    0xee, 0xee, 0xee, 0xee, 0xee, 0xee, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xe0, 0x4, 0x88,
    0x88, 0x88, 0x88, 0xcf, 0x88, 0x88, 0x88, 0x88,
    0x70, 0x0, 0x0, 0x9, 0x70, 0x0, 0x8f, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xa0,
    0x0, 0x8f, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0x60, 0x0, 0x8f, 0xff, 0xff, 0xff,
    0xf4, 0x0, 0x0, 0x0, 0xaf, 0xa0, 0x0, 0x8f,
    0x88, 0x88, 0x88, 0x82, 0x0, 0x0, 0x1, 0xff,
    0xf3, 0x0, 0x8f, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xf4, 0xde, 0x20, 0x8f, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xa0, 0x3f, 0xf7,
    0x9f, 0x10, 0x0, 0x0, 0x0, 0x0, 0x3, 0xfe,
    0x10, 0x2, 0xcf, 0xff, 0xb9, 0x99, 0x99, 0x99,
    0x91, 0xb, 0xf3, 0x0, 0x0, 0x4, 0x9c, 0xef,
    0xff, 0xff, 0xff, 0xe0, 0x0, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+6709 "有" */
    0x0, 0x0, 0x0, 0x1, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x39, 0x99,
    0x99, 0x9f, 0xd9, 0x99, 0x99, 0x99, 0x99, 0x98,
    0x5f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfd, 0x0, 0x0, 0x2, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xe8, 0x88, 0x88, 0x88, 0x88, 0x82, 0x0,
    0x0, 0x2, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf4, 0x0, 0x0, 0x1d, 0xff, 0x70, 0x0, 0x0,
    0x0, 0x5, 0xf4, 0x0, 0x1, 0xdf, 0x8f, 0x70,
    0x0, 0x0, 0x0, 0x5, 0xf4, 0x0, 0x3e, 0xf6,
    0x2f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x0,
    0x9f, 0x50, 0x2f, 0xb7, 0x77, 0x77, 0x77, 0x7a,
    0xf4, 0x0, 0x3, 0x0, 0x2f, 0x70, 0x0, 0x0,
    0x0, 0x5, 0xf4, 0x0, 0x0, 0x0, 0x2f, 0x70,
    0x0, 0x0, 0x0, 0x5, 0xf4, 0x0, 0x0, 0x0,
    0x2f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x0,
    0x0, 0x0, 0x2f, 0xa6, 0x66, 0x66, 0x66, 0x69,
    0xf4, 0x0, 0x0, 0x0, 0x2f, 0x70, 0x0, 0x0,
    0x0, 0x5, 0xf4, 0x0, 0x0, 0x0, 0x2f, 0x70,
    0x0, 0x0, 0x0, 0x5, 0xf4, 0x0, 0x0, 0x0,
    0x2f, 0x70, 0x0, 0x0, 0x78, 0x8c, 0xf3, 0x0,
    0x0, 0x0, 0x2f, 0x70, 0x0, 0x0, 0xaf, 0xff,
    0xa0, 0x0, 0x0, 0x0, 0x1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+67E5 "查" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x34, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0x88, 0x88, 0x88, 0x88, 0xdf, 0x88,
    0x88, 0x88, 0x88, 0x70, 0x9, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe0, 0x0,
    0x0, 0x0, 0x2, 0xed, 0xce, 0xaf, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xd1, 0xbe,
    0xa, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1a,
    0xfb, 0x10, 0xbe, 0x0, 0x8f, 0xd4, 0x0, 0x0,
    0x0, 0x6, 0xef, 0x70, 0x0, 0xbe, 0x0, 0x3,
    0xdf, 0xb3, 0x0, 0x7, 0xef, 0xc2, 0x0, 0x0,
    0x45, 0x0, 0x0, 0x8, 0xff, 0xd1, 0x5, 0xc4,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x17,
    0x60, 0x0, 0x0, 0xf, 0xb3, 0x33, 0x33, 0x33,
    0x38, 0xf4, 0x0, 0x0, 0x0, 0x0, 0xf, 0xa0,
    0x0, 0x0, 0x0, 0x7, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xb3, 0x33, 0x33,
    0x33, 0x38, 0xf4, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xa0, 0x0, 0x0, 0x0, 0x7, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf4, 0x0, 0x0, 0x0, 0x0, 0x3, 0x33, 0x33,
    0x33, 0x33, 0x33, 0x30, 0x0, 0x0, 0x1, 0x77,
    0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77,
    0x40, 0x3, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+6B63 "正" */
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf2, 0xa, 0xaa, 0xaa, 0xaa, 0xac, 0xfd,
    0xaa, 0xaa, 0xaa, 0xa2, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0x40, 0x0, 0x4, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xd0, 0x0,
    0x4, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xd0, 0x0, 0x4, 0xfc, 0x99, 0x99, 0x99, 0x20,
    0x0, 0xd, 0xd0, 0x0, 0x4, 0xff, 0xff, 0xff,
    0xff, 0x50, 0x0, 0xd, 0xd0, 0x0, 0x4, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xd0, 0x0,
    0x4, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xd0, 0x0, 0x4, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xd, 0xd0, 0x0, 0x4, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xd, 0xd0, 0x0, 0x4, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xd0, 0x0,
    0x4, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xd0, 0x0, 0x4, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0x9a, 0xaa, 0xaa, 0xaa, 0xaa, 0xaa,
    0xaa, 0xaa, 0xaa, 0xa9,

    /* U+6B65 "步" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x35, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5a,
    0x10, 0x0, 0xbf, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xf2, 0x0, 0xb, 0xff, 0xff, 0xff,
    0xf9, 0x0, 0x0, 0x0, 0x8f, 0x20, 0x0, 0xbf,
    0x87, 0x77, 0x77, 0x40, 0x0, 0x0, 0x8, 0xf2,
    0x0, 0xb, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0x20, 0x0, 0xbf, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9a, 0xad, 0xfb, 0xaa, 0xae, 0xfa,
    0xaa, 0xaa, 0xaa, 0xa4, 0xd, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x0, 0xe, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xf7, 0x0, 0xee, 0x0,
    0x0, 0xb, 0x50, 0x0, 0x0, 0x0, 0xdf, 0x20,
    0xe, 0xe0, 0x0, 0x8, 0xf7, 0x0, 0x0, 0x0,
    0xbf, 0x50, 0x0, 0xee, 0x0, 0x4, 0xfd, 0x0,
    0x0, 0x1, 0xcf, 0x80, 0x0, 0xe, 0xe0, 0x3,
    0xff, 0x20, 0x0, 0x0, 0xdf, 0x80, 0x0, 0x0,
    0xee, 0x4, 0xff, 0x40, 0x0, 0x0, 0x5, 0x70,
    0x0, 0x0, 0xc, 0xd9, 0xfe, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7e, 0xfc, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x39, 0xef,
    0xe6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x13, 0x6a,
    0xef, 0xfc, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0xff, 0xfc, 0x82, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xa7, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+6F0F "漏" */
    0x0, 0x31, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1e, 0xf6, 0x0, 0xdf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf1, 0x0, 0x3d, 0xfc,
    0x1e, 0xc7, 0x77, 0x77, 0x77, 0x77, 0xbf, 0x10,
    0x0, 0xa, 0xc0, 0xea, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xf1, 0x0, 0x0, 0x1, 0xe, 0xb2, 0x22,
    0x22, 0x22, 0x22, 0x9f, 0x10, 0x0, 0x0, 0x0,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0x1,
    0x0, 0x0, 0xe, 0xb3, 0x33, 0x33, 0x33, 0x33,
    0x33, 0x0, 0xce, 0x60, 0x0, 0xeb, 0x33, 0x33,
    0x33, 0x33, 0x33, 0x32, 0x3, 0xcf, 0xd3, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x0,
    0x6e, 0x20, 0xfa, 0x22, 0x22, 0x4f, 0x52, 0x22,
    0x21, 0x0, 0x0, 0x0, 0xf, 0x83, 0x33, 0x34,
    0xf6, 0x33, 0x33, 0x10, 0x0, 0x0, 0x1, 0xfa,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x0, 0x0,
    0x30, 0x2f, 0x9f, 0x31, 0x13, 0xf5, 0x11, 0x1f,
    0x70, 0x0, 0x3f, 0x44, 0xf7, 0xf4, 0xc2, 0x2f,
    0x5c, 0x30, 0xf7, 0x0, 0x9, 0xf1, 0x6f, 0x5f,
    0x28, 0xf5, 0xf4, 0x6f, 0x3f, 0x70, 0x0, 0xeb,
    0x9, 0xf3, 0xf2, 0x4, 0x2f, 0x40, 0x40, 0xf7,
    0x0, 0x6f, 0x50, 0xdb, 0x3f, 0x48, 0x2, 0xf6,
    0xa1, 0xf, 0x70, 0xc, 0xe0, 0x2f, 0x73, 0xf3,
    0xcc, 0x3f, 0x4a, 0xd2, 0xf7, 0x4, 0xf8, 0x9,
    0xf1, 0x3f, 0x20, 0x94, 0xf4, 0x7, 0xf, 0x70,
    0xbf, 0x21, 0xfa, 0x3, 0xf2, 0x0, 0x2f, 0x40,
    0x22, 0xf6, 0x1, 0x70, 0x9, 0x20, 0x3f, 0x20,
    0x1, 0xe3, 0xf, 0xfd, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+7535 "电" */
    0x0, 0x0, 0x0, 0x0, 0x32, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xec, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xec, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x11,
    0x11, 0x11, 0xec, 0x11, 0x11, 0x11, 0x10, 0x0,
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf1, 0x0, 0x1f, 0xd8, 0x88, 0x88, 0xfe, 0x88,
    0x88, 0x8d, 0xf1, 0x0, 0x1f, 0xa0, 0x0, 0x0,
    0xec, 0x0, 0x0, 0x9, 0xf1, 0x0, 0x1f, 0xa0,
    0x0, 0x0, 0xec, 0x0, 0x0, 0x9, 0xf1, 0x0,
    0x1f, 0xd8, 0x88, 0x88, 0xfe, 0x88, 0x88, 0x8c,
    0xf1, 0x0, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf1, 0x0, 0x1f, 0xa0, 0x0, 0x0,
    0xec, 0x0, 0x0, 0x9, 0xf1, 0x0, 0x1f, 0xa0,
    0x0, 0x0, 0xec, 0x0, 0x0, 0x9, 0xf1, 0x0,
    0x1f, 0xa0, 0x0, 0x0, 0xec, 0x0, 0x0, 0x9,
    0xf1, 0x0, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf1, 0x0, 0x1f, 0xea, 0xaa, 0xaa,
    0xfe, 0xaa, 0xaa, 0xaa, 0xa0, 0x0, 0x1f, 0xa0,
    0x0, 0x0, 0xec, 0x0, 0x0, 0x0, 0x5, 0x50,
    0x6, 0x30, 0x0, 0x0, 0xec, 0x0, 0x0, 0x0,
    0x8, 0xf2, 0x0, 0x0, 0x0, 0x0, 0xec, 0x0,
    0x0, 0x0, 0xa, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0xde, 0x0, 0x0, 0x0, 0xd, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xdb, 0xbb, 0xbb, 0xdf, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x1b, 0xff, 0xff, 0xff,
    0xfb, 0x0,

    /* U+7684 "的" */
    0x0, 0x0, 0x55, 0x0, 0x0, 0x0, 0x73, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xfd, 0x0, 0x0, 0x2,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0x2, 0xf7, 0x0,
    0x0, 0x6, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x7,
    0xf1, 0x0, 0x0, 0xc, 0xe0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0xff, 0xff, 0x90, 0x2f, 0xff, 0xff,
    0xff, 0xf6, 0x1f, 0xb8, 0x88, 0x8f, 0x90, 0x9f,
    0x88, 0x88, 0x89, 0xf6, 0x1f, 0x70, 0x0, 0xe,
    0x91, 0xfb, 0x0, 0x0, 0x3, 0xf5, 0x1f, 0x70,
    0x0, 0xe, 0x9a, 0xf4, 0x0, 0x0, 0x3, 0xf5,
    0x1f, 0x70, 0x0, 0xe, 0xad, 0xa0, 0x0, 0x0,
    0x4, 0xf4, 0x1f, 0x70, 0x0, 0xe, 0x90, 0x8,
    0xd0, 0x0, 0x4, 0xf3, 0x1f, 0xff, 0xff, 0xff,
    0x90, 0x3, 0xfa, 0x0, 0x5, 0xf3, 0x1f, 0xb7,
    0x77, 0x7f, 0x90, 0x0, 0x7f, 0x50, 0x6, 0xf2,
    0x1f, 0x70, 0x0, 0xe, 0x90, 0x0, 0xd, 0xf1,
    0x7, 0xf1, 0x1f, 0x70, 0x0, 0xe, 0x90, 0x0,
    0x3, 0xfa, 0x7, 0xf0, 0x1f, 0x70, 0x0, 0xe,
    0x90, 0x0, 0x0, 0x72, 0x9, 0xf0, 0x1f, 0x70,
    0x0, 0xe, 0x90, 0x0, 0x0, 0x0, 0xa, 0xe0,
    0x1f, 0x70, 0x0, 0xe, 0x90, 0x0, 0x0, 0x0,
    0xc, 0xc0, 0x1f, 0xff, 0xff, 0xff, 0x90, 0x0,
    0x0, 0x0, 0xe, 0xa0, 0x1f, 0xb7, 0x77, 0x77,
    0x40, 0x0, 0x0, 0x0, 0x2f, 0x80, 0x1f, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x57, 0x55, 0xbf, 0x30,
    0x1f, 0x70, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0x22, 0x10, 0x0,

    /* U+770B "看" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0x0, 0x0, 0x1, 0x22, 0x23, 0x34, 0x56,
    0x78, 0x9b, 0xdf, 0xf9, 0x0, 0x0, 0x7f, 0xff,
    0xff, 0xff, 0xfd, 0xcb, 0x98, 0x63, 0x10, 0x0,
    0x0, 0x22, 0x21, 0x16, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x15, 0x55, 0x55, 0xcf, 0x75,
    0x55, 0x55, 0x55, 0x51, 0x0, 0x5, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x30, 0x0,
    0x0, 0x0, 0x7, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x56, 0x66, 0x66, 0xef, 0x66, 0x66,
    0x66, 0x66, 0x66, 0x63, 0xe, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0x0,
    0x0, 0x2f, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xfe, 0xee, 0xee, 0xee,
    0xee, 0xee, 0x40, 0x0, 0x0, 0xa, 0xff, 0x76,
    0x66, 0x66, 0x66, 0x69, 0xf5, 0x0, 0x0, 0xa,
    0xfd, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x5f, 0x50,
    0x0, 0x2c, 0xf7, 0x8f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf5, 0x0, 0x3f, 0xf6, 0x8, 0xf4, 0x33,
    0x33, 0x33, 0x33, 0x7f, 0x50, 0x0, 0x82, 0x0,
    0x8f, 0x32, 0x22, 0x22, 0x22, 0x26, 0xf5, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x50, 0x0, 0x0, 0x0, 0x8f, 0x10, 0x0,
    0x0, 0x0, 0x5, 0xf5, 0x0, 0x0, 0x0, 0x8,
    0xf6, 0x55, 0x55, 0x55, 0x55, 0x8f, 0x50, 0x0,
    0x0, 0x0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf5, 0x0, 0x0, 0x0, 0x8, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0x50, 0x0,

    /* U+793A "示" */
    0x0, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x0, 0x0, 0x5b, 0xbb, 0xbb, 0xbb, 0xbb,
    0xbb, 0xbb, 0xba, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x49, 0x99, 0x99, 0x99, 0x99, 0x99,
    0x99, 0x99, 0x99, 0x98, 0x7f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x76, 0x0, 0xa, 0xf2,
    0x0, 0x17, 0x0, 0x0, 0x0, 0x2, 0xfb, 0x0,
    0xa, 0xf2, 0x0, 0x6f, 0x90, 0x0, 0x0, 0xa,
    0xf4, 0x0, 0xa, 0xf2, 0x0, 0xb, 0xf4, 0x0,
    0x0, 0x3f, 0xc0, 0x0, 0xa, 0xf2, 0x0, 0x2,
    0xfd, 0x0, 0x0, 0xdf, 0x30, 0x0, 0xa, 0xf2,
    0x0, 0x0, 0x8f, 0x70, 0x9, 0xf8, 0x0, 0x0,
    0xa, 0xf2, 0x0, 0x0, 0x1f, 0xe0, 0x8f, 0xc0,
    0x0, 0x0, 0xa, 0xf2, 0x0, 0x0, 0x8, 0xf7,
    0x5c, 0x10, 0x0, 0x0, 0xa, 0xf2, 0x0, 0x0,
    0x1, 0xd5, 0x0, 0x0, 0x4, 0xaa, 0xae, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff,
    0xfe, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+79BB "离" */
    0x0, 0x0, 0x0, 0x0, 0x6, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x27, 0x77, 0x77, 0x77,
    0x7c, 0xf8, 0x77, 0x77, 0x77, 0x76, 0x6f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x0, 0x3, 0x20, 0x24, 0x0, 0x0, 0x36, 0x0,
    0x31, 0x0, 0x0, 0xf, 0x90, 0x5d, 0xc5, 0x7,
    0xe6, 0x2, 0xf8, 0x0, 0x0, 0xf, 0x90, 0x0,
    0x6f, 0xfe, 0x20, 0x2, 0xf8, 0x0, 0x0, 0xf,
    0x90, 0x5, 0xde, 0x8e, 0xd5, 0x2, 0xf8, 0x0,
    0x0, 0xf, 0x91, 0xee, 0x60, 0x0, 0x7f, 0x52,
    0xf8, 0x0, 0x0, 0xf, 0xc6, 0x96, 0x66, 0x66,
    0x67, 0x67, 0xf8, 0x0, 0x0, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xd, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0x77, 0x77, 0x77, 0x9f, 0xc7, 0x77, 0x77,
    0x77, 0x60, 0x6, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe0, 0x6, 0xf3, 0x0, 0x9,
    0xf3, 0x0, 0x82, 0x0, 0xb, 0xe0, 0x6, 0xf3,
    0x0, 0x4f, 0x80, 0x0, 0xcd, 0x0, 0xb, 0xe0,
    0x6, 0xf3, 0x3, 0xed, 0x34, 0x67, 0xaf, 0x90,
    0xb, 0xe0, 0x6, 0xf3, 0xa, 0xff, 0xff, 0xfd,
    0xcb, 0xf4, 0xb, 0xe0, 0x6, 0xf3, 0x4, 0x85,
    0x31, 0x0, 0x0, 0x81, 0xb, 0xe0, 0x6, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x45, 0x5d, 0xd0,
    0x6, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0xfd, 0x60,

    /* U+79D2 "秒" */
    0x0, 0x0, 0x0, 0x0, 0x10, 0x0, 0x0, 0x94,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x26, 0xaf, 0xb0,
    0x0, 0x1, 0xf8, 0x0, 0x0, 0x0, 0x6, 0xbe,
    0xff, 0xe9, 0x40, 0x0, 0x1, 0xf8, 0x0, 0x0,
    0x0, 0x5, 0xa7, 0x9f, 0x40, 0x0, 0x0, 0x1,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0x40,
    0x0, 0x3b, 0x31, 0xf8, 0xa, 0xc0, 0x0, 0x0,
    0x0, 0x6f, 0x40, 0x0, 0x6f, 0x21, 0xf8, 0x6,
    0xf5, 0x0, 0x6, 0x66, 0x9f, 0x86, 0x62, 0x9f,
    0x1, 0xf8, 0x0, 0xec, 0x0, 0xf, 0xff, 0xff,
    0xff, 0xf5, 0xcc, 0x1, 0xf8, 0x0, 0x8f, 0x30,
    0x2, 0x22, 0xcf, 0x72, 0x21, 0xf8, 0x1, 0xf8,
    0x0, 0x1f, 0x90, 0x0, 0x1, 0xff, 0xe1, 0x5,
    0xf3, 0x1, 0xf8, 0x0, 0xc, 0xd0, 0x0, 0x8,
    0xff, 0xec, 0xa, 0xe0, 0x1, 0xf8, 0x0, 0x2,
    0x10, 0x0, 0x1f, 0xbf, 0x6f, 0x80, 0x30, 0x1,
    0xf8, 0x0, 0x87, 0x0, 0x0, 0x8e, 0x6f, 0x47,
    0xe0, 0x0, 0x1, 0xf8, 0x2, 0xfa, 0x0, 0x2,
    0xf7, 0x6f, 0x40, 0x30, 0x0, 0x1, 0xf8, 0xa,
    0xf3, 0x0, 0xc, 0xe0, 0x6f, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xa0, 0x0, 0x2f, 0x60, 0x6f,
    0x40, 0x0, 0x0, 0x0, 0x3, 0xfe, 0x10, 0x0,
    0x5, 0x0, 0x6f, 0x40, 0x0, 0x0, 0x0, 0x5f,
    0xe3, 0x0, 0x0, 0x0, 0x0, 0x6f, 0x40, 0x0,
    0x0, 0x2a, 0xfe, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0x40, 0x0, 0x4a, 0xff, 0xa1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0x40, 0xaf, 0xff, 0xb3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0x40,
    0x6c, 0x72, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+7AEF "端" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x52,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0x10, 0x0, 0x33,
    0x0, 0xf, 0x60, 0x2, 0x40, 0x0, 0x2, 0xf8,
    0x0, 0xc, 0xd0, 0x0, 0xf6, 0x0, 0x8f, 0x10,
    0x0, 0xa, 0xe0, 0x0, 0xcd, 0x0, 0xf, 0x60,
    0x8, 0xf1, 0x3, 0x33, 0x68, 0x43, 0x1c, 0xd0,
    0x0, 0xf6, 0x0, 0x8f, 0x10, 0xef, 0xff, 0xff,
    0xf8, 0xce, 0x88, 0x8f, 0xb8, 0x8c, 0xf1, 0x4,
    0x44, 0x44, 0x44, 0x2b, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x10, 0x19, 0x10, 0x9, 0x80, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf4, 0x0, 0xc9,
    0x68, 0x88, 0x88, 0x88, 0x88, 0x88, 0x70, 0xe,
    0x60, 0xe, 0x7b, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfd, 0x0, 0xb9, 0x0, 0xf4, 0x0, 0x0, 0x4,
    0xf5, 0x0, 0x0, 0x0, 0x9, 0xb0, 0x3f, 0x20,
    0x0, 0x0, 0x7f, 0x10, 0x0, 0x0, 0x0, 0x7d,
    0x5, 0xf0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x50, 0x6, 0xe0, 0x7c, 0x0, 0xfb, 0x6a, 0xf6,
    0x8f, 0x67, 0xf5, 0x0, 0x4f, 0xa, 0x90, 0xf,
    0x80, 0x6e, 0x4, 0xf0, 0x1f, 0x50, 0x1, 0x40,
    0xd9, 0x86, 0xf8, 0x6, 0xe0, 0x4f, 0x1, 0xf5,
    0x1, 0x48, 0xbf, 0xff, 0x9f, 0x80, 0x6e, 0x4,
    0xf0, 0x1f, 0x50, 0xef, 0xfe, 0xa5, 0x10, 0xf8,
    0x6, 0xe0, 0x4f, 0x1, 0xf5, 0x7, 0x62, 0x0,
    0x0, 0xf, 0x80, 0x6e, 0x4, 0xf0, 0x1f, 0x50,
    0x0, 0x0, 0x0, 0x0, 0xf8, 0x6, 0xe0, 0x4f,
    0x36, 0xf5, 0x0, 0x0, 0x0, 0x0, 0xe, 0x80,
    0x6e, 0x4, 0xf5, 0xfc, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+80FD "能" */
    0x0, 0x4, 0x30, 0x0, 0x0, 0x7, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xfd, 0x0, 0x0, 0x0,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0x41,
    0xe8, 0x0, 0xf, 0xa0, 0x0, 0x4a, 0x20, 0x0,
    0x3f, 0xa0, 0x8, 0xf4, 0x0, 0xfa, 0x17, 0xdf,
    0xe6, 0x0, 0x1d, 0xd0, 0x0, 0x2e, 0xd0, 0xf,
    0xef, 0xfa, 0x50, 0x0, 0xa, 0xff, 0xef, 0xff,
    0xff, 0x60, 0xfd, 0x50, 0x0, 0x0, 0x0, 0x5a,
    0x86, 0x54, 0x32, 0xda, 0xf, 0xa0, 0x0, 0x0,
    0x65, 0x0, 0x0, 0x0, 0x0, 0x1, 0x0, 0xfa,
    0x0, 0x0, 0xb, 0xc0, 0xb, 0xcc, 0xcc, 0xcc,
    0xb0, 0xe, 0xe7, 0x77, 0x78, 0xf9, 0x0, 0xed,
    0xaa, 0xaa, 0xef, 0x0, 0x7f, 0xff, 0xff, 0xfd,
    0x20, 0xe, 0x90, 0x0, 0xb, 0xf0, 0x4, 0x20,
    0x0, 0x0, 0x0, 0x0, 0xeb, 0x55, 0x55, 0xcf,
    0x0, 0xfa, 0x0, 0x0, 0x0, 0x0, 0xe, 0xff,
    0xff, 0xff, 0xf0, 0xf, 0xa0, 0x0, 0x3b, 0x70,
    0x0, 0xe9, 0x0, 0x0, 0xbf, 0x0, 0xfa, 0x6,
    0xcf, 0xf8, 0x0, 0xe, 0xa0, 0x0, 0xb, 0xf0,
    0xf, 0xef, 0xfc, 0x60, 0x0, 0x0, 0xef, 0xff,
    0xff, 0xff, 0x0, 0xfd, 0x61, 0x0, 0x0, 0x0,
    0xe, 0xb4, 0x44, 0x4c, 0xf0, 0xf, 0xa0, 0x0,
    0x0, 0x45, 0x0, 0xe9, 0x0, 0x0, 0xbf, 0x0,
    0xfa, 0x0, 0x0, 0x7, 0xf0, 0xe, 0x90, 0x0,
    0xb, 0xf0, 0xf, 0xa0, 0x0, 0x0, 0x9e, 0x0,
    0xe9, 0x1, 0x77, 0xde, 0x0, 0xee, 0x99, 0x99,
    0x9f, 0xa0, 0xe, 0x90, 0xe, 0xfe, 0x70, 0x4,
    0xce, 0xee, 0xee, 0xb2, 0x0, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+81F3 "至" */
    0x1, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
    0x11, 0x10, 0x4f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf3, 0x28, 0x88, 0x88, 0xcf,
    0xc8, 0x88, 0x88, 0x88, 0x88, 0x81, 0x0, 0x0,
    0x4, 0xfd, 0x10, 0x0, 0x16, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2e, 0xe1, 0x0, 0x0, 0x7f, 0xb1,
    0x0, 0x0, 0x0, 0x2, 0xee, 0x20, 0x0, 0x0,
    0x4, 0xfd, 0x20, 0x0, 0x0, 0x4e, 0xf5, 0x34,
    0x45, 0x66, 0x77, 0xbf, 0xe3, 0x0, 0x1, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xed, 0xef, 0x40,
    0x0, 0x87, 0x65, 0x43, 0x33, 0x20, 0x0, 0x0,
    0x2e, 0xb0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xe0,
    0x0, 0x0, 0x2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x68,
    0x88, 0x88, 0x8e, 0xf8, 0x88, 0x88, 0x88, 0x0,
    0x0, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0xd, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xd, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x79, 0x99, 0x99, 0x99, 0x9e, 0xf9,
    0x99, 0x99, 0x99, 0x98, 0xbf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe,

    /* U+81F4 "致" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xe0, 0x0, 0x0, 0x0, 0xe, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0xf, 0xb0, 0x0, 0x0,
    0x0, 0x6, 0x78, 0xfd, 0x77, 0x77, 0x60, 0x3f,
    0x70, 0x0, 0x0, 0x0, 0x0, 0x7, 0xf4, 0x5,
    0x30, 0x0, 0x8f, 0x98, 0x88, 0x88, 0x80, 0x0,
    0x1f, 0xb0, 0xc, 0xd0, 0x0, 0xdf, 0xff, 0xff,
    0xff, 0xf0, 0x0, 0xaf, 0x10, 0x2, 0xf7, 0x4,
    0xf9, 0x0, 0x5, 0xf6, 0x0, 0x7, 0xfd, 0xac,
    0xde, 0xff, 0x1b, 0xfb, 0x0, 0x7, 0xf3, 0x0,
    0x9, 0xfe, 0xca, 0x97, 0x6f, 0xaf, 0xff, 0x0,
    0xa, 0xf0, 0x0, 0x1, 0x0, 0x5, 0x60, 0x2,
    0xcf, 0x7f, 0x40, 0xe, 0xc0, 0x0, 0x0, 0x0,
    0xc, 0xe0, 0x1, 0xd9, 0xf, 0x90, 0x2f, 0x80,
    0x0, 0x0, 0x0, 0xc, 0xe0, 0x0, 0x10, 0xb,
    0xe0, 0x7f, 0x40, 0x0, 0x8, 0xff, 0xff, 0xff,
    0xff, 0xa0, 0x5, 0xf6, 0xde, 0x0, 0x0, 0x4,
    0x99, 0x9e, 0xf9, 0x99, 0x60, 0x0, 0xde, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xe0, 0x0, 0x0,
    0x0, 0x6f, 0xf1, 0x0, 0x0, 0x0, 0x0, 0xc,
    0xe0, 0x0, 0x0, 0x0, 0x8f, 0xe1, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xe3, 0x69, 0xc1, 0x6, 0xfd,
    0xfc, 0x0, 0x0, 0x4, 0x69, 0xcf, 0xff, 0xff,
    0xd2, 0x7f, 0xd1, 0x8f, 0xb0, 0x0, 0xf, 0xff,
    0xeb, 0x85, 0x30, 0x2b, 0xfd, 0x10, 0xa, 0xfd,
    0x30, 0x6, 0x41, 0x0, 0x0, 0x1, 0xef, 0xa0,
    0x0, 0x0, 0x8f, 0xe1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x54, 0x0, 0x0, 0x0, 0x3, 0x30,

    /* U+88AB "被" */
    0x0, 0x1, 0x30, 0x0, 0x0, 0x0, 0x0, 0x95,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xad, 0x0, 0x2, 0x33, 0x33,
    0xfa, 0x33, 0x33, 0x10, 0x9, 0x99, 0xa9, 0x95,
    0x9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd0, 0xf,
    0xff, 0xff, 0xfb, 0x9, 0xf5, 0x55, 0xfb, 0x55,
    0x6f, 0x80, 0x0, 0x0, 0x3, 0xf4, 0x9, 0xe0,
    0x0, 0xf9, 0x0, 0x5f, 0x30, 0x0, 0x0, 0xa,
    0xc0, 0x9, 0xe0, 0x0, 0xf9, 0x0, 0xae, 0x0,
    0x0, 0x0, 0x4f, 0x32, 0x19, 0xe0, 0x0, 0xf9,
    0x0, 0x12, 0x0, 0x0, 0x1, 0xea, 0xd, 0xa9,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0x0, 0x0, 0xa,
    0xfe, 0xbc, 0x1a, 0xfe, 0xd8, 0x88, 0x88, 0xfb,
    0x0, 0x0, 0x6f, 0xff, 0xf1, 0xa, 0xd8, 0xe0,
    0x0, 0x4, 0xf6, 0x0, 0x6, 0xfa, 0xf9, 0xf8,
    0xb, 0xb1, 0xf6, 0x0, 0xb, 0xf0, 0x0, 0x1f,
    0xb1, 0xf7, 0x6f, 0x3d, 0xa0, 0xbd, 0x0, 0x3f,
    0x80, 0x0, 0x7, 0x1, 0xf7, 0x8, 0xf, 0x80,
    0x3f, 0x90, 0xde, 0x10, 0x0, 0x0, 0x1, 0xf7,
    0x0, 0x3f, 0x50, 0x9, 0xfc, 0xf5, 0x0, 0x0,
    0x0, 0x1, 0xf7, 0x0, 0x7f, 0x10, 0x0, 0xef,
    0xa0, 0x0, 0x0, 0x0, 0x1, 0xf7, 0x0, 0xdc,
    0x0, 0xa, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x1,
    0xf7, 0x6, 0xf5, 0x4, 0xdf, 0x91, 0xcf, 0xc3,
    0x0, 0x0, 0x1, 0xf7, 0xe, 0xd2, 0xcf, 0xe5,
    0x0, 0x9, 0xff, 0xc0, 0x0, 0x1, 0xf7, 0x5,
    0x40, 0xb7, 0x0, 0x0, 0x0, 0x19, 0x70,

    /* U+89E3 "解" */
    0x0, 0x0, 0x31, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf9, 0x0, 0x0,
    0x2, 0x33, 0x33, 0x33, 0x33, 0x20, 0x0, 0x5,
    0xf9, 0x55, 0x50, 0xa, 0xff, 0xff, 0xff, 0xff,
    0x90, 0x0, 0xb, 0xff, 0xff, 0xf6, 0x1, 0x33,
    0xce, 0x33, 0x3f, 0x80, 0x0, 0x2f, 0x70, 0x9,
    0xe0, 0x0, 0x0, 0xfa, 0x0, 0xf, 0x70, 0x0,
    0xbe, 0x10, 0x1f, 0x70, 0x0, 0x7, 0xf4, 0x0,
    0x1f, 0x60, 0x6, 0xff, 0xdd, 0xef, 0xed, 0x60,
    0x3f, 0xd0, 0x0, 0x5f, 0x40, 0xd, 0xff, 0x56,
    0xf6, 0x5f, 0x78, 0xff, 0x30, 0xce, 0xfe, 0x0,
    0x1, 0x9e, 0x1, 0xf1, 0xe, 0x7a, 0xd3, 0x0,
    0x36, 0x61, 0x0, 0x0, 0x7e, 0x1, 0xf1, 0xe,
    0x70, 0x1c, 0x41, 0xe7, 0x0, 0x0, 0x0, 0x7f,
    0xdd, 0xfd, 0xdf, 0x70, 0x6f, 0x32, 0xf8, 0x11,
    0x0, 0x0, 0x7f, 0x56, 0xf6, 0x5f, 0x70, 0xbf,
    0xff, 0xff, 0xff, 0xa0, 0x0, 0x8e, 0x1, 0xf1,
    0xe, 0x72, 0xf8, 0x66, 0xfb, 0x66, 0x40, 0x0,
    0x8e, 0x1, 0xf1, 0xe, 0x7b, 0xd0, 0x1, 0xf8,
    0x0, 0x0, 0x0, 0x9f, 0xcc, 0xfc, 0xcf, 0x71,
    0x30, 0x1, 0xf8, 0x0, 0x0, 0x0, 0xbd, 0x89,
    0xf8, 0x8f, 0x79, 0xff, 0xff, 0xff, 0xff, 0xf3,
    0x0, 0xd8, 0x1, 0xf1, 0xe, 0x75, 0x88, 0x88,
    0xfc, 0x88, 0x82, 0x0, 0xf5, 0x1, 0xf1, 0xe,
    0x70, 0x0, 0x1, 0xf8, 0x0, 0x0, 0x4, 0xf2,
    0x1, 0xf1, 0xe, 0x70, 0x0, 0x1, 0xf8, 0x0,
    0x0, 0xb, 0xd0, 0x1, 0x95, 0x6f, 0x60, 0x0,
    0x1, 0xf8, 0x0, 0x0, 0xa, 0x60, 0x0, 0x8,
    0xfc, 0x20, 0x0, 0x1, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+8BBE "设" */
    0x0, 0xa, 0x50, 0x0, 0x0, 0x9, 0x99, 0x99,
    0x98, 0x0, 0x0, 0x0, 0xc, 0xf7, 0x0, 0x0,
    0x1f, 0xff, 0xff, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0x70, 0x0, 0x1f, 0x70, 0x0, 0xae, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xd0, 0x0, 0x2f, 0x60,
    0x0, 0xae, 0x0, 0x0, 0x0, 0x0, 0x1, 0x10,
    0x0, 0x6f, 0x40, 0x0, 0xae, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xde, 0x0, 0x0, 0xae,
    0x0, 0x0, 0x9, 0x99, 0x95, 0x0, 0x1b, 0xf6,
    0x0, 0x0, 0x8f, 0xcb, 0xb0, 0xf, 0xff, 0xfa,
    0x3, 0xff, 0x90, 0x0, 0x0, 0x19, 0xaa, 0xa0,
    0x0, 0x0, 0xfa, 0x0, 0x95, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xfa, 0x0, 0x49,
    0x99, 0x99, 0x99, 0x99, 0x95, 0x0, 0x0, 0x0,
    0xfa, 0x0, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0x0, 0x0, 0x0, 0xfa, 0x0, 0x6, 0xf4, 0x0,
    0x0, 0x7, 0xf3, 0x0, 0x0, 0x0, 0xfa, 0x0,
    0x0, 0xdd, 0x0, 0x0, 0x1f, 0xb0, 0x0, 0x0,
    0x0, 0xfa, 0x0, 0x0, 0x4f, 0x90, 0x0, 0xcf,
    0x20, 0x0, 0x0, 0x0, 0xfa, 0x3, 0x20, 0x8,
    0xf7, 0xc, 0xf5, 0x0, 0x0, 0x0, 0x0, 0xfa,
    0x8f, 0x90, 0x0, 0xaf, 0xef, 0x70, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xfa, 0x0, 0x0, 0x7f, 0xfe,
    0x40, 0x0, 0x0, 0x0, 0x7, 0xff, 0x50, 0x1,
    0x7e, 0xfc, 0x7f, 0xfc, 0x50, 0x0, 0x0, 0xb,
    0xd2, 0x2, 0xcf, 0xfd, 0x50, 0x1, 0x9f, 0xff,
    0xb0, 0x0, 0x2, 0x0, 0x0, 0xb8, 0x30, 0x0,
    0x0, 0x1, 0x7c, 0x80,

    /* U+8BEF "误" */
    0x0, 0x2a, 0x0, 0x0, 0x4, 0x77, 0x77, 0x77,
    0x77, 0x74, 0x0, 0x0, 0x5f, 0xc1, 0x0, 0xa,
    0xff, 0xff, 0xff, 0xff, 0xfa, 0x0, 0x0, 0x5,
    0xfd, 0x10, 0xa, 0xe0, 0x0, 0x0, 0x0, 0xfa,
    0x0, 0x0, 0x0, 0x6f, 0x70, 0xa, 0xe0, 0x0,
    0x0, 0x0, 0xfa, 0x0, 0x0, 0x0, 0x5, 0x0,
    0xa, 0xe0, 0x0, 0x0, 0x0, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xff, 0xff, 0xff, 0xff,
    0xfa, 0x0, 0x9, 0x99, 0x96, 0x0, 0x5, 0x88,
    0x88, 0x88, 0x88, 0x85, 0x0, 0xf, 0xff, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xea, 0x0, 0x18, 0x88, 0x88, 0x88,
    0x88, 0x88, 0x20, 0x0, 0x0, 0xea, 0x0, 0x3f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x40, 0x0, 0x0,
    0xea, 0x0, 0x0, 0x0, 0x6, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xea, 0x0, 0x0, 0x0, 0x8,
    0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0xea, 0x0,
    0x78, 0x88, 0x8c, 0xf8, 0x88, 0x88, 0x81, 0x0,
    0x0, 0xea, 0x0, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf1, 0x0, 0x0, 0xea, 0x1, 0x10, 0x0,
    0x3f, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0xea,
    0x6f, 0x60, 0x0, 0xcf, 0x7f, 0x60, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xfa, 0x0, 0x9, 0xf7, 0xb,
    0xf6, 0x0, 0x0, 0x0, 0x7, 0xff, 0x50, 0x3,
    0xcf, 0xa0, 0x0, 0xcf, 0xa2, 0x0, 0x0, 0x3,
    0xc2, 0x3, 0xcf, 0xf7, 0x0, 0x0, 0x9, 0xff,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0xb7, 0x10, 0x0,
    0x0, 0x0, 0x3a, 0x70,

    /* U+8BF7 "请" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4a, 0x20,
    0x0, 0x0, 0x0, 0x3e, 0x40, 0x0, 0x0, 0x0,
    0x6, 0xf4, 0x0, 0x0, 0x0, 0x1, 0xdf, 0x50,
    0x7, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0x1, 0xdf, 0x40, 0x25, 0x55, 0x59, 0xf7, 0x55,
    0x55, 0x20, 0x0, 0x1, 0xe8, 0x0, 0x11, 0x11,
    0x6f, 0x41, 0x11, 0x10, 0x0, 0x0, 0x2, 0x0,
    0xe, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x12, 0x22, 0x7f, 0x52, 0x22,
    0x10, 0x8, 0x99, 0x98, 0x0, 0x55, 0x55, 0x59,
    0xf8, 0x55, 0x55, 0x51, 0xff, 0xff, 0xe0, 0x2e,
    0xee, 0xee, 0xee, 0xee, 0xee, 0xee, 0x30, 0x0,
    0xae, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xe0, 0x0, 0x8f, 0xff, 0xff,
    0xff, 0xff, 0x70, 0x0, 0x0, 0xae, 0x0, 0x9,
    0xf5, 0x55, 0x55, 0x56, 0xf7, 0x0, 0x0, 0xa,
    0xe0, 0x0, 0x9f, 0x0, 0x0, 0x0, 0x1f, 0x70,
    0x0, 0x0, 0xae, 0x0, 0x9, 0xff, 0xff, 0xff,
    0xff, 0xf7, 0x0, 0x0, 0xa, 0xe0, 0x0, 0x9f,
    0x33, 0x33, 0x33, 0x4f, 0x70, 0x0, 0x0, 0xae,
    0x6, 0x59, 0xf0, 0x0, 0x0, 0x1, 0xf7, 0x0,
    0x0, 0xa, 0xfa, 0xf8, 0x9f, 0xff, 0xff, 0xff,
    0xff, 0x70, 0x0, 0x0, 0xbf, 0xf9, 0x9, 0xf4,
    0x44, 0x44, 0x45, 0xf7, 0x0, 0x0, 0x1f, 0xf7,
    0x0, 0x9f, 0x0, 0x0, 0x0, 0x1f, 0x70, 0x0,
    0x7, 0xf5, 0x0, 0x9, 0xf0, 0x0, 0x5, 0x57,
    0xf7, 0x0, 0x0, 0x4, 0x0, 0x0, 0x9f, 0x0,
    0x0, 0xaf, 0xfc, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+8DDD "距" */
    0x1, 0xcc, 0xcc, 0xcc, 0xc0, 0x59, 0x99, 0x99,
    0x99, 0x99, 0x60, 0x1, 0xfc, 0xaa, 0xac, 0xf1,
    0x9f, 0xff, 0xff, 0xff, 0xff, 0xa0, 0x1, 0xf5,
    0x0, 0x6, 0xf1, 0x9f, 0x21, 0x11, 0x11, 0x11,
    0x0, 0x1, 0xf5, 0x0, 0x6, 0xf1, 0x9f, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xf5, 0x0, 0x6,
    0xf1, 0x9f, 0x10, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xf5, 0x0, 0x7, 0xf1, 0x9f, 0x21, 0x11, 0x11,
    0x10, 0x0, 0x1, 0xff, 0xff, 0xff, 0xf1, 0x9f,
    0xff, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x66, 0x6f,
    0xb6, 0x60, 0x9f, 0x77, 0x77, 0x77, 0xf8, 0x0,
    0x0, 0x0, 0xf, 0x80, 0x0, 0x9f, 0x10, 0x0,
    0x0, 0xf8, 0x0, 0x0, 0x61, 0xf, 0x80, 0x0,
    0x9f, 0x10, 0x0, 0x0, 0xf8, 0x0, 0x1, 0xf5,
    0xf, 0xb7, 0x73, 0x9f, 0x10, 0x0, 0x0, 0xf8,
    0x0, 0x1, 0xf5, 0xf, 0xff, 0xf7, 0x9f, 0x10,
    0x0, 0x1, 0xf8, 0x0, 0x1, 0xf5, 0xf, 0x80,
    0x0, 0x9f, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x1,
    0xf5, 0xf, 0x80, 0x0, 0x9f, 0x88, 0x88, 0x88,
    0x84, 0x0, 0x1, 0xf5, 0xf, 0x80, 0x0, 0x9f,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x1, 0xf5, 0xf,
    0xa6, 0xa6, 0x9f, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xf9, 0xaf, 0xff, 0xc5, 0x9f, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x1e, 0xff, 0xea, 0x51, 0x0,
    0x9f, 0xaa, 0xaa, 0xaa, 0xaa, 0xa0, 0xa, 0x72,
    0x0, 0x0, 0x0, 0x8f, 0xff, 0xff, 0xff, 0xff,
    0xf1,

    /* U+8FD8 "还" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xac, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f,
    0xc0, 0xe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x50, 0x0, 0x6, 0xfb, 0x9, 0xaa, 0xaa, 0xac,
    0xfd, 0xaa, 0xaa, 0x30, 0x0, 0x0, 0x8f, 0x40,
    0x0, 0x0, 0xb, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0x0, 0x0, 0x0, 0x5f, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xef, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xff, 0x44, 0x30, 0x0, 0x0,
    0xf, 0xff, 0xf9, 0x0, 0x0, 0x8f, 0xef, 0x5d,
    0xf4, 0x0, 0x0, 0x9, 0x9a, 0xf9, 0x0, 0x8,
    0xfb, 0x6f, 0x41, 0xdf, 0x30, 0x0, 0x0, 0x1,
    0xf9, 0x0, 0x9f, 0xc0, 0x6f, 0x40, 0x2e, 0xf3,
    0x0, 0x0, 0x1, 0xf9, 0x2c, 0xfb, 0x0, 0x6f,
    0x40, 0x2, 0xef, 0x30, 0x0, 0x1, 0xf9, 0x6f,
    0x90, 0x0, 0x6f, 0x40, 0x0, 0x3f, 0x60, 0x0,
    0x1, 0xf9, 0x2, 0x0, 0x0, 0x6f, 0x40, 0x0,
    0x2, 0x0, 0x0, 0x1, 0xf9, 0x0, 0x0, 0x0,
    0x6f, 0x40, 0x0, 0x0, 0x0, 0x0, 0x1, 0xf9,
    0x0, 0x0, 0x0, 0x6f, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xfa, 0x0, 0x0, 0x0, 0x6f, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0x90, 0x0,
    0x0, 0x38, 0x20, 0x0, 0x0, 0x0, 0x5, 0xfb,
    0x6, 0xfc, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x11, 0x2f, 0xe1, 0x0, 0x4e, 0xfe, 0xb9, 0x98,
    0x99, 0xab, 0xde, 0xf1, 0xc, 0x60, 0x0, 0x1,
    0x7c, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x11, 0x11, 0x0,
    0x0, 0x0,

    /* U+9009 "选" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0x50,
    0x0, 0x0, 0x0, 0x1, 0x81, 0x0, 0x0, 0x4,
    0xa2, 0xb, 0xe0, 0x0, 0x0, 0x0, 0x4, 0xfd,
    0x20, 0x0, 0x9, 0xf1, 0xb, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xe1, 0x0, 0x1f, 0xd6, 0x6d,
    0xf6, 0x66, 0x66, 0x0, 0x0, 0x4, 0xfc, 0x0,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0x0,
    0x0, 0x74, 0x3, 0xfb, 0x0, 0xb, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xf2, 0x0,
    0xb, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x10, 0x0, 0xb, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xcc, 0xcc, 0xcf, 0xfc,
    0xcc, 0xcc, 0xb0, 0xc, 0xff, 0xf8, 0xa, 0xbb,
    0xbf, 0xeb, 0xbf, 0xdb, 0xbb, 0xa0, 0x6, 0x88,
    0xf8, 0x0, 0x0, 0x2f, 0x80, 0x1f, 0x90, 0x0,
    0x0, 0x0, 0x1, 0xf8, 0x0, 0x0, 0x5f, 0x50,
    0x1f, 0x90, 0x0, 0x0, 0x0, 0x1, 0xf8, 0x0,
    0x0, 0xbf, 0x10, 0x1f, 0x90, 0x0, 0x0, 0x0,
    0x1, 0xf8, 0x0, 0x4, 0xfa, 0x0, 0x1f, 0x90,
    0x6, 0xc0, 0x0, 0x1, 0xf8, 0x0, 0x4e, 0xe1,
    0x0, 0xf, 0x90, 0x7, 0xf0, 0x0, 0x1, 0xf8,
    0x2a, 0xfe, 0x30, 0x0, 0xf, 0xd9, 0x9e, 0xd0,
    0x0, 0x1, 0xf9, 0x1e, 0xa1, 0x0, 0x0, 0x8,
    0xef, 0xfe, 0x50, 0x0, 0x8, 0xff, 0xa1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0xa5, 0xec, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xf9, 0x0, 0x1c, 0xfd, 0x98, 0x77,
    0x77, 0x89, 0x9b, 0xe1, 0x9, 0xc0, 0x0, 0x0,
    0x5b, 0xef, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x0,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x11, 0x11, 0x10,
    0x0, 0x0,

    /* U+91CF "量" */
    0x0, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf2, 0x0, 0x0, 0x1, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0x20, 0x0, 0x0, 0x1f, 0xed,
    0xdd, 0xdd, 0xdd, 0xdd, 0xde, 0xf2, 0x0, 0x0,
    0x1, 0xf9, 0x22, 0x22, 0x22, 0x22, 0x22, 0x9f,
    0x20, 0x0, 0x0, 0x1f, 0xa3, 0x33, 0x33, 0x33,
    0x33, 0x3a, 0xf2, 0x0, 0x0, 0x0, 0xcc, 0xcc,
    0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0x10, 0x0, 0x34,
    0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0xc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe0, 0x0, 0x1, 0x11, 0x11,
    0x11, 0x11, 0x11, 0x11, 0x10, 0x0, 0x0, 0x7,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0,
    0x0, 0x0, 0x7f, 0x10, 0x0, 0xc, 0xd0, 0x0,
    0x0, 0xec, 0x0, 0x0, 0x7, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xc0, 0x0, 0x0, 0x7f,
    0x10, 0x0, 0xc, 0xd0, 0x0, 0x0, 0xec, 0x0,
    0x0, 0x7, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xc0, 0x0, 0x0, 0x1, 0x11, 0x11, 0x1c,
    0xd1, 0x11, 0x11, 0x10, 0x0, 0x0, 0x4, 0x44,
    0x44, 0x44, 0xdd, 0x44, 0x44, 0x44, 0x41, 0x0,
    0x1, 0xee, 0xee, 0xee, 0xef, 0xfe, 0xee, 0xee,
    0xee, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcd,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x44, 0x44, 0x44,
    0x44, 0x4d, 0xd4, 0x44, 0x44, 0x44, 0x44, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf0,

    /* U+949F "钟" */
    0x0, 0x0, 0x10, 0x0, 0x0, 0x0, 0x0, 0x23,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0x30, 0x0, 0x0,
    0x0, 0xb, 0xf0, 0x0, 0x0, 0x0, 0xc, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0x0, 0x0, 0x0,
    0x5, 0xff, 0xff, 0xff, 0x10, 0x0, 0xb, 0xf0,
    0x0, 0x0, 0x1, 0xed, 0x99, 0x99, 0x90, 0x0,
    0x0, 0xbf, 0x0, 0x0, 0x0, 0xdf, 0x30, 0x0,
    0x0, 0x2d, 0xdd, 0xdf, 0xfd, 0xdd, 0xd7, 0xd,
    0x70, 0x0, 0x0, 0x3, 0xfd, 0xcc, 0xef, 0xcc,
    0xcf, 0x90, 0x28, 0xff, 0xff, 0xfa, 0x3f, 0x40,
    0xb, 0xf0, 0x0, 0xf9, 0x0, 0x47, 0xbf, 0x87,
    0x53, 0xf4, 0x0, 0xbf, 0x0, 0xf, 0x90, 0x0,
    0x7, 0xf1, 0x0, 0x3f, 0x40, 0xb, 0xf0, 0x0,
    0xf9, 0x0, 0x0, 0x7f, 0x10, 0x3, 0xf4, 0x0,
    0xbf, 0x0, 0xf, 0x90, 0x69, 0x9c, 0xf9, 0x99,
    0x4f, 0x40, 0xb, 0xf0, 0x0, 0xf9, 0xa, 0xff,
    0xff, 0xff, 0xf5, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x90, 0x0, 0x7, 0xf1, 0x0, 0x3f, 0xb9, 0x9e,
    0xf9, 0x99, 0xf9, 0x0, 0x0, 0x7f, 0x10, 0x2,
    0xf4, 0x0, 0xbf, 0x0, 0xd, 0x70, 0x0, 0x7,
    0xf1, 0x0, 0x0, 0x0, 0xb, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0x10, 0x60, 0x0, 0x0, 0xbf,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xf6, 0xdf, 0x30,
    0x0, 0xb, 0xf0, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0xfc, 0x40, 0x0, 0x0, 0xbf, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xe5, 0x0, 0x0, 0x0, 0xb, 0xf0,
    0x0, 0x0, 0x0, 0x1, 0x80, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0x0, 0x0, 0x0,

    /* U+9501 "锁" */
    0x0, 0x1, 0x30, 0x0, 0x0, 0x0, 0x0, 0x86,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xf2, 0x0, 0x1,
    0x81, 0x0, 0xea, 0x0, 0x35, 0x0, 0x0, 0xe,
    0xb0, 0x0, 0x1, 0xea, 0x0, 0xea, 0x0, 0xcc,
    0x0, 0x0, 0x7f, 0xff, 0xff, 0xf1, 0x5f, 0x40,
    0xea, 0x4, 0xf4, 0x0, 0x2, 0xfc, 0x88, 0x88,
    0x80, 0xd, 0xc0, 0xea, 0xd, 0xa0, 0x0, 0x1d,
    0xe1, 0x0, 0x0, 0x0, 0x4, 0x60, 0xea, 0x19,
    0x10, 0x0, 0x1e, 0x50, 0x0, 0x0, 0x0, 0x89,
    0x99, 0xfd, 0x99, 0x96, 0x0, 0x1, 0x9f, 0xff,
    0xff, 0x90, 0xef, 0xff, 0xff, 0xff, 0xfa, 0x0,
    0x0, 0x47, 0xcf, 0x77, 0x40, 0xe9, 0x0, 0x0,
    0x0, 0xea, 0x0, 0x0, 0x0, 0x9e, 0x0, 0x0,
    0xe9, 0x0, 0xc7, 0x0, 0xea, 0x0, 0x0, 0x0,
    0x9e, 0x0, 0x0, 0xe9, 0x0, 0xf9, 0x0, 0xea,
    0x0, 0x4, 0x99, 0xdf, 0x99, 0x80, 0xe9, 0x0,
    0xf9, 0x0, 0xea, 0x0, 0x8, 0xff, 0xff, 0xff,
    0xe0, 0xe9, 0x0, 0xf9, 0x0, 0xea, 0x0, 0x0,
    0x0, 0x9e, 0x0, 0x0, 0xe9, 0x0, 0xf8, 0x0,
    0xea, 0x0, 0x0, 0x0, 0x9e, 0x0, 0x0, 0xe9,
    0x2, 0xf7, 0x0, 0xea, 0x0, 0x0, 0x0, 0x9e,
    0x0, 0x0, 0xe9, 0x7, 0xf3, 0x0, 0xea, 0x0,
    0x0, 0x0, 0x9e, 0x2, 0x60, 0xa6, 0x1e, 0xd1,
    0x0, 0x97, 0x0, 0x0, 0x0, 0xae, 0x9f, 0xd0,
    0x2, 0xdf, 0x4e, 0xe7, 0x0, 0x0, 0x0, 0x1,
    0xff, 0xf8, 0x0, 0x7e, 0xf4, 0x2, 0x9f, 0xe6,
    0x0, 0x0, 0xa, 0xfa, 0x10, 0x7f, 0xfb, 0x20,
    0x0, 0x2, 0xbf, 0xb0, 0x0, 0x1, 0x40, 0x0,
    0x29, 0x30, 0x0, 0x0, 0x0, 0x6, 0x30,

    /* U+957F "长" */
    0x0, 0x0, 0x47, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0xbc, 0x20, 0x0, 0x0, 0x0, 0xaf,
    0x10, 0x0, 0x0, 0x0, 0xbf, 0xa0, 0x0, 0x0,
    0x0, 0xa, 0xf1, 0x0, 0x0, 0x2, 0xdf, 0x90,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0x10, 0x0, 0x7,
    0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0xa, 0xf1,
    0x0, 0x5d, 0xfd, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0x11, 0xdf, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xf1, 0x6, 0xa2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x90, 0xab, 0xbb, 0xef, 0xbb, 0xbc, 0xfd, 0xbb,
    0xbb, 0xbb, 0xb6, 0x0, 0x0, 0xa, 0xf1, 0x0,
    0x1f, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0x10, 0x0, 0x9f, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xf1, 0x0, 0x1, 0xfd, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0x10, 0x0,
    0x6, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xf1, 0x0, 0x0, 0xa, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0x10, 0x0, 0x1, 0xb, 0xfc,
    0x20, 0x0, 0x0, 0x0, 0xa, 0xf2, 0x37, 0xbf,
    0x80, 0xa, 0xff, 0x81, 0x0, 0x0, 0x0, 0xdf,
    0xff, 0xfe, 0xa4, 0x0, 0x5, 0xef, 0xfa, 0x0,
    0x0, 0x5f, 0xfb, 0x73, 0x0, 0x0, 0x0, 0x0,
    0x7d, 0x50, 0x0, 0x0, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+95EA "闪" */
    0x0, 0x1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xdc, 0x0, 0x6, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x84, 0x0, 0x5f, 0xb0, 0xc,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf9, 0x0, 0x8,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xf9,
    0x0, 0x0, 0xbf, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xf9, 0x3, 0x10, 0x1a, 0x10, 0x0, 0x63,
    0x0, 0x0, 0x1, 0xf9, 0x1f, 0x90, 0x0, 0x0,
    0x2, 0xfa, 0x0, 0x0, 0x1, 0xf9, 0x1f, 0x90,
    0x0, 0x0, 0x6, 0xf6, 0x0, 0x0, 0x1, 0xf9,
    0x1f, 0x90, 0x0, 0x0, 0xb, 0xf1, 0x0, 0x0,
    0x1, 0xf9, 0x1f, 0x90, 0x0, 0x0, 0x2f, 0xe0,
    0x0, 0x0, 0x1, 0xf9, 0x1f, 0x90, 0x0, 0x0,
    0x9f, 0xfb, 0x0, 0x0, 0x1, 0xf9, 0x1f, 0x90,
    0x0, 0x1, 0xfd, 0x7f, 0xb0, 0x0, 0x1, 0xf9,
    0x1f, 0x90, 0x0, 0xc, 0xf5, 0x8, 0xfa, 0x0,
    0x1, 0xf9, 0x1f, 0x90, 0x0, 0x8f, 0xa0, 0x0,
    0x9f, 0x90, 0x1, 0xf9, 0x1f, 0x90, 0x9, 0xfd,
    0x10, 0x0, 0xc, 0xf6, 0x1, 0xf9, 0x1f, 0x90,
    0xbf, 0xd1, 0x0, 0x0, 0x1, 0xef, 0x1, 0xf9,
    0x1f, 0x90, 0x6b, 0x10, 0x0, 0x0, 0x0, 0x46,
    0x1, 0xf9, 0x1f, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xf9, 0x1f, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xf8, 0x1f, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9a, 0xab, 0xf7,
    0x1f, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0xff, 0xb1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+95ED "闭" */
    0x0, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xf8, 0x0, 0x18, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x80, 0x0, 0xaf, 0x60, 0x3f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0x0, 0xd,
    0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xf1,
    0x0, 0x3, 0x40, 0x0, 0x0, 0x3, 0x50, 0x0,
    0x9, 0xf1, 0x4, 0x20, 0x0, 0x0, 0x0, 0xa,
    0xf0, 0x0, 0x9, 0xf1, 0xf, 0x90, 0x0, 0x0,
    0x0, 0xa, 0xf0, 0x0, 0x9, 0xf1, 0xf, 0x90,
    0x0, 0x0, 0x0, 0xa, 0xf1, 0x0, 0x9, 0xf1,
    0xf, 0x90, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x19, 0xf1, 0xf, 0x90, 0x68, 0x88, 0x88, 0xef,
    0xf8, 0x88, 0x9, 0xf1, 0xf, 0x90, 0x0, 0x0,
    0x5, 0xff, 0xf0, 0x0, 0x9, 0xf1, 0xf, 0x90,
    0x0, 0x0, 0x4f, 0xcb, 0xf0, 0x0, 0x9, 0xf1,
    0xf, 0x90, 0x0, 0x4, 0xfd, 0x1a, 0xf0, 0x0,
    0x9, 0xf1, 0xf, 0x90, 0x0, 0x8f, 0xc1, 0xa,
    0xf0, 0x0, 0x9, 0xf1, 0xf, 0x90, 0x3c, 0xf9,
    0x0, 0xa, 0xf0, 0x0, 0x9, 0xf1, 0xf, 0x96,
    0xfe, 0x50, 0x0, 0xa, 0xf0, 0x0, 0x9, 0xf1,
    0xf, 0x90, 0x50, 0x0, 0x0, 0xb, 0xf0, 0x0,
    0x9, 0xf1, 0xf, 0x90, 0x0, 0x0, 0xce, 0xef,
    0xe0, 0x0, 0x9, 0xf1, 0xf, 0x90, 0x0, 0x0,
    0x5a, 0x97, 0x20, 0x0, 0x9, 0xf1, 0xf, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x28, 0x8d, 0xf0,
    0xf, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xfe, 0x70,

    /* U+97F3 "音" */
    0x0, 0x0, 0x0, 0x0, 0x17, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0x77, 0x77, 0x77,
    0x7c, 0xfa, 0x77, 0x77, 0x77, 0x60, 0x6, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd0,
    0x0, 0x0, 0x3b, 0x50, 0x0, 0x0, 0x1, 0xd8,
    0x0, 0x0, 0x0, 0x0, 0xe, 0xd0, 0x0, 0x0,
    0x6, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x7, 0xf4,
    0x0, 0x0, 0xd, 0xe0, 0x0, 0x0, 0x47, 0x77,
    0x79, 0xfa, 0x77, 0x77, 0x9f, 0xb7, 0x77, 0x76,
    0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0x0,
    0x0, 0xa, 0xf7, 0x77, 0x77, 0x77, 0x77, 0x7c,
    0xf1, 0x0, 0x0, 0xa, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xf1, 0x0, 0x0, 0xa, 0xf2, 0x22,
    0x22, 0x22, 0x22, 0x2a, 0xf1, 0x0, 0x0, 0xa,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0x0,
    0x0, 0xa, 0xf2, 0x22, 0x22, 0x22, 0x22, 0x2a,
    0xf1, 0x0, 0x0, 0xa, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xf1, 0x0, 0x0, 0xa, 0xf7, 0x77,
    0x77, 0x77, 0x77, 0x7c, 0xf1, 0x0, 0x0, 0xa,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0x0,
    0x0, 0xa, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x9,
    0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0
};


/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 79, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 0, .adv_w = 114, .box_w = 3, .box_h = 16, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 24, .adv_w = 167, .box_w = 8, .box_h = 7, .ofs_x = 1, .ofs_y = 10},
    {.bitmap_index = 52, .adv_w = 195, .box_w = 12, .box_h = 16, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 148, .adv_w = 195, .box_w = 10, .box_h = 21, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 253, .adv_w = 324, .box_w = 20, .box_h = 16, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 413, .adv_w = 239, .box_w = 15, .box_h = 16, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 533, .adv_w = 98, .box_w = 4, .box_h = 7, .ofs_x = 1, .ofs_y = 10},
    {.bitmap_index = 547, .adv_w = 119, .box_w = 5, .box_h = 23, .ofs_x = 2, .ofs_y = -5},
    {.bitmap_index = 605, .adv_w = 119, .box_w = 6, .box_h = 23, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 674, .adv_w = 164, .box_w = 8, .box_h = 7, .ofs_x = 1, .ofs_y = 10},
    {.bitmap_index = 702, .adv_w = 195, .box_w = 12, .box_h = 11, .ofs_x = 0, .ofs_y = 3},
    {.bitmap_index = 768, .adv_w = 98, .box_w = 4, .box_h = 8, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 784, .adv_w = 122, .box_w = 6, .box_h = 2, .ofs_x = 1, .ofs_y = 5},
    {.bitmap_index = 790, .adv_w = 98, .box_w = 4, .box_h = 3, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 796, .adv_w = 138, .box_w = 9, .box_h = 21, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 891, .adv_w = 195, .box_w = 11, .box_h = 16, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 979, .adv_w = 195, .box_w = 10, .box_h = 16, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1059, .adv_w = 195, .box_w = 12, .box_h = 16, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1155, .adv_w = 195, .box_w = 11, .box_h = 16, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1243, .adv_w = 195, .box_w = 12, .box_h = 16, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1339, .adv_w = 195, .box_w = 12, .box_h = 16, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1435, .adv_w = 195, .box_w = 11, .box_h = 16, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1523, .adv_w = 195, .box_w = 11, .box_h = 16, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1611, .adv_w = 195, .box_w = 11, .box_h = 16, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1699, .adv_w = 195, .box_w = 12, .box_h = 16, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1795, .adv_w = 98, .box_w = 4, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1819, .adv_w = 98, .box_w = 4, .box_h = 17, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 1853, .adv_w = 195, .box_w = 12, .box_h = 10, .ofs_x = 0, .ofs_y = 3},
    {.bitmap_index = 1913, .adv_w = 195, .box_w = 12, .box_h = 7, .ofs_x = 0, .ofs_y = 5},
    {.bitmap_index = 1955, .adv_w = 195, .box_w = 12, .box_h = 10, .ofs_x = 0, .ofs_y = 3},
    {.bitmap_index = 2015, .adv_w = 167, .box_w = 10, .box_h = 16, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2095, .adv_w = 333, .box_w = 19, .box_h = 20, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 2285, .adv_w = 214, .box_w = 14, .box_h = 16, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2397, .adv_w = 231, .box_w = 12, .box_h = 16, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 2493, .adv_w = 225, .box_w = 13, .box_h = 16, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2597, .adv_w = 242, .box_w = 12, .box_h = 16, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 2693, .adv_w = 207, .box_w = 10, .box_h = 16, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 2773, .adv_w = 194, .box_w = 10, .box_h = 16, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 2853, .adv_w = 243, .box_w = 13, .box_h = 16, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2957, .adv_w = 256, .box_w = 12, .box_h = 16, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 3053, .adv_w = 103, .box_w = 3, .box_h = 16, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 3077, .adv_w = 188, .box_w = 10, .box_h = 16, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3157, .adv_w = 227, .box_w = 13, .box_h = 16, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 3261, .adv_w = 191, .box_w = 10, .box_h = 16, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 3341, .adv_w = 286, .box_w = 14, .box_h = 16, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 3453, .adv_w = 255, .box_w = 12, .box_h = 16, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 3549, .adv_w = 261, .box_w = 15, .box_h = 16, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3669, .adv_w = 223, .box_w = 11, .box_h = 16, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 3757, .adv_w = 261, .box_w = 15, .box_h = 20, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 3907, .adv_w = 223, .box_w = 12, .box_h = 16, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 4003, .adv_w = 210, .box_w = 12, .box_h = 16, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4099, .adv_w = 211, .box_w = 13, .box_h = 16, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4203, .adv_w = 254, .box_w = 12, .box_h = 16, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 4299, .adv_w = 202, .box_w = 13, .box_h = 16, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4403, .adv_w = 309, .box_w = 19, .box_h = 16, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4555, .adv_w = 202, .box_w = 13, .box_h = 16, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4659, .adv_w = 187, .box_w = 13, .box_h = 16, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 4763, .adv_w = 212, .box_w = 12, .box_h = 16, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4859, .adv_w = 119, .box_w = 5, .box_h = 22, .ofs_x = 2, .ofs_y = -5},
    {.bitmap_index = 4914, .adv_w = 138, .box_w = 9, .box_h = 21, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 5009, .adv_w = 119, .box_w = 6, .box_h = 22, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 5075, .adv_w = 195, .box_w = 10, .box_h = 10, .ofs_x = 1, .ofs_y = 7},
    {.bitmap_index = 5125, .adv_w = 197, .box_w = 12, .box_h = 3, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 5143, .adv_w = 213, .box_w = 6, .box_h = 6, .ofs_x = 3, .ofs_y = 13},
    {.bitmap_index = 5161, .adv_w = 198, .box_w = 10, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 5221, .adv_w = 218, .box_w = 11, .box_h = 17, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 5315, .adv_w = 179, .box_w = 10, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 5375, .adv_w = 218, .box_w = 11, .box_h = 17, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 5469, .adv_w = 195, .box_w = 11, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 5535, .adv_w = 114, .box_w = 8, .box_h = 18, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5607, .adv_w = 199, .box_w = 12, .box_h = 17, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 5709, .adv_w = 214, .box_w = 10, .box_h = 17, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 5794, .adv_w = 97, .box_w = 4, .box_h = 17, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 5828, .adv_w = 97, .box_w = 6, .box_h = 22, .ofs_x = -1, .ofs_y = -5},
    {.bitmap_index = 5894, .adv_w = 194, .box_w = 10, .box_h = 17, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 5979, .adv_w = 100, .box_w = 4, .box_h = 17, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 6013, .adv_w = 326, .box_w = 17, .box_h = 12, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 6115, .adv_w = 215, .box_w = 10, .box_h = 12, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 6175, .adv_w = 213, .box_w = 12, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 6247, .adv_w = 218, .box_w = 11, .box_h = 17, .ofs_x = 2, .ofs_y = -5},
    {.bitmap_index = 6341, .adv_w = 218, .box_w = 11, .box_h = 17, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 6435, .adv_w = 137, .box_w = 7, .box_h = 12, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 6477, .adv_w = 165, .box_w = 10, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6537, .adv_w = 133, .box_w = 8, .box_h = 16, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6601, .adv_w = 214, .box_w = 11, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 6667, .adv_w = 183, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6739, .adv_w = 282, .box_w = 18, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6847, .adv_w = 175, .box_w = 11, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6913, .adv_w = 183, .box_w = 12, .box_h = 17, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 7015, .adv_w = 167, .box_w = 10, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7075, .adv_w = 119, .box_w = 7, .box_h = 22, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 7152, .adv_w = 95, .box_w = 2, .box_h = 24, .ofs_x = 2, .ofs_y = -6},
    {.bitmap_index = 7176, .adv_w = 119, .box_w = 7, .box_h = 22, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 7253, .adv_w = 195, .box_w = 12, .box_h = 5, .ofs_x = 0, .ofs_y = 6},
    {.bitmap_index = 7283, .adv_w = 352, .box_w = 21, .box_h = 21, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 7504, .adv_w = 352, .box_w = 22, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 7735, .adv_w = 352, .box_w = 21, .box_h = 22, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 7966, .adv_w = 352, .box_w = 21, .box_h = 20, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 8176, .adv_w = 352, .box_w = 21, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 8397, .adv_w = 352, .box_w = 20, .box_h = 20, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 8597, .adv_w = 352, .box_w = 20, .box_h = 21, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 8807, .adv_w = 352, .box_w = 21, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 9028, .adv_w = 352, .box_w = 20, .box_h = 20, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 9228, .adv_w = 352, .box_w = 20, .box_h = 21, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 9438, .adv_w = 352, .box_w = 22, .box_h = 22, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 9680, .adv_w = 352, .box_w = 21, .box_h = 21, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 9901, .adv_w = 352, .box_w = 22, .box_h = 22, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 10143, .adv_w = 352, .box_w = 20, .box_h = 21, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 10353, .adv_w = 352, .box_w = 21, .box_h = 22, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 10584, .adv_w = 352, .box_w = 22, .box_h = 22, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 10826, .adv_w = 352, .box_w = 22, .box_h = 21, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 11057, .adv_w = 352, .box_w = 21, .box_h = 22, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 11288, .adv_w = 352, .box_w = 22, .box_h = 22, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 11530, .adv_w = 352, .box_w = 21, .box_h = 22, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 11761, .adv_w = 352, .box_w = 22, .box_h = 22, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 12003, .adv_w = 352, .box_w = 21, .box_h = 21, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 12224, .adv_w = 352, .box_w = 22, .box_h = 21, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 12455, .adv_w = 352, .box_w = 22, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 12686, .adv_w = 352, .box_w = 20, .box_h = 22, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 12906, .adv_w = 352, .box_w = 22, .box_h = 21, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 13137, .adv_w = 352, .box_w = 20, .box_h = 18, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 13317, .adv_w = 352, .box_w = 21, .box_h = 21, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 13538, .adv_w = 352, .box_w = 21, .box_h = 22, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 13769, .adv_w = 352, .box_w = 20, .box_h = 21, .ofs_x = 2, .ofs_y = -2},
    {.bitmap_index = 13979, .adv_w = 352, .box_w = 20, .box_h = 22, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 14199, .adv_w = 352, .box_w = 21, .box_h = 21, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 14420, .adv_w = 352, .box_w = 20, .box_h = 20, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 14620, .adv_w = 352, .box_w = 20, .box_h = 21, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 14830, .adv_w = 352, .box_w = 22, .box_h = 21, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 15061, .adv_w = 352, .box_w = 21, .box_h = 22, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 15292, .adv_w = 352, .box_w = 21, .box_h = 22, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 15523, .adv_w = 352, .box_w = 20, .box_h = 19, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 15713, .adv_w = 352, .box_w = 22, .box_h = 21, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 15944, .adv_w = 352, .box_w = 22, .box_h = 21, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 16175, .adv_w = 352, .box_w = 22, .box_h = 22, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 16417, .adv_w = 352, .box_w = 22, .box_h = 20, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 16637, .adv_w = 352, .box_w = 22, .box_h = 20, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 16857, .adv_w = 352, .box_w = 21, .box_h = 22, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 17088, .adv_w = 352, .box_w = 22, .box_h = 19, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 17297, .adv_w = 352, .box_w = 22, .box_h = 22, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 17539, .adv_w = 352, .box_w = 22, .box_h = 22, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 17781, .adv_w = 352, .box_w = 21, .box_h = 20, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 17991, .adv_w = 352, .box_w = 21, .box_h = 21, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 18212, .adv_w = 352, .box_w = 22, .box_h = 21, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 18443, .adv_w = 352, .box_w = 21, .box_h = 21, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 18664, .adv_w = 352, .box_w = 20, .box_h = 22, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 18884, .adv_w = 352, .box_w = 20, .box_h = 21, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 19094, .adv_w = 352, .box_w = 20, .box_h = 22, .ofs_x = 1, .ofs_y = -3}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/

static const uint16_t unicode_list_1[] = {
    0x0, 0x93, 0xaa, 0x1a3, 0x22c, 0x27c, 0x299, 0x2b3,
    0x56b, 0x5b5, 0x794, 0x7b4, 0x858, 0xa89, 0xa93, 0x10cd,
    0x1176, 0x1196, 0x11fb, 0x1232, 0x125d, 0x13c3, 0x13fd, 0x14bc,
    0x1596, 0x1672, 0x19f0, 0x19f2, 0x1d9c, 0x23c2, 0x2511, 0x2598,
    0x27c7, 0x2848, 0x285f, 0x297c, 0x2f8a, 0x3080, 0x3081, 0x3738,
    0x3870, 0x3a4b, 0x3a7c, 0x3a84, 0x3c6a, 0x3e65, 0x3e96, 0x405c,
    0x432c, 0x438e, 0x440c, 0x4477, 0x447a, 0x4680
};

/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] =
{
    {
        .range_start = 32, .range_length = 95, .glyph_id_start = 1,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    },
    {
        .range_start = 20851, .range_length = 18049, .glyph_id_start = 96,
        .unicode_list = unicode_list_1, .glyph_id_ofs_list = NULL, .list_length = 54, .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY
    }
};

/*-----------------
 *    KERNING
 *----------------*/


/*Map glyph_ids to kern left classes*/
static const uint8_t kern_left_class_mapping[] =
{
    0, 0, 0, 1, 0, 0, 0, 0,
    1, 2, 0, 0, 0, 3, 4, 3,
    5, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 6, 6, 0, 0, 0,
    0, 0, 7, 8, 9, 10, 11, 12,
    13, 0, 0, 14, 15, 16, 0, 0,
    10, 17, 10, 18, 19, 20, 21, 22,
    23, 24, 25, 26, 2, 27, 0, 0,
    0, 0, 28, 29, 30, 0, 31, 32,
    33, 34, 0, 0, 35, 36, 34, 34,
    29, 29, 37, 38, 39, 40, 37, 41,
    42, 43, 44, 45, 2, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0
};

/*Map glyph_ids to kern right classes*/
static const uint8_t kern_right_class_mapping[] =
{
    0, 0, 1, 2, 0, 0, 0, 0,
    2, 0, 3, 4, 0, 5, 6, 7,
    8, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 9, 10, 0, 0, 0,
    11, 0, 12, 0, 13, 0, 0, 0,
    13, 0, 0, 14, 0, 0, 0, 0,
    13, 0, 13, 0, 15, 16, 17, 18,
    19, 20, 21, 22, 0, 23, 3, 0,
    0, 0, 24, 0, 25, 25, 25, 26,
    27, 0, 28, 29, 0, 0, 30, 30,
    25, 30, 25, 30, 31, 32, 33, 34,
    35, 36, 37, 38, 0, 0, 3, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0
};

/*Kern values between classes*/
static const int8_t kern_class_values[] =
{
    0, 0, 0, 0, -45, 0, -45, 0,
    0, 0, 0, -21, 0, -37, -4, 0,
    0, 0, 0, -4, 0, 0, 0, 0,
    -12, 0, 0, 0, 0, 0, -8, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -8, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 31, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -38, 0, -54,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -39, -8, -26, -13, 0,
    -36, 0, 0, 0, -5, 0, 0, 0,
    10, 0, 0, -18, 0, -13, -9, 0,
    -8, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -8,
    -7, -19, 0, -7, -4, -10, -26, -8,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -10, 0, -3, 0, -5, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -16, -4, -31, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -9,
    -12, 0, -4, 10, 10, 0, 0, 3,
    -8, 0, 0, 0, 0, 0, 0, 0,
    0, -19, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -10, 0, 0, 0, 0, 0,
    0, 0, 1, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -21, 0, -37,
    0, 0, 0, 0, 0, 0, -10, -2,
    -4, 0, 0, -21, -6, -5, 0, 1,
    -5, -3, -16, 9, 0, -4, 0, 0,
    0, 0, 9, -5, -2, -3, -1, -1,
    -3, 0, 0, 0, 0, -12, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -6,
    -5, -9, 0, -2, -1, -1, -5, -1,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -4, 0, -5, -4, -4, -5, 0,
    0, 0, 0, 0, 0, -10, 0, 0,
    0, 0, 0, 0, -11, -4, -9, -7,
    -5, -1, -1, -1, -3, -4, 0, 0,
    0, 0, -8, 0, 0, 0, 0, -10,
    -4, -5, -4, 0, -5, 0, 0, 0,
    0, -13, 0, 0, 0, -7, 0, 0,
    0, -4, 0, -15, 0, -9, 0, -4,
    -2, -7, -8, -8, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -5, 0, -3, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -4, 0, 0, 0,
    0, 0, 0, -9, 0, -4, 0, -12,
    -4, 0, 0, 0, 0, 0, -29, 0,
    -29, -27, 0, 0, 0, -15, -4, -54,
    -8, 0, 0, 1, 1, -9, 0, -12,
    0, -13, -5, 0, -9, 0, 0, -8,
    -8, -4, -6, -8, -6, -10, -6, -12,
    0, 0, 0, -11, 0, 0, 0, 0,
    0, 0, 0, -1, 0, 0, 0, -8,
    0, -5, -1, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -9, 0, -9, 0, 0, 0,
    0, 0, 0, -16, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -8, 0, -16,
    0, -12, 0, 0, 0, 0, -3, -4,
    -8, 0, -3, -7, -5, -5, -4, 0,
    -6, 0, 0, 0, -3, 0, 0, 0,
    -4, 0, 0, -13, -6, -8, -6, -6,
    -8, -5, 0, -35, 0, -60, 0, -21,
    0, 0, 0, 0, -13, 1, -10, 0,
    -9, -47, -11, -30, -22, 0, -30, 0,
    -31, 0, -5, -5, -1, 0, 0, 0,
    0, -8, -4, -14, -13, 0, -14, 0,
    0, 0, 0, 0, -44, -13, -44, -30,
    0, 0, 0, -20, 0, -58, -4, -9,
    0, 0, 0, -9, -4, -31, 0, -17,
    -9, 0, -12, 0, 0, 0, -4, 0,
    0, 0, 0, -5, 0, -8, 0, 0,
    0, -4, 0, -12, 0, 0, 0, 0,
    0, -1, 0, -7, -5, -5, 0, 2,
    2, -1, 0, -4, 0, -1, -4, 0,
    -1, 0, 0, 0, 0, 0, 0, 0,
    0, -3, 0, -3, 0, 0, 0, -6,
    0, 5, 0, 0, 0, 0, 0, 0,
    0, -5, -5, -8, 0, 0, 0, 0,
    -5, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -9, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -42, -29,
    -42, -35, -8, -8, 0, -16, -9, -50,
    -15, 0, 0, 0, 0, -8, -5, -21,
    0, -29, -26, -7, -29, 0, 0, -18,
    -23, -7, -18, -13, -13, -15, -13, -30,
    0, 0, 0, 0, -6, 0, -6, -12,
    0, 0, 0, -6, 0, -19, -4, 0,
    0, -1, 0, -4, -5, 0, 0, -1,
    0, 0, -4, 0, 0, 0, -1, 0,
    0, 0, 0, -3, 0, 0, 0, 0,
    0, 0, -26, -7, -26, -18, 0, 0,
    0, -5, -4, -29, -4, 0, -4, 4,
    0, 0, 0, -7, 0, -9, -6, 0,
    -8, 0, 0, -8, -5, 0, -12, -3,
    -3, -6, -3, -10, 0, 0, 0, 0,
    -13, -4, -13, -12, 0, 0, 0, 0,
    -2, -26, -2, 0, 0, 0, 0, 0,
    0, -2, 0, -6, 0, 0, -5, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -4, 0, -4, 0, -4, 0, -10,
    0, 0, 0, 0, 0, 1, -7, 0,
    -5, -8, -4, 0, 0, 0, 0, 0,
    0, -4, -3, -6, 0, 0, 0, 0,
    0, -6, -4, -6, -5, -4, -6, -5,
    0, 0, 0, 0, -36, -26, -36, -26,
    -10, -10, -3, -5, -5, -39, -6, -5,
    -4, 0, 0, 0, 0, -10, 0, -26,
    -16, 0, -24, 0, 0, -16, -16, -11,
    -13, -5, -9, -13, -5, -19, 0, 0,
    0, 0, 0, -13, 0, 0, 0, 0,
    0, -2, -8, -13, -12, 0, -4, -2,
    -2, 0, -5, -6, 0, -6, -8, -8,
    -6, 0, 0, 0, 0, -5, -9, -6,
    -6, -9, -6, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -34, -12, -21, -12, 0,
    -29, 0, 0, 0, 0, 0, 13, 0,
    29, 0, 0, 0, 0, -8, -4, 0,
    5, 0, 0, 0, 0, -21, 0, 0,
    0, 0, 0, 0, -5, 0, 0, 0,
    0, -9, 0, -6, -1, 0, -9, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -5, 0, 0, 0, 0, 0, 0,
    0, -12, 0, -10, -4, 2, -4, 0,
    0, 0, -5, 0, 0, 0, 0, -23,
    0, -7, 0, -1, -18, 0, -10, -6,
    0, 0, 0, 0, 0, 0, 0, -7,
    0, -1, -1, -7, -1, -2, 0, 0,
    0, 0, 0, -8, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -8, 0, -5,
    0, 0, -9, 0, 0, -4, -8, 0,
    -4, 0, 0, 0, 0, -4, 0, 2,
    2, 3, 2, 0, 0, 0, 0, -13,
    0, 4, 0, 0, 0, 0, -3, 0,
    0, -8, -8, -9, 0, -6, -4, 0,
    -10, 0, -8, -6, 0, 0, -4, 0,
    0, 0, 0, -4, 0, 2, 2, -3,
    2, 0, 6, 16, 19, 0, -20, -5,
    -20, -6, 0, 0, 10, 0, 0, 0,
    0, 18, 0, 26, 18, 13, 23, 0,
    25, -8, -4, 0, -6, 0, -4, 0,
    -1, 0, 0, 5, 0, -1, 0, -5,
    0, 0, 6, -13, 0, 0, 0, 19,
    0, 0, -14, 0, 0, 0, 0, -10,
    0, 0, 0, 0, -5, 0, 0, -6,
    -5, 0, 0, 0, 14, 0, 0, 0,
    0, -1, -1, 0, 6, -5, 0, 0,
    0, -13, 0, 0, 0, 0, 0, 0,
    -3, 0, 0, 0, 0, -9, 0, -4,
    0, 0, -6, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -8,
    5, -17, 5, 0, 5, 5, -5, 0,
    0, 0, 0, -14, 0, 0, 0, 0,
    -5, 0, 0, -4, -7, 0, -4, 0,
    -4, 0, 0, -8, -5, 0, 0, -3,
    0, -3, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 3, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -9, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -8,
    0, -5, 0, 0, -12, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -22, -9, -22, -13, 10, 10,
    0, -5, 0, -21, 0, 0, 0, 0,
    0, 0, 0, -4, 5, -9, -4, 0,
    -4, 0, 0, 0, -1, 0, 0, 10,
    7, 0, 10, -1, 0, 0, 0, -19,
    0, 4, 0, 0, 0, 0, -4, 0,
    0, 0, 0, -9, 0, -4, 0, 0,
    -8, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -8, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 2, -10,
    2, 4, 6, 6, -10, 0, 0, 0,
    0, -5, 0, 0, 0, 0, -1, 0,
    0, -8, -5, 0, -4, 0, 0, 0,
    -4, -8, 0, 0, 0, -6, 0, 0,
    0, 0, 0, -5, -13, -3, -13, -8,
    0, 0, 0, -5, 0, -16, 0, -8,
    0, -3, 0, 0, -5, -4, 0, -8,
    -1, 0, 0, 0, -4, 0, 0, 0,
    0, 0, 0, 0, 0, -9, 0, 0,
    0, -5, -16, 0, -16, -3, 0, 0,
    0, -1, 0, -12, 0, -9, 0, -3,
    0, -5, -9, 0, 0, -4, -1, 0,
    0, 0, -4, 0, 0, 0, 0, 0,
    0, 0, 0, -7, -5, 0, 0, -9,
    3, -5, -3, 0, 0, 3, 0, 0,
    -4, 0, -1, -13, 0, -6, 0, -4,
    -13, 0, 0, -4, -7, 0, 0, 0,
    0, 0, 0, -9, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -13, 0,
    -13, -6, 0, 0, 0, 0, 0, -16,
    0, -8, 0, -1, 0, -1, -3, 0,
    0, -8, -1, 0, 0, 0, -4, 0,
    0, 0, 0, 0, 0, -5, 0, -9,
    0, 0, 0, 0, 0, -6, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -10,
    0, 0, 0, 0, -12, 0, 0, -9,
    -4, 0, -2, 0, 0, 0, 0, 0,
    -4, -1, 0, 0, -1, 0
};


/*Collect the kern class' data in one place*/
static const lv_font_fmt_txt_kern_classes_t kern_classes =
{
    .class_pair_values   = kern_class_values,
    .left_class_mapping  = kern_left_class_mapping,
    .right_class_mapping = kern_right_class_mapping,
    .left_class_cnt      = 45,
    .right_class_cnt     = 38,
};

/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LVGL_VERSION_MAJOR == 8
/*Store all the custom data of the font*/
static  lv_font_fmt_txt_glyph_cache_t cache;
#endif

#if LVGL_VERSION_MAJOR >= 8
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = &kern_classes,
    .kern_scale = 16,
    .cmap_num = 2,
    .bpp = 4,
    .kern_classes = 1,
    .bitmap_format = 0,
#if LVGL_VERSION_MAJOR == 8
    .cache = &cache
#endif
};



/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LVGL_VERSION_MAJOR >= 8
const lv_font_t font_source_han_sans_regular_22 = {
#else
lv_font_t font_source_han_sans_regular_22 = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 25,          /*The maximum line height required by the font*/
    .base_line = 6,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = -3,
    .underline_thickness = 1,
#endif
    .dsc = &font_dsc,          /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
#if LV_VERSION_CHECK(8, 2, 0) || LVGL_VERSION_MAJOR >= 9
    .fallback = NULL,
#endif
    .user_data = NULL,
};



#endif /*#if FONT_SOURCE_HAN_SANS_REGULAR_22*/

