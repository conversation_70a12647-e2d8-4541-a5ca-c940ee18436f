//
// Created by <PERSON><PERSON> on 2024/6/15.
//

#include "page_control_center.h"
#include "comp_menu_option.h"
#include "comp_bar.h"
#include "comp_border_focused.h"
#include "multi_language.h"

lv_obj_t *drag_control_center_bottom;

OptionStruct ctrl_beep = {setting_beep, "", &icon_vol_off, false, opt_ctrl_center};
OptionStruct ctrl_lock = {setting_lock, "", &icon_unlock, false, opt_ctrl_center};
OptionStruct ctrl_lamp = {setting_lamp, "", &icon_lamp, false, opt_ctrl_center};
OptionStruct ctrl_tcm = {setting_tcm, "TCM", NULL, false, opt_ctrl_center};

// 跳转设置页回调
static void setting_enter_cb(lv_event_t *e) {
    // lv_obj_t *tar_obj = lv_event_get_target(e);
    lv_event_code_t code = lv_event_get_code(e);
    static bool is_short_clicked;
    static uint16_t count_pressing;
    if (code == LV_EVENT_PRESSING) {
        count_pressing++;
        if (count_pressing > Short_Click_Sensitivity) {
            is_short_clicked = false;
        } else {
            is_short_clicked = true;
        }
    } else if (code == LV_EVENT_RELEASED) {
        count_pressing = 0;
    } else if (code == LV_EVENT_SHORT_CLICKED || code == LV_EVENT_KEY) {
        if (code == LV_EVENT_KEY) {
            char c = *((char *) lv_event_get_param(e));
            if (c == LV_KEY_ENTER) {
                is_short_clicked = true;
            } else if (c == LV_KEY_HOME || c == LV_KEY_END) {
                page_click_anim(pos_bottom, &Pages.page[Page_Home], anim_overlay);
            } else {
                return;
            }
        }
        if (is_short_clicked) {
            // 创建页面
            pm_creat_page(PageManager.main_page->pos[pos_right].page, PageManager.main_page, LV_ALIGN_OUT_RIGHT_MID,
                          lv_color_black());
            // lv_obj_update_layout(Param.grp_tile_view);
            // Anim.is_finished_anim = false;

            // 先记录当前页面的overlay_page
            Pages.page[Page_Setting].overlay_page = PageManager.main_page->overlay_page;
            Pages.page[Page_Setting].overlay_pos = pos_bottom;
            page_click_anim(pos_right, &Pages.page[Page_Setting], anim_slide);
            // Pages.Page_Setting.is_overlay_active = true;
        }
    }
}

void control_center_layout(lv_obj_t *parent) {
    const lv_coord_t sub_comp_spacing = 8;
    lv_obj_t *h_layout = lv_layout_custom_create(parent, LV_FLEX_FLOW_ROW_WRAP);
    // lv_obj_set_style_pad_top(h_layout, status_bar_h + 10, 0);
    lv_obj_set_style_pad_gap(h_layout, sub_comp_spacing, 0);
    lv_obj_set_style_pad_hor(h_layout, 18, 0);
    lv_obj_set_size(h_layout, LV_PCT(100), LV_SIZE_CONTENT);
    lv_obj_set_style_bg_color(h_layout, lv_color_hex(0x000000), 0);

    comp_menu_option_init(h_layout, &ctrl_beep, &Pages.page[Page_Control_Center]);
    comp_menu_option_init(h_layout, &ctrl_lock, &Pages.page[Page_Control_Center]);
    comp_menu_option_init(h_layout, &ctrl_lamp, &Pages.page[Page_Control_Center]);
    comp_menu_option_init(h_layout, &ctrl_tcm, &Pages.page[Page_Control_Center]);

    lv_obj_t *setting_enter_box = lv_obj_create(h_layout);
    lv_obj_add_style(setting_enter_box, &PageManager.default_style, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_size(setting_enter_box, LV_PCT(100), 114);
    lv_obj_set_style_bg_opa(setting_enter_box, LV_OPA_TRANSP, 0);
    lv_obj_set_style_pad_top(setting_enter_box, sub_comp_spacing, 0);

    lv_obj_t *setting_enter = lv_layout_custom_create(setting_enter_box, LV_FLEX_FLOW_ROW);
    lv_obj_set_size(setting_enter, LV_PCT(100), LV_PCT(100));
    lv_obj_set_style_bg_color(setting_enter, lv_color_hex(Bg_Color_Black), 0);
    lv_obj_set_style_radius(setting_enter, RADIUS_DEFAULT, 0);
    lv_obj_add_event_cb(setting_enter, setting_enter_cb, LV_EVENT_ALL, NULL);
    border_focused_obj_init(setting_enter, &Pages.page[Page_Control_Center], Focused_Opr_Parent);
    lv_obj_t *img = lv_img_custom_create(setting_enter, &icon_setting);
    // lv_obj_set_style_bg_color(img, lv_color_hex(Main_Color),0);
    // lv_obj_set_style_bg_opa(img,LV_OPA_COVER,0);
    // lv_obj_center(img);
    lv_obj_t *label = lv_label_custom_create(setting_enter, get_text(text_menu), FONT_MENU_OPTION, lv_color_white(),
                                             LV_TEXT_ALIGN_CENTER);
    // lv_obj_set_style_bg_color(label, lv_color_hex(Main_Color),0);
    // lv_obj_set_style_bg_opa(label,LV_OPA_COVER,0);
    //lv_coord_t text_height = lv_font_get_line_height(FONT_MENU_OPTION);
    //lv_coord_t text_width = lv_txt_get_width("MENU", strlen("MENU"), FONT_MENU_OPTION, 0, LV_TEXT_FLAG_EXPAND);
    //lv_obj_set_size(label,text_width,text_height);
    lv_obj_set_flex_align(setting_enter, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    // lv_obj_set_style_pad_top(setting_enter,sub_comp_spacing*2,0);
}

void page_control_center_init(lv_obj_t *page) {
    control_center_layout(page);

    drag_control_center_bottom = pm_create_handle(page, pos_bottom);

    // 底部手柄视觉对象
    bottom_bar_init(page);
}