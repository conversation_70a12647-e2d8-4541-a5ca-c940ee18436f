//
// Created by <PERSON><PERSON> on 2024/6/15.
//

#include <stdio.h>
// #include <malloc.h>
#include "page_manager_anim_mode.h"
#include "comp_status_bar.h"
#include "page_manager.h"

#define ANIM_TIME_LIMIT 150

AnimStructType Anim;

static bool drag_touched;
static bool had_last_point;

static lv_coord_t page_offset_y;
static lv_coord_t page_offset_x;

// 打印主页
void printf_main_page() {
    if (PageManager.main_page == &Pages.page[Page_Home]) {
        LV_LOG("main page is page home\n");
    } else if (PageManager.main_page == &Pages.page[Page_Control_Center]) {
        LV_LOG("main page is page Control_Center\n");
    } else if (PageManager.main_page == &Pages.page[Page_Setting]) {
        LV_LOG("main page is page Page_Setting\n");
    } else if (PageManager.main_page == &Pages.page[Page_Setting_Detail]) {
        LV_LOG("main page is page Page_Setting_Detail\n");
    } else if (PageManager.main_page == &Pages.page[Page_Multi]) {
        LV_LOG("main page is page Page_Multi\n");
    }
}

// 动画完成后的回调函数
static void overlay_ready_cb(lv_anim_t *a) {
    Anim.is_finished_anim = true;
    lv_obj_add_flag(Anim.mask, LV_OBJ_FLAG_HIDDEN);
    if (lv_obj_has_flag(Anim.mask, LV_OBJ_FLAG_HIDDEN)) {
        LV_LOG("has LV_OBJ_FLAG_HIDDEN\n");
    } else {
        LV_LOG("has not LV_OBJ_FLAG_HIDDEN\n");
    }
    if (Anim.can_be_deleted && Anim.source_page->obj != NULL) {
        Anim.source_page->state = page_valid;
        lv_obj_del_async(Anim.source_page->obj);
        Anim.source_page->obj = NULL;
        Anim.can_be_deleted = false;
        if (Anim.source_page == &Pages.page[Page_Home]) {
            LV_LOG("Delete Page is page Home\n");
        } else if (Anim.source_page == &Pages.page[Page_Control_Center]) {
            LV_LOG("Delete Page is page Control_Center\n");
        } else if (Anim.source_page == &Pages.page[Page_Setting]) {
            LV_LOG("Delete Page is page Page_Setting\n");
        } else if (Anim.source_page == &Pages.page[Page_Setting_Detail]) {
            LV_LOG("Delete Page is page Page_Setting_Detail\n");
        }
        // 清空组obj指针数组
        for (int i = 0; i < GROUP_OBJ_MAX; i++) {
            if (Anim.source_page->group_obj[i] != NULL) {
                // 将指针设置为NULL
                Anim.source_page->group_obj[i] = NULL;
            }
        }
        // // 避免如multi页内的roller在切换页面前已经创建出来，并添加在了默认组内，进而出现focus状态
        // lv_group_del(Anim.source_page->indev_group);
        // Anim.source_page->indev_group = NULL;
    }
    if (!PageManager.main_page->is_overlay_active) {
        // LV_LOG("overlay_ready_cb set_status_bar_parent\n");
        // 修改通知栏父级对象
        lv_obj_set_parent(status_bar, lv_scr_act());
        update_status_bar();
    }
    // // 避免如multi页内的roller在切换页面前已经创建出来，并添加在了默认组内，进而出现focus状态
    // lv_group_del(Anim.source_page->indev_group);
    // Anim.source_page->indev_group = NULL;

    LV_LOG("overlay finished\n");
    // TODO 异常：动画未完成被按键打断
}

// slide 回弹
// 松手未切换页面，删除页面回调
// 避免点了一下手柄把页面创建了，但未切换页面，导致后续切换时页面不正常
static void slide_source_ready_cb(lv_anim_t *a) {
    Anim.is_finished_anim = true;
    lv_obj_add_flag(Anim.mask, LV_OBJ_FLAG_HIDDEN);

    if (lv_obj_is_valid(Anim.target_page->obj)) {
        Anim.target_page->state = page_valid;
        lv_obj_del_async(Anim.target_page->obj);
        Anim.target_page->obj = NULL;
        // 清空组obj指针数组
        for (int i = 0; i < GROUP_OBJ_MAX; i++) {
            if (Anim.target_page->group_obj[i] != NULL) {
                // 将指针设置为NULL
                Anim.target_page->group_obj[i] = NULL;
            }
        }
        lv_group_remove_all_objs(Anim.target_page->indev_group);
        lv_group_del(Anim.target_page->indev_group);
        Anim.target_page->indev_group = NULL;
    }

}

// 删除页面回调
static void slide_ready_cb(lv_anim_t *a) {
    Anim.source_page->is_overlay_active = false;
    // LV_LOG("lv_obj_get_x %d\n", lv_obj_get_x(Anim.source_page->obj));
    // LV_LOG("lv_obj_get_y %d\n", lv_obj_get_y(Anim.source_page->obj));
    // LV_LOG("lv_obj_get_x %d\n", lv_obj_get_x(Anim.target_page->obj));
    // LV_LOG("lv_obj_get_y %d\n", lv_obj_get_y(Anim.target_page->obj));
    if (lv_obj_is_valid(Anim.source_page->obj)) {
        Anim.source_page->state = page_valid;
        lv_obj_del_async(Anim.source_page->obj);
        Anim.source_page->obj = NULL;
        // 清空组obj指针数组
        for (int i = 0; i < GROUP_OBJ_MAX; i++) {
            if (Anim.source_page->group_obj[i] != NULL) {
                // 将指针设置为NULL
                Anim.source_page->group_obj[i] = NULL;
            }
        }
        lv_group_remove_all_objs(Anim.source_page->indev_group);
        // 避免如multi页内的roller在切换页面前已经创建出来，并添加在了默认组内，进而出现focus状态
        lv_group_del(Anim.source_page->indev_group);
        Anim.source_page->indev_group = NULL;
    }
    // if (Anim.source_page == &Pages.page[Page_Group_Switch] && Anim.target_page == &Pages.page[Page_Home]) {
    if (Anim.target_page == &Pages.page[Page_Home]) {
        lv_obj_set_parent(status_bar, lv_scr_act());
    }
    Anim.is_finished_anim = true;
    lv_obj_add_flag(Anim.mask, LV_OBJ_FLAG_HIDDEN);

    LV_LOG("slide finished\n");
    // TODO group
    // lv_group_focus_obj(NULL);
    // lv_obj_t *focused_obj = lv_group_get_focused(lv_group_get_default());
    // if (focused_obj != NULL) {
    //     LV_LOG("---------------%d\n", lv_obj_get_state(focused_obj));
    // } else {
    //     LV_LOG("---------------NULL\n");
    // }
}

// TODO 仅完成pull_left
// 点击平推动画
void page_click_anim(PositionEnum handle_pos, PageTypeHandle *page, uint8_t anim_mode) {
    if (!Anim.is_finished_anim) {
        return;
    }
    LV_LOG("page_click_anim\n");

    Anim.is_finished_anim = false;
    int32_t target_page_end_pos = 0, source_page_end_pos = 0;
    uint32_t target_page_anim_duration = 0, source_page_anim_duration = 0;
    lv_obj_t *target_page = page->obj;
    lv_obj_t *source_page = PageManager.main_page->obj;
    Anim.source_page = PageManager.main_page;
    Anim.target_page = page;
    target_page_end_pos = 0;
    if (handle_pos == pos_right) {
        source_page_end_pos = -lv_obj_get_width(target_page);
    } else if (handle_pos == pos_left) {
        source_page_end_pos = lv_obj_get_width(target_page);
    } else if (handle_pos == pos_top) {
        source_page_end_pos = lv_obj_get_height(source_page);
    } else if (handle_pos == pos_bottom) {
        source_page_end_pos = -lv_obj_get_height(source_page);
    }
    lv_anim_t target_page_anim;
    lv_anim_t source_page_anim;
    lv_anim_init(&target_page_anim);
    lv_anim_init(&source_page_anim);
    lv_anim_set_var(&target_page_anim, target_page);
    lv_anim_set_var(&source_page_anim, source_page);

    LV_LOG("PageManager.main_page->overlay_pos: %d\n", PageManager.main_page->overlay_pos);

    bool overlay_flag = false;
    if (PageManager.main_page->is_overlay_active && anim_mode == anim_overlay &&
        handle_pos == PageManager.main_page->overlay_pos) {
        target_page = source_page;
        target_page_anim = source_page_anim;
        lv_anim_set_var(&target_page_anim, target_page);
        overlay_flag = true;
    }

    if (anim_mode == anim_overlay) {
        if (overlay_flag) {
            target_page_end_pos = source_page_end_pos;
            PageManager.main_page->is_overlay_active = false;
            pm_set_main_page(PageManager.main_page->overlay_page);
            Anim.can_be_deleted = true;
        } else {
            if (!Anim.source_page->is_overlay_active) {
                lv_obj_set_parent(status_bar, source_page);
            }
            target_page_end_pos = 0;
            Anim.target_page->overlay_page = PageManager.main_page;
            pm_set_main_page(Anim.target_page);
            // 记录目标页的回推手柄位置
            switch (handle_pos) {
                case pos_top:
                    PageManager.main_page->overlay_pos = pos_bottom;
                    break;
                case pos_bottom:
                    PageManager.main_page->overlay_pos = pos_top;
                    break;
                case pos_left:
                    PageManager.main_page->overlay_pos = pos_right;
                    break;
                case pos_right:
                    PageManager.main_page->overlay_pos = pos_left;
                    break;
                default:
                    break;
            }
            PageManager.main_page->is_overlay_active = true;
        }
        lv_anim_set_ready_cb(&target_page_anim, overlay_ready_cb);
    }
    if (handle_pos == pos_right || handle_pos == pos_left) {
        target_page_anim_duration = LV_ABS(target_page_end_pos - lv_obj_get_x(target_page));
        source_page_anim_duration = LV_ABS(source_page_end_pos - lv_obj_get_x(source_page));
        lv_anim_set_values(&target_page_anim, lv_obj_get_x(target_page), target_page_end_pos);
        lv_anim_set_values(&source_page_anim, lv_obj_get_x(source_page), source_page_end_pos);
        lv_anim_set_exec_cb(&target_page_anim, (lv_anim_exec_xcb_t) lv_obj_set_x);
        lv_anim_set_exec_cb(&source_page_anim, (lv_anim_exec_xcb_t) lv_obj_set_x);
    } else if (handle_pos == pos_top || handle_pos == pos_bottom) {
        target_page_anim_duration = LV_ABS(target_page_end_pos - lv_obj_get_y(target_page));
        source_page_anim_duration = LV_ABS(source_page_end_pos - lv_obj_get_y(source_page));
        lv_anim_set_values(&target_page_anim, lv_obj_get_y(target_page), target_page_end_pos);
        lv_anim_set_values(&source_page_anim, lv_obj_get_y(source_page), source_page_end_pos);
        lv_anim_set_exec_cb(&target_page_anim, (lv_anim_exec_xcb_t) lv_obj_set_y);
        lv_anim_set_exec_cb(&source_page_anim, (lv_anim_exec_xcb_t) lv_obj_set_y);
    }
    if (target_page_anim_duration > ANIM_TIME_LIMIT) {
        target_page_anim_duration = ANIM_TIME_LIMIT;
    }
    if (source_page_anim_duration > ANIM_TIME_LIMIT) {
        source_page_anim_duration = ANIM_TIME_LIMIT;
    }
    lv_anim_set_time(&target_page_anim, target_page_anim_duration);
    lv_anim_set_time(&source_page_anim, source_page_anim_duration);
    if (anim_mode == anim_slide) {
        pm_set_main_page(page);
        lv_anim_set_ready_cb(&target_page_anim, slide_ready_cb);
        lv_anim_start(&source_page_anim);
        // 若设置了始终覆盖状态，则也将状态设置回 true
        if (PageManager.main_page->is_always_overlay) {
            PageManager.main_page->is_overlay_active = true;
        }
    }
    lv_obj_clear_flag(Anim.mask, LV_OBJ_FLAG_HIDDEN);
    lv_anim_start(&target_page_anim);
    printf_main_page();
}

// 手柄按住回调
// 每拖一下进一次
static void page_swipe_pressing_cb(lv_event_t *e) {
    // 避免editing状态下切换页面导致的死机
    // 原因：
    // 对象已经被删除，但通过LV_EVENT_FOCUSED进入到了enter_key_event_cb内，
    // 在lv_event_send时死机，lv_obj_is_valid(target_obj->parent)判断结果并不为false
    // 于是通过取消editing状态来避免此问题
    if (lv_group_get_editing(lv_group_get_default())) {
        lv_group_set_editing(lv_group_get_default(), false);
    }

    if (!Anim.is_finished_anim) {
        LV_LOG("page_swipe_pressing_cb return\n");
        return;
    }
    // LV_LOG("pressing_cb\n");

    lv_obj_t *tar_obj = lv_event_get_target(e);
    HandleStructType *handle_struct = lv_obj_get_user_data(tar_obj);
    // 获取触发事件的对象
    // lv_obj_t *handle_obj = lv_event_get_target(e);
    // 获取触发事件的对象的父对象
    // lv_obj_t * screen = lv_obj_get_parent(handle_obj);
    // lv_obj_t *target_page = PageManager.main_page->up.page->obj;
    if (handle_struct->target_page == NULL) {
        LV_LOG("target_page is NULL\n");
        return;
    }

    // 手柄目标页
    lv_obj_t *target_page = handle_struct->target_page->obj;
    // 手柄来源页
    lv_obj_t *source_page = PageManager.main_page->obj;
    // 目标页对齐位置
    lv_align_t target_page_align;
    // 当前动画模式
    AnimModeEnum anim_mode = PageManager.main_page->pos[handle_struct->position].anim_mode;

    // 用于判断目标页是否在指定位置开始拖动
    // true则需要校正位置，false就可以拖动
    bool check_target_page_pos;
    // 限制范围
    lv_coord_t source_page_limit_pos;

    // 滑动方向
    SlideDirectionEnumType slide_direction = slide_none;
    if (handle_struct->position <= pos_bottom) {
        slide_direction = slide_vertical;
    } else if (handle_struct->position <= pos_right) {
        slide_direction = slide_horizontal;
    }
    switch (handle_struct->position) {
        case pos_top:
            target_page_align = LV_ALIGN_OUT_TOP_MID;
            break;
        case pos_bottom:
            target_page_align = LV_ALIGN_OUT_BOTTOM_MID;
            break;
        case pos_left:
            target_page_align = LV_ALIGN_OUT_LEFT_MID;
            break;
        case pos_right:
            target_page_align = LV_ALIGN_OUT_RIGHT_MID;
            break;
        default:
            break;
    }

    // 存储点的位置
    static lv_point_t click_point, last_click_point;

    // 触摸设备
    lv_indev_t *indev_touch = lv_indev_get_act();
    // 第一次按下记录坐标
    if (drag_touched == false) {
        lv_indev_get_point(indev_touch, &click_point);
        // 将 target_page 移动到最前
        // lv_obj_move_foreground(target_page);
        drag_touched = true;
        // 记录完第一次的值就出去
        LV_LOG("drag_touched return\n");
        LV_LOG("-----------------1-----------------\n");
        LV_LOG("click_point x: %d\n", click_point.x);
        LV_LOG("last_click_point x: %d\n", last_click_point.x);
        had_last_point = false;
        return;
    } else {
        // 第二次按下记录坐标
        lv_indev_get_point(indev_touch, &last_click_point);
        had_last_point = true;
        LV_LOG("-----------------2-----------------\n");
        LV_LOG("click_point x: %d\n", click_point.x);
        LV_LOG("last_click_point x: %d\n", last_click_point.x);
    }

    // 计算2次坐标的差值
    page_offset_y = last_click_point.y - click_point.y;
    page_offset_x = last_click_point.x - click_point.x;

#if 0
    LV_LOG("page_offset_x: %d\n", page_offset_x);
    LV_LOG("last_click_point.x: %d\n", last_click_point.x);
    LV_LOG("click_point.x: %d\n", click_point.x);
#endif

    // 判断当前是否处理覆盖页
    bool is_overlay_page = PageManager.main_page->is_overlay_active && anim_mode == anim_overlay;
    // 拉动被覆盖页
    if (is_overlay_page) {
        // 目标页改为源页
        target_page = source_page;
        // 源页改为覆盖页
        source_page = PageManager.main_page->overlay_page->obj;
        target_page_align = LV_ALIGN_CENTER;

        if (lv_obj_get_x(source_page) != 0 || lv_obj_get_y(source_page) != 0) {
            check_target_page_pos = true;
        } else {
            check_target_page_pos = false;

        }
        // 如果目标页未创建,则须先创建
        if (source_page == NULL) {
            // LV_LOG("target_page == NULL\n");
            // return;
            // CENTER类的ALIGN会闪屏
            // align需要赋正确，否则会导致在实机的时候点击过快导致pressing只进一次，在此处return后不会走后续的lv_obj_align_to，进而导致target_page的位置不对
            pm_creat_page(PageManager.main_page->overlay_page, PageManager.main_page, target_page_align,
                          lv_color_black());
            LV_LOG("overlay_page == NULL return\n");
            return;
        }
    } else {
        // 如果目标页未创建,则须先创建
        if (target_page == NULL) {
            // LV_LOG("target_page == NULL\n");
            // return;
            // CENTER类的ALIGN会闪屏
            // align需要赋正确，否则会导致在实机的时候点击过快导致pressing只进一次，在此处return后不会走后续的lv_obj_align_to，进而导致target_page的位置不对
            pm_creat_page(handle_struct->target_page, PageManager.main_page, target_page_align, lv_color_black());
            LV_LOG("target_page == NULL return\n");
            return;
        }
        switch (handle_struct->position) {
            case pos_top:
                check_target_page_pos = lv_obj_get_y(target_page) >= 0;
                break;
            case pos_bottom:
                check_target_page_pos = lv_obj_get_y(target_page) <= 0;
                break;
            case pos_left:
                check_target_page_pos = lv_obj_get_x(target_page) >= 0;
                break;
            case pos_right:
                check_target_page_pos = lv_obj_get_x(target_page) <= 0;
                break;
            default:
                break;
        }
    }

    switch (handle_struct->position) {
        case pos_top:
            source_page_limit_pos = lv_obj_get_height(target_page);
            break;
        case pos_bottom:
            source_page_limit_pos = -lv_obj_get_height(target_page);
            break;
        case pos_left:
            source_page_limit_pos = lv_obj_get_width(target_page);
            break;
        case pos_right:
            source_page_limit_pos = -lv_obj_get_width(target_page);
            break;
        default:
            break;
    }

    // 把后一次的y保存到前一次
    click_point.y = last_click_point.y;
    click_point.x = last_click_point.x;
    lv_coord_t target_page_y = (lv_coord_t) (lv_obj_get_y(target_page) + page_offset_y);
    lv_coord_t source_page_y = (lv_coord_t) (lv_obj_get_y(source_page) + page_offset_y);
    lv_coord_t target_page_x = (lv_coord_t) (lv_obj_get_x(target_page) + page_offset_x);
    lv_coord_t source_page_x = (lv_coord_t) (lv_obj_get_x(source_page) + page_offset_x);

    // 将 target_page 移动到最前的方法移动到此处，避免点击手柄时闪屏的问题
    lv_obj_move_foreground(target_page);

    // 竖直方向滑动
    if (slide_direction == slide_vertical) {
        if (anim_mode == anim_slide) {
            // 符号相反，跳出点击函数
            // 当source_page_y 为负数时，限制范围若为正数，则说明使用了上手柄上滑
            if (source_page_y * source_page_limit_pos < 0) {
                return;
            }
            lv_obj_set_y(source_page, source_page_y);
            // lv_obj_align_to 将 TargetPage 对象对齐到 SourcePage 对象的中心，并在 x 轴上偏移 SourcePage 对象的宽度。
            // lv_obj_align_to(TargetPage->obj, SourcePage->obj, LV_ALIGN_CENTER, -lv_obj_get_width(SourcePage->obj), 0);
            lv_obj_align_to(target_page, source_page, target_page_align, 0, 0);
        } else if (anim_mode == anim_overlay) {
            // 修改通知栏父级对象
            if (!handle_struct->target_page->is_overlay_active) {
                if (lv_obj_is_valid(status_bar)) {
                    lv_obj_set_parent(status_bar, source_page);
                }
            }
            // lv_obj_move_background(status_bar);
            // if (lv_obj_get_parent(status_bar) == Pages.Page_Home.obj) {
            //     LV_LOG("here\n");
            // }
            if (check_target_page_pos) {
                if (target_page_align == LV_ALIGN_CENTER) {
                    lv_obj_align_to(source_page, target_page, target_page_align, 0, 0);
                } else {
                    lv_obj_align_to(target_page, source_page, target_page_align, 0, 0);
                }
            } else {
                if (target_page_y * source_page_limit_pos < 0 && PageManager.main_page->is_overlay_active) {
                    return;
                }
                // 根据差值调整背景板y坐标
                lv_obj_set_y(target_page, target_page_y);
            }
        }
    } else if (slide_direction == slide_horizontal) {
        // 水平方向滑动
        if (anim_mode == anim_slide) {
            // 符号相反，跳出点击函数
            // 当source_page_x 为负数时，限制范围若为正数，则说明使用了左手柄左滑
            if (source_page_x * source_page_limit_pos < 0) {
                return;
            }
            lv_obj_set_x(source_page, source_page_x);
            lv_obj_align_to(target_page, source_page, target_page_align, 0, 0);
        } else if (anim_mode == anim_overlay) {
            // 修改通知栏父级对象
            if (!handle_struct->target_page->is_overlay_active) {
                if (lv_obj_is_valid(status_bar)) {
                    lv_obj_set_parent(status_bar, source_page);
                }
            }
            if (check_target_page_pos) {
                if (target_page_align == LV_ALIGN_CENTER) {
                    lv_obj_align_to(source_page, target_page, target_page_align, 0, 0);
                } else {
                    lv_obj_align_to(target_page, source_page, target_page_align, 0, 0);
                }
            } else {
                if (target_page_x * source_page_limit_pos < 0 && PageManager.main_page->is_overlay_active) {
                    return;
                }
                // 根据差值调整背景板x坐标
                lv_obj_set_x(target_page, target_page_x);
            }
        }
    }
}

// 上下拉松手回调
static void page_swipe_released_cb(lv_event_t *e) {
    // if (!Anim.is_finished_anim || !had_last_point) {
    if (!Anim.is_finished_anim) {
        LV_LOG("released_cb return\n");
        return;
    }
    LV_LOG("released_cb\n");

    drag_touched = false;
    lv_obj_t *tar_obj = lv_event_get_target(e);
    HandleStructType *handle_struct = lv_obj_get_user_data(tar_obj);
    // 获取触发事件的对象的父对象
    lv_obj_t *parent_obj = lv_obj_get_parent(tar_obj);
    // lv_obj_t *parent_obj = lv_obj_get_parent(handle_struct->parent_page);
    PageTypeHandle *parent_node = lv_obj_get_user_data(parent_obj);

    if (PageManager.main_page->is_overlay_active) {
        if (PageManager.main_page->overlay_page == NULL || PageManager.main_page->overlay_page->obj == NULL) {
            LV_LOG("overlay_page is NULL\n");
            return;
        }
    } else {
        if (handle_struct->target_page == NULL || handle_struct->target_page->obj == NULL) {
            LV_LOG("target_page is NULL\n");
            return;
        }
    }


    lv_obj_clear_flag(Anim.mask, LV_OBJ_FLAG_HIDDEN);
    if (lv_obj_has_flag(Anim.mask, LV_OBJ_FLAG_HIDDEN)) {
        LV_LOG("has LV_OBJ_FLAG_HIDDEN\n");
    } else {
        LV_LOG("has not LV_OBJ_FLAG_HIDDEN\n");
    }

    Anim.target_page = handle_struct->target_page;
    Anim.source_page = PageManager.main_page;
    lv_obj_t *target_page = Anim.target_page->obj;
    lv_obj_t *source_page = Anim.source_page->obj;
    PageMotionDirectionEnum page_motion_direction = page_motion_direction_none;
    uint8_t trigger_threshold = 5;
    SlideDirectionEnumType slide_direction = slide_none;
    // 动画参数
    lv_anim_t target_page_anim;
    lv_anim_t source_page_anim;
    lv_anim_init(&target_page_anim);
    lv_anim_init(&source_page_anim);
    lv_anim_set_var(&target_page_anim, target_page);
    lv_anim_set_var(&source_page_anim, source_page);
    AnimModeEnum anim_mode = parent_node->pos[handle_struct->position].anim_mode;
    switch (handle_struct->position) {
        case pos_top:
            slide_direction = slide_vertical;
            break;
        case pos_bottom:
            slide_direction = slide_vertical;
            break;
        case pos_left:
            slide_direction = slide_horizontal;
            break;
        case pos_right:
            slide_direction = slide_horizontal;
            break;
        default:
            break;
    }

    bool overlay_flag = false;
    if (PageManager.main_page->is_overlay_active) {
        target_page = source_page;
        target_page_anim = source_page_anim;
        lv_anim_set_var(&target_page_anim, target_page);
        // 重新进松手事件才会初始化为false
        overlay_flag = true;
    }

    if (slide_direction == slide_vertical) {
        //如果y差值大于5，页面往下拉到底
        if (page_offset_y > trigger_threshold) {
            page_motion_direction = page_pull_down;
        } else if (page_offset_y < -trigger_threshold) {
            //如果y差值小于-5，页面往上拉到顶
            page_motion_direction = page_pull_up;
        }

        // 若页面运动方向未赋值，再判断页面所处位置进行页面运动动画
        if (page_motion_direction == page_motion_direction_none) {
            bool check_param = false;
            bool top_check = lv_obj_get_y(target_page) >= -lv_obj_get_height(target_page) / 2;
            bool bottom_check = lv_obj_get_y(target_page) >= lv_obj_get_height(target_page) / 2;
            //超过一半往下拉
            // TODO 其余方向未做覆盖时的判断条件调换，会导致下一个TODO的情况
            if (!PageManager.main_page->is_overlay_active) {
                if (handle_struct->position == pos_top) {
                    check_param = top_check;
                } else if (handle_struct->position == pos_bottom) {
                    check_param = bottom_check;
                }
            } else {
                if (handle_struct->position == pos_top) {
                    check_param = bottom_check;
                } else if (handle_struct->position == pos_bottom) {
                    check_param = top_check;
                }
            }
            if (check_param) {
                page_motion_direction = page_pull_down;
            } else {
                //小于一半往上拉
                // TODO 覆盖时，释放必定走这里
                page_motion_direction = page_pull_up;
            }
        }
        lv_anim_set_exec_cb(&target_page_anim, (lv_anim_exec_xcb_t) lv_obj_set_y);
        lv_anim_set_exec_cb(&source_page_anim, (lv_anim_exec_xcb_t) lv_obj_set_y);
    } else if (slide_direction == slide_horizontal) {
        LV_LOG("page_offset_x: %d\n", page_offset_x);
        //如果x差值大于5，页面往右拉到底
        if (page_offset_x > trigger_threshold) {
            page_motion_direction = page_pull_right;
        } else if (page_offset_x < -trigger_threshold) {
            //如果x差值小于-5，页面往左拉到顶
            page_motion_direction = page_pull_left;
        }
        // 若页面运动方向未赋值，再判断页面所处位置进行页面运动动画
        if (page_motion_direction == page_motion_direction_none) {
            bool check_param = false;
            bool left_check = lv_obj_get_x(target_page) >= -lv_obj_get_width(target_page) / 2;
            bool right_check = lv_obj_get_x(target_page) >= lv_obj_get_width(target_page) / 2;
            // 超过一半往右拉
            if (!PageManager.main_page->is_overlay_active) {
                if (handle_struct->position == pos_left) {
                    check_param = left_check;
                } else if (handle_struct->position == pos_right) {
                    check_param = right_check;
                }
            } else {
                if (handle_struct->position == pos_left) {
                    check_param = right_check;
                } else if (handle_struct->position == pos_right) {
                    check_param = left_check;
                }
            }
            if (check_param) {
                page_motion_direction = page_pull_right;
            } else {
                //小于一半往左拉
                page_motion_direction = page_pull_left;
            }
        }
        lv_anim_set_exec_cb(&target_page_anim, (lv_anim_exec_xcb_t) lv_obj_set_x);
        lv_anim_set_exec_cb(&source_page_anim, (lv_anim_exec_xcb_t) lv_obj_set_x);
    }

    int32_t target_page_end_pos, source_page_end_pos;
    bool change_main_page = false;
    switch (anim_mode) {
        case anim_slide:
            if (page_motion_direction == page_pull_down) {
                if (handle_struct->position != pos_bottom) {
                    target_page_end_pos = 0;
                    source_page_end_pos = lv_obj_get_height(source_page);
                    // 下拉且目标页面为当前主页上方的页面时才切换主页
                    pm_set_main_page(handle_struct->target_page);
                    change_main_page = true;
                } else {
                    target_page_end_pos = lv_obj_get_height(source_page);
                    source_page_end_pos = 0;
                }
                // 动画时长 = 松手位置到目标位置的距离
                lv_anim_set_time(&target_page_anim, target_page_end_pos - lv_obj_get_y(target_page));
                lv_anim_set_values(&target_page_anim, lv_obj_get_y(target_page), target_page_end_pos);
                lv_anim_set_time(&source_page_anim, source_page_end_pos - lv_obj_get_y(source_page));
                lv_anim_set_values(&source_page_anim, lv_obj_get_y(source_page), source_page_end_pos);
                LV_LOG("T down %d\t", target_page_end_pos - lv_obj_get_y(target_page));
                LV_LOG("S down %d\n", source_page_end_pos - lv_obj_get_y(source_page));
            } else if (page_motion_direction == page_pull_up) {
                if (handle_struct->position != pos_top) {
                    target_page_end_pos = 0;
                    source_page_end_pos = -lv_obj_get_height(target_page);
                    // 上拉且目标页面为当前主页下方的页面时才切换主页
                    pm_set_main_page(handle_struct->target_page);
                    change_main_page = true;
                } else {
                    target_page_end_pos = -lv_obj_get_height(target_page);
                    source_page_end_pos = 0;
                }
                lv_anim_set_time(&target_page_anim, LV_ABS(target_page_end_pos - lv_obj_get_y(target_page)));
                lv_anim_set_values(&target_page_anim, lv_obj_get_y(target_page), target_page_end_pos);
                lv_anim_set_time(&source_page_anim, LV_ABS(source_page_end_pos - lv_obj_get_y(source_page)));
                lv_anim_set_values(&source_page_anim, lv_obj_get_y(source_page), source_page_end_pos);
                LV_LOG("T up %d\t", LV_ABS(target_page_end_pos - lv_obj_get_y(target_page)));
                LV_LOG("S up %d\n", LV_ABS(source_page_end_pos - lv_obj_get_y(source_page)));
            } else if (page_motion_direction == page_pull_left) {
                if (handle_struct->position != pos_left) {
                    target_page_end_pos = 0;
                    source_page_end_pos = -lv_obj_get_width(target_page);
                    // 上拉且目标页面为当前主页下方的页面时才切换主页
                    pm_set_main_page(handle_struct->target_page);
                    change_main_page = true;
                } else {
                    target_page_end_pos = -lv_obj_get_width(target_page);
                    source_page_end_pos = 0;
                }
                lv_anim_set_time(&target_page_anim, LV_ABS(target_page_end_pos - lv_obj_get_x(target_page)));
                lv_anim_set_values(&target_page_anim, lv_obj_get_x(target_page), target_page_end_pos);
                lv_anim_set_time(&source_page_anim, LV_ABS(source_page_end_pos - lv_obj_get_x(source_page)));
                lv_anim_set_values(&source_page_anim, lv_obj_get_x(source_page), source_page_end_pos);
                LV_LOG("T left %d\t", LV_ABS(target_page_end_pos - lv_obj_get_x(target_page)));
                LV_LOG("S left %d\n", LV_ABS(source_page_end_pos - lv_obj_get_x(source_page)));
                // page_click_anim(page_pull_left, handle_struct->position, handle_struct->target_page);
            } else if (page_motion_direction == page_pull_right) {
                if (handle_struct->position != pos_right) {
                    target_page_end_pos = 0;
                    source_page_end_pos = lv_obj_get_width(target_page);
                    // 上拉且目标页面为当前主页下方的页面时才切换主页
                    pm_set_main_page(handle_struct->target_page);
                    change_main_page = true;
                } else {
                    target_page_end_pos = lv_obj_get_width(target_page);
                    source_page_end_pos = 0;
                }
                lv_anim_set_time(&target_page_anim, target_page_end_pos - lv_obj_get_x(target_page));
                lv_anim_set_values(&target_page_anim, lv_obj_get_x(target_page), target_page_end_pos);
                lv_anim_set_time(&source_page_anim, source_page_end_pos - lv_obj_get_x(source_page));
                lv_anim_set_values(&source_page_anim, lv_obj_get_x(source_page), source_page_end_pos);
                LV_LOG("T right %d\t", target_page_end_pos - lv_obj_get_x(target_page));
                LV_LOG("S right %d\n", source_page_end_pos - lv_obj_get_x(source_page));
            }
            PageManager.main_page->is_overlay_active = false;
            if (change_main_page) {
                // 不删除页面无法重绘组别详情页
                lv_anim_set_ready_cb(&target_page_anim, slide_ready_cb);
                LV_LOG("target_page_end_pos:::::%d\n", target_page_end_pos);
            } else {
                lv_anim_set_ready_cb(&source_page_anim, slide_source_ready_cb);
            }
            lv_anim_start(&target_page_anim);
            lv_anim_start(&source_page_anim);
            // lv_obj_del_async(source_page);
            // if (PageManager.main_page == &Pages.Page_Home) {
            //     LV_LOG("main page is page home\n");
            // } else if (PageManager.main_page == &Pages.Page_Control_Center) {
            //     LV_LOG("main page is page Control_Center\n");
            // } else if (PageManager.main_page == &Pages.Page_Group_Info) {
            //     LV_LOG("main page is page Group_Info\n");
            // }
            // if (lv_obj_get_user_data(handle_struct->target_page->obj) == PageManager.main_page->top.page) {
            //     LV_LOG("set top.page to main page\n");
            // }
            break;
        case anim_overlay:
            // TODO 从下方覆盖上来后若右手柄还是覆盖动画，那么会把自己推出去，而非把右边页面拉出来，考虑做垂直和水平覆盖的区分
            if (target_page == PageManager.main_page->obj) {
                LV_LOG("PageManager.main_page->obj\n");
            }
            if (page_motion_direction == page_pull_down) {
                LV_LOG("overlay_pull_down\n");
                if (handle_struct->position == pos_top) {
                    // 下拉且目标页面为当前主页上方的页面时才切换主页
                    if (overlay_flag) {
                        target_page_end_pos = lv_obj_get_height(target_page);
                        PageManager.main_page->is_overlay_active = false;
                        pm_set_main_page(PageManager.main_page->overlay_page);
                        Anim.can_be_deleted = true;
                    } else {
                        target_page_end_pos = 0;
                        // 保存覆盖前的页面
                        handle_struct->target_page->overlay_page = PageManager.main_page;
                        // 切换主页
                        pm_set_main_page(handle_struct->target_page);
                        // 记录当前页为覆盖页
                        PageManager.main_page->is_overlay_active = true;
                    }
                } else {
                    // 回弹
                    if (overlay_flag) {
                        target_page_end_pos = 0;
                    } else {
                        target_page_end_pos = lv_obj_get_height(target_page);
                    }
                }
                lv_anim_set_time(&target_page_anim, target_page_end_pos - lv_obj_get_y(target_page));
                lv_anim_set_values(&target_page_anim, lv_obj_get_y(target_page), target_page_end_pos);
            } else if (page_motion_direction == page_pull_up) {
                LV_LOG("overlay_pull_up\n");
                if (handle_struct->position == pos_bottom) {
                    // 上拉且由下手柄触发时才切换主页
                    if (overlay_flag) {
                        target_page_end_pos = -lv_obj_get_height(target_page);
                        PageManager.main_page->is_overlay_active = false;
                        pm_set_main_page(PageManager.main_page->overlay_page);
                        Anim.can_be_deleted = true;
                    } else {
                        target_page_end_pos = 0;
                        handle_struct->target_page->overlay_page = PageManager.main_page;
                        pm_set_main_page(handle_struct->target_page);
                        PageManager.main_page->is_overlay_active = true;
                    }
                } else {
                    target_page_end_pos = -lv_obj_get_height(target_page);
                }
                lv_anim_set_time(&target_page_anim, LV_ABS(target_page_end_pos - lv_obj_get_y(target_page)));
                lv_anim_set_values(&target_page_anim, lv_obj_get_y(target_page), target_page_end_pos);
            } else if (page_motion_direction == page_pull_left) {
                LV_LOG("overlay_pull_left\n");
                if (handle_struct->position == pos_right) {
                    if (overlay_flag) {
                        target_page_end_pos = -lv_obj_get_width(target_page);
                        PageManager.main_page->is_overlay_active = false;
                    } else {
                        target_page_end_pos = 0;
                    }
                    // 左拉且目标页面为当前主页右方的页面时才切换主页
                    Anim.can_be_deleted = true;
                    pm_set_main_page(handle_struct->target_page);
                    if (!overlay_flag) {
                        PageManager.main_page->is_overlay_active = true;
                    }
                } else {
                    // 回弹
                    if (overlay_flag) {
                        target_page_end_pos = 0;
                    } else {
                        target_page_end_pos = -lv_obj_get_width(target_page);
                    }
                }
                lv_anim_set_time(&target_page_anim, LV_ABS(target_page_end_pos - lv_obj_get_x(target_page)));
                lv_anim_set_values(&target_page_anim, lv_obj_get_x(target_page), target_page_end_pos);
            } else if (page_motion_direction == page_pull_right) {
                LV_LOG("overlay_pull_right\n");
                if (handle_struct->position == pos_left) {
                    if (overlay_flag) {
                        target_page_end_pos = lv_obj_get_width(target_page);
                        PageManager.main_page->is_overlay_active = false;
                    } else {
                        target_page_end_pos = 0;
                    }
                    // 右拉且目标页面为当前主页左方的页面时才切换主页
                    Anim.can_be_deleted = true;
                    pm_set_main_page(handle_struct->target_page);
                    // 若设置了始终覆盖状态，则也将状态设置回 true
                    if (!overlay_flag || PageManager.main_page->is_always_overlay) {
                        PageManager.main_page->is_overlay_active = true;
                    }
                } else {
                    target_page_end_pos = lv_obj_get_width(target_page);
                }
                lv_anim_set_time(&target_page_anim, LV_ABS(target_page_end_pos - lv_obj_get_x(target_page)));
                lv_anim_set_values(&target_page_anim, lv_obj_get_x(target_page), target_page_end_pos);
            }
            if (!overlay_flag) {
                switch (handle_struct->position) {
                    case pos_top:
                        PageManager.main_page->overlay_pos = pos_bottom;
                        break;
                    case pos_bottom:
                        PageManager.main_page->overlay_pos = pos_top;
                        break;
                    case pos_left:
                        PageManager.main_page->overlay_pos = pos_right;
                        break;
                    case pos_right:
                        PageManager.main_page->overlay_pos = pos_left;
                        break;
                    default:
                        break;
                }
            }
            lv_anim_set_ready_cb(&target_page_anim, overlay_ready_cb);
            lv_anim_start(&target_page_anim);
            break;
        default:
            break;
    }
    printf_main_page();
}

// 释放用于手柄结构体的动态内存
void free_handle_struct_cb(lv_event_t *e) {
    lv_obj_t *handle = lv_event_get_target(e);
    HandleStructType *anim_data = (HandleStructType *) lv_obj_get_user_data(handle);
    if (anim_data != NULL) {
        free(anim_data);
        lv_obj_set_user_data(handle, NULL);
    }
}

// 创建手柄
lv_obj_t *pm_create_handle(lv_obj_t *parent_page, PositionEnum position) {
    // 立即刷新
    // lv_refr_now(NULL);
    // parent_page可能未完成布局，故取不到值,在回调函数内可取到
    // LV_LOG("W %d\n",lv_obj_get_width(parent_page));
    // LV_LOG("H %d\n",lv_obj_get_height(parent_page));

    lv_align_t handle_align = LV_ALIGN_DEFAULT;
    lv_coord_t handle_width = 0, handle_height = 0, x_offset = 0, y_offset = 0, handle_thickness = 20;
    PageTypeHandle *parent_page_node = lv_obj_get_user_data(parent_page);
    // PageTypeHandle *parent_page_node = lv_obj_get_user_data(PageManager.main_page->obj);

    HandleStructType *handle_struct = (HandleStructType *) malloc(sizeof(HandleStructType));
    if (handle_struct == NULL) {
        // 如果分配失败，处理错误
        LV_LOG("Memory allocation failed\n");
        return NULL;
    }
    handle_struct->position = position;
    handle_struct->parent_page = parent_page;
    handle_struct->target_page = parent_page_node->pos[position].page;

    switch (position) {
        case pos_top:
            handle_align = LV_ALIGN_TOP_MID;
            handle_width = Screen_Width;
            handle_height = handle_thickness;
            break;
        case pos_bottom:
            handle_align = LV_ALIGN_BOTTOM_MID;
            handle_width = Screen_Width;
            handle_height = handle_thickness;
            break;
        case pos_left:
            handle_align = LV_ALIGN_LEFT_MID;
            handle_width = handle_thickness;
            handle_height = Screen_Height;
            break;
        case pos_right:
            handle_align = LV_ALIGN_RIGHT_MID;
            handle_width = handle_thickness;
            handle_height = Screen_Height;
            break;
        default:
            break;
    }

    // 主屏上面的面板上拉手柄
    lv_obj_t *handle = lv_label_create(parent_page);
    lv_obj_set_size(handle, handle_width, handle_height);
    lv_obj_align(handle, handle_align, 0, 0);
    // 设置背景颜色和不透明度
    // lv_obj_set_style_bg_color(handle, lv_color_hex(0xf65c64), LV_PART_MAIN | LV_STATE_DEFAULT);
    // lv_obj_set_style_bg_opa(handle, LV_OPA_COVER, LV_PART_MAIN | LV_STATE_DEFAULT);

    // 增加触控范围
    lv_obj_set_ext_click_area(handle, handle_thickness);
    lv_label_set_text(handle, " ");
    lv_obj_add_flag(handle, LV_OBJ_FLAG_CLICKABLE | LV_OBJ_FLAG_OVERFLOW_VISIBLE);

    // 按住回调
    lv_obj_add_event_cb(handle, page_swipe_pressing_cb, LV_EVENT_PRESSING, NULL);
    // 松开回调
    lv_obj_add_event_cb(handle, page_swipe_released_cb, LV_EVENT_RELEASED, NULL);
    // 删除页面时释放动态内存
    lv_obj_add_event_cb(handle, free_handle_struct_cb, LV_EVENT_DELETE, NULL);
    // 设置 user_data 为 handle_struct
    lv_obj_set_user_data(handle, handle_struct);
    return handle;
}
