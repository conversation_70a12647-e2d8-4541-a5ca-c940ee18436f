//
// Created by <PERSON><PERSON> on 2024/9/2.
//

#include "page_setting.h"
#include "lvgl_custom_function.h"
#include "comp_menu_option.h"
#include "comp_status_bar.h"
#include "comp_bar.h"

lv_obj_t *handle_setting_bottom;

// 结构体需放在全局，避免被销毁
OptionStruct option_rf = {setting_rf, "", &icon_rf, false, opt_setting};
OptionStruct option_dist = {setting_dist, "DIST", NULL, false, opt_setting};
#if ProductModel == QZ_N
OptionStruct option_sync = {setting_sync, "SYNC", NULL, false, opt_setting};
#elif ProductModel == QZ_F
OptionStruct option_zoom_disp = {setting_zoom_disp, "", &icon_zoom, false, opt_setting};
#endif
OptionStruct option_beep = {setting_beep, "", &icon_vol_on, false, opt_setting};
OptionStruct option_zoom = {setting_zoom, "ZOOM", NULL, false, opt_setting};
OptionStruct option_tcm = {setting_tcm, "TCM", NULL, true, opt_setting};
// OptionStruct option_shoot = {setting_shoot, "SHOOT", NULL, true, opt_setting};
OptionStruct option_shoot = {setting_shoot, "", &icon_hotshoe, true, opt_setting};
//OptionStruct option_2_5_mm = {setting_2_5mm, "2.5mm", NULL, false, opt_setting};
OptionStruct option_screen = {setting_screen, "", &icon_screen_setting, false, opt_setting};
#if MODULE_BT
OptionStruct option_bluetooth = {setting_bluetooth, "", &icon_bluetooth, true, opt_setting};
#endif
OptionStruct option_disp_type = {setting_disp_type, "", &icon_power_disp, false, opt_setting};
OptionStruct option_language = {setting_language, "", &icon_language, false, opt_setting};
OptionStruct option_reset = {setting_reset, "", &icon_reset, false, opt_setting};

void page_setting_init(lv_obj_t *parent) {
    lv_obj_t *h_layout = lv_layout_custom_create(parent, LV_FLEX_FLOW_ROW_WRAP);
    const uint8_t sub_comp_spacing = 8;
    lv_obj_set_style_pad_ver(h_layout, status_bar_h + 10, 0);
    lv_obj_set_style_bg_opa(h_layout, LV_OPA_TRANSP, 0);
    lv_obj_set_size(h_layout, LV_PCT(100), LV_PCT(100));
    lv_obj_set_style_pad_hor(h_layout, 18, 0);
    lv_obj_set_style_pad_gap(h_layout, sub_comp_spacing, 0);
    lv_obj_set_scrollbar_mode(h_layout, LV_SCROLLBAR_MODE_OFF);
    lv_obj_set_scroll_dir(h_layout, LV_DIR_VER);

    comp_menu_option_init(h_layout, &option_rf, &Pages.page[Page_Setting]);
    comp_menu_option_init(h_layout, &option_dist, &Pages.page[Page_Setting]);
#if ProductModel == QZ_N
    comp_menu_option_init(h_layout, &option_sync, &Pages.page[Page_Setting]);
#elif ProductModel == QZ_F
    comp_menu_option_init(h_layout, &option_zoom_disp, &Pages.page[Page_Setting]);
#endif
    comp_menu_option_init(h_layout, &option_beep, &Pages.page[Page_Setting]);
    comp_menu_option_init(h_layout, &option_zoom, &Pages.page[Page_Setting]);
    comp_menu_option_init(h_layout, &option_tcm, &Pages.page[Page_Setting]);
    comp_menu_option_init(h_layout, &option_shoot, &Pages.page[Page_Setting]);
    //comp_menu_option_init(h_layout, &option_2_5_mm,&Pages.page[Page_Setting]);
    comp_menu_option_init(h_layout, &option_screen, &Pages.page[Page_Setting]);
#if MODULE_BT
    comp_menu_option_init(h_layout, &option_bluetooth, &Pages.page[Page_Setting]);
#endif
    comp_menu_option_init(h_layout, &option_disp_type, &Pages.page[Page_Setting]);
    comp_menu_option_init(h_layout, &option_language, &Pages.page[Page_Setting]);
    comp_menu_option_init(h_layout, &option_reset, &Pages.page[Page_Setting]);

    // 半透明遮罩有两种思路：
    // 第一种是直接使用图片
    // 第二种是创建一个48高度的透明obj
    // 然后再创建一个从85到255渐变的组件（figma内是前33%纯透明，剩余部分再渐变）
    // lv_obj_set_style_bg_main_stop(ui->screen_cont_1, 85, LV_PART_MAIN|LV_STATE_DEFAULT);
    // lv_obj_set_style_bg_grad_stop(ui->screen_cont_1, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    // 但考虑到实机使用lvgl原生渐变容易出现色块，固还是选择第一种

    // lv_obj_t *img = lv_img_custom_create(parent, &img_translucent_mask_bg);
    // lv_obj_align(img, LV_ALIGN_BOTTOM_MID, 0, 0);

    // 底部手柄视觉对象
    bottom_bar_init(parent);

    handle_setting_bottom = pm_create_handle(parent, pos_bottom);
}
