//
// Created by <PERSON><PERSON> on 2025/4/18.
//
#if 0
#include "comp_sleep.h"
#include "user.h"

void sleep_arc_point_init(lv_obj_t *parent, int16_t x, int16_t y) {
    lv_obj_t *point = lv_obj_create(parent);
    lv_obj_add_style(point, &PageManager.default_style, 0);
    lv_obj_set_size(point, 4, 4);
    lv_obj_set_style_bg_opa(point, LV_OPA_COVER, 0);
    lv_obj_set_style_bg_color(point, lv_color_white(), 0);
    lv_obj_set_style_radius(point, 2, 0);
    lv_obj_set_pos(point, x, y);
}

lv_obj_t *comp_sleep_init() {
    lv_obj_t *mask = lv_obj_custom_trans_create(lv_layer_top(), LV_PCT(100), LV_PCT(100));
    lv_obj_set_style_bg_color(mask, lv_color_black(), 0);
    lv_obj_set_style_bg_opa(mask, LV_OPA_COVER, 0);

    lv_obj_t *sleep_obj = lv_obj_custom_trans_create(mask, 160, 144);
    // lv_obj_set_style_border_width(sleep_obj, 1, 0);
    lv_obj_center(sleep_obj);

    lv_obj_t *arc_obj = lv_obj_custom_trans_create(sleep_obj, 114, 114);
    lv_obj_align(arc_obj, LV_ALIGN_BOTTOM_LEFT, 0, 0);
    // lv_obj_set_style_border_width(arc_obj, 1, 0);

    lv_obj_t *arc = lv_arc_create(arc_obj);
    lv_arc_set_angles(arc, 0, 270);
    lv_arc_set_bg_angles(arc, 0, 270);
    lv_obj_set_style_arc_width(arc, 4, LV_PART_MAIN);
    lv_obj_set_style_arc_width(arc, 0, LV_PART_INDICATOR);
    lv_obj_set_size(arc, LV_PCT(100), LV_PCT(100));
    // 去除旋钮
    lv_obj_remove_style(arc, NULL, LV_PART_KNOB);
    // 去除可点击属性
    lv_obj_clear_flag(arc, LV_OBJ_FLAG_CLICKABLE);

    sleep_arc_point_init(arc_obj, 9, (-115 / 2) + 2 + 1);
    sleep_arc_point_init(arc_obj, 20, (-115 / 2) + 2 + 4);
    sleep_arc_point_init(arc_obj, 29, (-115 / 2) + 2 + 10);
    sleep_arc_point_init(arc_obj, 38, (-115 / 2) + 2 + 17);
    sleep_arc_point_init(arc_obj, 44, (-115 / 2) + 2 + 25);
    sleep_arc_point_init(arc_obj, 49, (-115 / 2) + 2 + 34);
    sleep_arc_point_init(arc_obj, 53, (-115 / 2) + 2 + 45);

    lv_obj_t *line_v = lv_line_custom_creat(arc_obj, 4, 30, 2);
    lv_obj_set_pos(line_v, -2, -(30 / 2) + 4 + 2);

    lv_obj_t *line_h = lv_line_custom_creat(arc_obj, 30, 4, 2); 
    lv_obj_set_pos(line_h, (30 / 2) - 4, 2 + 2);

    lv_obj_t *img_sleep = lv_img_custom_create(sleep_obj, &icon_sleep);
    lv_obj_align(img_sleep, LV_ALIGN_TOP_RIGHT, 0, 0);

    // lv_obj_t *z1_obj = lv_obj_custom_trans_create(sleep_obj, 25, 25);
    // lv_obj_align(z1_obj, LV_ALIGN_TOP_RIGHT, 0, 0);
    // lv_obj_t *line_t = lv_line_custom_creat(z1_obj, 23, 3, 2);
    // lv_obj_t *line_m = lv_line_custom_creat(mask, 32, 3, 2);
    // // lv_obj_set_style_transform_angle(line_m, 490, 0);
    // lv_obj_center(line_m);
    // lv_obj_t *line_b = lv_line_custom_creat(z1_obj, 24, 3, 2);
    // lv_obj_align(line_t, LV_ALIGN_TOP_LEFT, 0, 0);
    // lv_obj_align(line_b, LV_ALIGN_BOTTOM_RIGHT, 0, 0);
    // lv_obj_t *point = lv_obj_create(arc_obj);
    // lv_obj_set_style_bg_opa(point, LV_OPA_COVER, 0);
    // lv_obj_set_style_bg_color(point, lv_color_white(), 0);
    // lv_obj_set_style_radius(point, 2, 0);
    // lv_obj_set_pos(point, 9, (-115 / 2) + 2 + 1);
    // }
    return mask;
}
#endif