//
// Created by <PERSON><PERSON> on 2024/7/11.
//

#include "comp_status_bar.h"
#include "user.h"
#include "utils.h"
#include "view_model.h"

lv_coord_t status_bar_h = 36;
uint8_t img_index = 0;

// 定义电池图片数组
const lv_img_dsc_t *battery_images[] = {
        &icon_electricity_level_0,
        &icon_electricity_level_1,
        &icon_electricity_level_2,
        &icon_electricity_level_3
};

// 定时器回调函数
static void battery_animation_cb(lv_timer_t *timer) {
    if (Param.battery_status == battery_charging) {
        // 切换到下一张图片
        img_index = (img_index + 1) % 4;  // 0 -> 1 -> 2 -> 3 -> 0 循环
        if (img_index < Param.electricity_level) {
            img_index = Param.electricity_level;
            if (Param.electricity_level == 3) {
                img_index = 2;
            }
        }
        lv_img_set_src(StatusBar.icon_battery, battery_images[img_index]);  // 更新图片
    }
}

static void battery_icon_delete_cb(lv_event_t *e) {
    if (StatusBar.bat_timer != NULL) {
        lv_timer_del(StatusBar.bat_timer);
        StatusBar.bat_timer = NULL;
    }
}

static void battery_event_cb(lv_event_t *e) {
    lv_event_code_t code = lv_event_get_code(e);
    // lv_obj_t *obj = lv_event_get_target(e);
    if (code == LV_EVENT_VALUE_CHANGED) {
        LV_LOG("Param.battery_status == %d\n", Param.battery_status);
        if (Param.battery_status == battery_charging) {
            // 创建定时器，每500ms调用一次回调函数
            if (StatusBar.bat_timer != NULL) {
                lv_timer_del(StatusBar.bat_timer);
                StatusBar.bat_timer = NULL;
            }
            StatusBar.bat_timer = lv_timer_create(battery_animation_cb, 500, NULL);
        } else {
            if (StatusBar.bat_timer != NULL) {
                // 删除定时器，停止动画
                lv_timer_del(StatusBar.bat_timer);
                StatusBar.bat_timer = NULL;
            }
            if (Param.battery_status == battery_complete) {
                lv_img_set_src(StatusBar.icon_battery, &icon_chg_complete);
            } else if (Param.battery_status == battery_low || Param.battery_status == battery_off) {
                lv_img_set_src(StatusBar.icon_battery, &icon_battery_low);
            } else {
                lv_img_set_src(StatusBar.icon_battery, battery_images[Param.electricity_level]);
            }
        }
    }
}

// 更新状态栏
void update_status_bar() {
    // 避免状态栏不存在
    if (lv_obj_is_valid(status_bar)) {
        // 更新频道
        char *ch_label_str = malloc(7 * sizeof(char));
        if (ch_label_str == NULL) {
            LV_LOG("memory_fail\n");
            // 内存分配失败
            return;
        }
        sprintf(ch_label_str, "CH %d", Setting.roller_values[roller_setting_ch] + 1);
        // LV_LOG("ch: %d\n", Setting.roller_values[roller_setting_ch] + 1);
        lv_label_set_text(StatusBar.ch_label, ch_label_str);
        free_and_clear(ch_label_str);

        if (Setting.lamp_main_switch) {
            lv_obj_clear_flag(StatusBar.icon_lamp, LV_OBJ_FLAG_HIDDEN);
            // lv_obj_set_style_img_opa(StatusBar.icon_lamp, LV_OPA_COVER, 0);
        } else {
            lv_obj_add_flag(StatusBar.icon_lamp, LV_OBJ_FLAG_HIDDEN);
            // lv_obj_set_style_img_opa(StatusBar.icon_lamp, LV_OPA_TRANSP, 0);
        }

        // 更新同步图标
        if (Param.status_bar_sync == sync_front) {
            lv_img_set_src(StatusBar.icon_sync, &icon_front_curtain_36);
// #if ProductModel != QZ_N
        } else if (Param.status_bar_sync == sync_rear) {
            lv_img_set_src(StatusBar.icon_sync, &icon_rear_curtain_36);
// #endif
        } else if (Param.status_bar_sync == sync_high_speed) {
            lv_img_set_src(StatusBar.icon_sync, &icon_high_speed_36);
        }

        // 相机连接图标
        if (get_camera_link()) {
            lv_obj_clear_flag(StatusBar.icon_camera, LV_OBJ_FLAG_HIDDEN);
        } else {
            lv_obj_add_flag(StatusBar.icon_camera, LV_OBJ_FLAG_HIDDEN);
        }

        if (Setting.values[setting_shoot]) {
            lv_obj_clear_flag(StatusBar.icon_single, LV_OBJ_FLAG_HIDDEN);
        } else {
            lv_obj_add_flag(StatusBar.icon_single, LV_OBJ_FLAG_HIDDEN);
        }
    }
}

lv_obj_t *comp_status_bar_init() {
    lv_obj_t *h_layout = lv_layout_custom_create(lv_scr_act(), LV_FLEX_FLOW_ROW);
    lv_obj_set_size(h_layout, LV_PCT(100), status_bar_h);
    lv_obj_align(h_layout, LV_ALIGN_TOP_MID, 0, 0);
    lv_obj_set_style_bg_color(h_layout, lv_color_black(), 0);
    lv_obj_set_flex_align(h_layout, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_set_style_pad_gap(h_layout, 0, 0);
    lv_obj_clear_flag(h_layout, LV_OBJ_FLAG_CLICKABLE);
    lv_obj_clear_flag(h_layout, LV_OBJ_FLAG_SCROLLABLE);
    lv_obj_set_scrollbar_mode(h_layout, LV_SCROLLBAR_MODE_OFF);
    // lv_obj_set_style_border_width(h_layout, 1, 0);

    char *ch_label_str = malloc(7 * sizeof(char));
    if (ch_label_str == NULL) {
        LV_LOG("memory_fail\n");
        // 内存分配失败
        return NULL;
    }
    sprintf(ch_label_str, "CH %d", Setting.roller_values[roller_setting_ch] + 1);
    LV_LOG("ch: %d\n", Setting.roller_values[roller_setting_ch] + 1);
    lv_obj_t *ch_label = lv_label_custom_create(h_layout, ch_label_str, FONT_STATUS_BAR, lv_color_white(),
                                                LV_TEXT_ALIGN_LEFT);
    lv_coord_t ch_label_h = lv_font_get_line_height(FONT_STATUS_BAR);
    lv_coord_t ch_label_w = lv_txt_get_width("CH 32", 5, FONT_STATUS_BAR, 0, LV_TEXT_FLAG_EXPAND);
    const uint8_t ch_label_pad_left = 55;
    lv_obj_set_style_pad_left(ch_label, ch_label_pad_left, 0);
    lv_obj_set_style_pad_top(ch_label, 2, 0);
    lv_obj_set_size(ch_label, ch_label_w + ch_label_pad_left, ch_label_h);
    StatusBar.ch_label = ch_label;
    free_and_clear(ch_label_str);

    // lv_obj_set_style_bg_color(ch_label, lv_color_hex(Main_Color), 0);
    // lv_obj_set_style_bg_opa(ch_label, LV_OPA_COVER, 0);
    lv_obj_t *img_grp = lv_layout_custom_create(h_layout, LV_FLEX_FLOW_ROW);

    lv_obj_set_size(img_grp, Screen_Width - (ch_label_w + ch_label_pad_left), LV_PCT(100));
    // lv_obj_set_style_border_width(img_grp, 1, 0);
    // lv_obj_set_style_border_color(img_grp, lv_color_white(), 0);
    lv_obj_set_style_pad_right(img_grp, 50, 0);
    lv_obj_set_style_pad_gap(img_grp, 4, 0);
    lv_obj_set_style_bg_opa(img_grp, LV_OPA_TRANSP, 0);
    lv_obj_set_flex_align(img_grp, LV_FLEX_ALIGN_END, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(img_grp, LV_OBJ_FLAG_CLICKABLE);
    // lv_obj_set_style_bg_color(img_grp, lv_color_hex(Bg_Color_Black), 0);
    // lv_obj_set_style_bg_opa(img_grp, LV_OPA_COVER, 0);
    // lv_obj_set_style_border_width(img_grp, 1, 0);
    StatusBar.icon_single = lv_img_custom_create(img_grp, &icon_single_contact);
    StatusBar.icon_camera = lv_img_custom_create(img_grp, &icon_camera_comm);
    StatusBar.icon_lamp = lv_img_custom_create(img_grp, &icon_lamp_36);
    StatusBar.icon_sync = lv_img_custom_create(img_grp, &icon_front_curtain_36);
    StatusBar.icon_battery = lv_img_custom_create(img_grp, battery_images[0]);
    lv_obj_add_event_cb(StatusBar.icon_battery, battery_icon_delete_cb, LV_EVENT_DELETE, NULL);
    StatusBar.bat_timer = NULL;
    lv_obj_add_event_cb(StatusBar.icon_battery, battery_event_cb, LV_EVENT_VALUE_CHANGED, NULL);
    lv_event_send(StatusBar.icon_battery, LV_EVENT_VALUE_CHANGED, NULL);

    // lv_obj_update_layout(h_layout);
    LV_LOG("comp_status_bar_init\n");
    // lv_obj_set_style_bg_color(light_img, lv_color_hex(Main_Color), 0);
    // lv_obj_set_style_bg_opa(light_img, LV_OPA_COVER, 0);
    return h_layout;
}