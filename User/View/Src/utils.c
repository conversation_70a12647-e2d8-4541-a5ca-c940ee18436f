//
// Created by <PERSON><PERSON> on 2024/11/28.
//

#include "utils.h"
#include "multi_language.h"

char str_flash_level_M_decimals[91][5] = {"1.0", "1.1", "1.2", "1.3", "1.4", "1.5", "1.6", "1.7", "1.8", "1.9",
                                          "2.0", "2.1", "2.2", "2.3", "2.4", "2.5", "2.6", "2.7", "2.8", "2.9",
                                          "3.0", "3.1", "3.2", "3.3", "3.4", "3.5", "3.6", "3.7", "3.8", "3.9",
                                          "4.0", "4.1", "4.2", "4.3", "4.4", "4.5", "4.6", "4.7", "4.8", "4.9",
                                          "5.0", "5.1", "5.2", "5.3", "5.4", "5.5", "5.6", "5.7", "5.8", "5.9",
                                          "6.0", "6.1", "6.2", "6.3", "6.4", "6.5", "6.6", "6.7", "6.8", "6.9",
                                          "7.0", "7.1", "7.2", "7.3", "7.4", "7.5", "7.6", "7.7", "7.8", "7.9",
                                          "8.0", "8.1", "8.2", "8.3", "8.4", "8.5", "8.6", "8.7", "8.8", "8.9",
                                          "9.0", "9.1", "9.2", "9.3", "9.4", "9.5", "9.6", "9.7", "9.8", "9.9",
                                          "10.0"
};

char str_flash_level_M_units[10][4] = {"+0.1", "+0.2", "+0.3", "+0.4", "+0.5", "+0.6", "+0.7", "+0.8", "+0.9"};

// 获取能级最大值
uint8_t get_m_level_max() {
    return max_level[Setting.roller_values[roller_min_power] % MAX_FLASH_LEVEL_INDEX];
}

// 获取频闪能级范围
uint8_t get_multi_level_range() {
    // switch (max_level[Setting.roller_values[roller_min_power] % MAX_FLASH_LEVEL_INDEX]) {
    //     case 70:
    //         return 5;
    //     case 80:
    //         return 6;
    //     case 90:
    //         return 7;
    // }
    return max_level[Setting.roller_values[roller_min_power] % MAX_FLASH_LEVEL_INDEX] / 10 - 2;
}

// 获取频闪能级字符串
char *get_str_flash_level_Multi(uint8_t flash_level_Multi) {
    uint8_t level_max = get_m_level_max();
    char *flash_level_Multi_str = flash_level_M_text[flash_level_Multi + (90 - level_max) / 10];
    return flash_level_Multi_str;
}

// 获取手动档能级字符串
char *get_str_flash_level_M(uint8_t flash_level_M) {
    uint8_t level_max = get_m_level_max();
    // 取出十位，同时根据最大能级调整下标
    uint8_t tens_digit = (flash_level_M / 10) % 10 + (90 - level_max) / 10;
    LV_LOG("%d\n", level_max);
    char *flash_level_M_str = flash_level_M_text[tens_digit];
    return flash_level_M_str;
}

// 获取小数显示字符串
char *get_decimals_str_flash_level_M(uint8_t flash_level_M) {
    // static char decimal_str[4];
    // if (flash_level_M < get_m_level_max()) {
    //     // float level_num = (float) (flash_level_M + (100 - get_m_level_max())) / 10;
    //     uint8_t level_num = (flash_level_M + (100 - get_m_level_max()));
    //     uint8_t tens_digit = level_num / 10;
    //     uint8_t units_digit = level_num % 10;
    //     snprintf(decimal_str, 4, "%d.%d", tens_digit, units_digit);
    // } else {
    //     uint8_t level_num = (flash_level_M + (100 - get_m_level_max())) / 10;
    //     snprintf(decimal_str, 3, "%.d", level_num);
    // }
    flash_level_M +=
            (MAX_FLASH_LEVEL_INDEX - 1 - (Setting.roller_values[roller_min_power] % MAX_FLASH_LEVEL_INDEX)) * 10;
    return str_flash_level_M_decimals[flash_level_M];
}

// char *get_str_flash_sub_level_M(uint8_t flash_level_M) {
//     // 取出个位
//     uint8_t units_digit = flash_level_M % 10;
//     // // 为结果字符串分配内存，最大长度为6: "+0.9\0"
//     // char *flash_sub_level_M_str = malloc(6 * sizeof(char));
//     // if (flash_sub_level_M_str == NULL) {
//     //     LV_LOG("memory_fail\n");
//     //     // 内存分配失败
//     //     return NULL;
//     // }
//     static char flash_sub_level_M_str[6];
//     if (units_digit) {
//         sprintf(flash_sub_level_M_str, "+0.%d", units_digit);
//     } else {
//         // 如果个位为0，返回空字符串
//         flash_sub_level_M_str[0] = '\0';
//     }
//
//     return flash_sub_level_M_str;
// }

// 获取小档位字符串
void get_str_flash_sub_level_M(uint8_t flash_level_M, char *buffer, size_t buffer_size) {
    // 取出个位
    uint8_t units_digit = flash_level_M % 10;

    // 如果个位为非零，格式化到 buffer 中
    if (units_digit) {
        snprintf(buffer, buffer_size, "+0.%d", units_digit);
    } else {
        // 如果个位为0，返回空字符串
        buffer[0] = '\0';
    }
    // buffer = str_flash_level_M_units[units_digit];
}

// 获取各模式能级最大值
uint8_t get_flash_level_max(GroupStruct *grp) {
    uint8_t flash_level_max;
    if (grp->mode == mode_M) {
        flash_level_max = get_m_level_max();
    } else if (grp->mode == mode_TTL) {
        flash_level_max = MAX_FLASH_LEVEL_TTL;
    } else {
        flash_level_max = 0;
    }
    return flash_level_max;
}

// 获取对应组别当前的能级
uint8_t *get_flash_level(GroupStruct *grp) {
    static uint8_t OFF_value = 0;
    if (grp->mode == mode_M) {
        return &grp->flash_level_M;
    } else if (grp->mode == mode_TTL) {
        return &grp->flash_level_TTL;
    } else {
        return &OFF_value;
    }
}

// 获取TTL补偿能级
char *get_str_flash_level_TTL(uint8_t flash_level_TTL) {
    // 0为不显示，1为正号，2为负号
    uint8_t show_sign = 0;
    if (flash_level_TTL > ZERO_TTL) {
        show_sign = 1;
        flash_level_TTL = TTL_flash_level[flash_level_TTL - ZERO_TTL - 1];
    } else if (flash_level_TTL < ZERO_TTL) {
        show_sign = 2;
        flash_level_TTL = TTL_flash_level[ZERO_TTL - flash_level_TTL - 1];
    } else if (flash_level_TTL == ZERO_TTL) {
        show_sign = 0;
        flash_level_TTL = 0;
    }

    // 取出十位
    uint16_t tens_digit = (flash_level_TTL / 10) % 10;
    // 取出个位
    uint8_t units_digit = flash_level_TTL % 10;
    // 为结果字符串分配内存，最大长度为6: "+3.0\0"
    // char *flash_level_TTL_str = malloc(6 * sizeof(char));
    // if (flash_level_TTL_str == NULL) {
    //     LV_LOG("memory_fail\n");
    //     // 内存分配失败
    //     return NULL;
    // }
    static char flash_level_TTL_str[6];
    if (show_sign == 0) {
        sprintf(flash_level_TTL_str, "%d.%d", tens_digit, units_digit);
    } else if (show_sign == 1) {
        sprintf(flash_level_TTL_str, "+%d.%d", tens_digit, units_digit);
    } else if (show_sign == 2) {
        sprintf(flash_level_TTL_str, "-%d.%d", tens_digit, units_digit);
    }
    return flash_level_TTL_str;
}

// 模式颜色处理
void mode_label_color_handle(lv_obj_t *label, FlashModeEnum mode) {
    switch (mode) {
        case mode_OFF:
            lv_obj_set_style_text_color(label, lv_color_hex(OFF_Color), 0);
            break;
        case mode_M:
            lv_obj_set_style_text_color(label, lv_color_hex(M_Color), 0);
            break;
        case mode_TTL:
            lv_obj_set_style_text_color(label, lv_color_hex(TTL_Color), 0);
            break;
    }
}

// 步进0.3特殊处理
int16_t level_step_0_3_handle(int16_t offset, int16_t level) {
    if (Setting.roller_values[roller_power_step] == step_0_3) {
        // 获取个位数
        uint8_t ones = level % 10;
        // 获取十位数
        uint8_t tens = level / 10;
        // 加的时候个位大于7往前进位，大于3视为7，大于0视为3
        // 减的时候个位小于3的退位，小于7视为3，小于等于9视为7
        if (offset > 0) {
            if (ones > 7) {
                ones = 0;
                tens += 1;
            } else if (ones > 3) {
                ones = 7;
            } else if (ones > 0) {
                ones = 3;
            }
        } else if (offset < 0) {
            if (ones < 3) {
                ones = 0;
                // if (tens > 0) {
                //     tens -= 1;
                // }
            } else if (ones < 7) {
                ones = 3;
            } else if (ones <= 9) {
                ones = 7;
            }
        }
        level = tens * 10 + ones;
        LV_LOG("offset: %d level: %d\n", offset, level);
    }
    return level;
}

/**
 * @brief 转换0.1
 * @param x
 * @return
 */
int trans_0_1_level(const uint16_t x) {
    return 3 * x + (x + 1) / 3;
}

/**
 * @brief 转换0.3
 * @param y
 * @return
 */
int trans_0_3_level(const uint16_t y) {
    // 整数除法，相当于向下取整
    const uint8_t k = (y + 3) / 10;
    const uint8_t remainder = y % 10;
    if (remainder == 7) {
        return 3 * k - 1;
    } else if (remainder == 0) {
        return 3 * k;
    } else if (remainder == 3) {
        return 3 * k + 1;
    } else {
        // 处理无效y值，返回-1表示错误
        return -1;
    }
}

/**
 * @brief 能级传值限制
 * @param offset 触摸偏移
 * @param level 当前能级
 * @param level_min 最小能级
 * @param level_max 最大能级
 * @param step 步长
 * @return 返回新能级
 */
int16_t level_adjust_handle(int16_t offset, int16_t level, int16_t level_min, int16_t level_max, uint8_t step) {
    // 确保step为正数
    step = LV_ABS(step);

    if (offset > 0) {
        // 增加时，确保不会超过最大值
        level = LV_MIN(level + step, level_max);
    } else if (offset < 0) {
        // 减少时，确保不会低于最小值
        level = LV_MAX(level - step, level_min);
    }

    // 最终确保值在范围内
    level = LV_CLAMP(level_min, level, level_max);

    return level;
}

// 传值映射到滑动条
lv_coord_t cal_slider_width_by_level(uint16_t slider_width, uint8_t level, uint8_t range) {
    uint16_t final_width;
    // 避免被除数为0导致的崩溃
    if (range) {
        // 精度
        uint16_t formula_precision = 1000;
        // 宽度 * 精度 / 范围 * 当前能级 + 精度 / 2（四舍五入）)/精度
        final_width = (slider_width * formula_precision / range * level + formula_precision / 2) / formula_precision;
    } else {
        final_width = 0;
    }
    return (lv_coord_t) final_width;
}

void free_and_clear(void *str) {
    free(str);
    str = NULL;
}

// 释放用于结构体的动态内存
void free_struct_cb(lv_event_t *e) {
    // LV_LOG("del\n");
    lv_obj_t *obj = lv_event_get_target(e);
    if (lv_obj_get_user_data(obj) != NULL) {
        // LV_LOG("free\n");
        free_and_clear(lv_obj_get_user_data(obj));
        lv_obj_set_user_data(obj, NULL);
        obj = NULL;
    }
}

// 全局缓冲区用于convert_times_arr_to_str
char times_str_buffer[560]; // 根据实际需要调整大小

// 数组转换为roller初始化用的的字符串
char *convert_times_arr_to_str(uint8_t *times_arr, int arr_size) {
    for (int i = 0; i < 560; i++) {
        times_str_buffer[i] = 0;
    }
    times_str_buffer[0] = '\0';

    for (int i = 0; i < arr_size; i++) {
        char temp[5];
        snprintf(temp, sizeof(temp), "%d\n", times_arr[i]);
        strcat(times_str_buffer, temp);
    }

    // 移除最后一个换行符
    int len = strlen(times_str_buffer);
    if (len > 0 && times_str_buffer[len - 1] == '\n') {
        times_str_buffer[len - 1] = '\0';
    }
    LV_LOG("times_str_buffer content:\n%s\n", times_str_buffer);
    LV_LOG("times_str_buffer length: %d\n", len);

    return times_str_buffer;
}

// 二分查找
// 在有序数组 nums_arr 中查找目标值 target_num
// 不可设为无符号整数，在找不到时会出错（0-1 = 255）
int binary_search_index(const uint8_t *nums_arr, int arr_size, uint8_t target_num) {
    int left = 0, right = arr_size - 1;
    while (left <= right) {
        int mid = (left + right) / 2;
        if (nums_arr[mid] == target_num) {
            return mid;
        } else if (nums_arr[mid] > target_num) {
            right = mid - 1;
        } else if (nums_arr[mid] < target_num) {
            left = mid + 1;
        }
    }
    // 找不到时
    return left;
}

// 将 Version 转换为字符串
void version_to_string(const Version *ver, char *buffer, size_t buffer_size) {
    // 格式化字符串并存入 buffer
    // snprintf(buffer, buffer_size, "Version: V%d.%02d.%02d", ver->major, ver->minor, ver->patch);
    snprintf(buffer, buffer_size, "%sV%d.%d.%d", get_text(text_version), ver->major, ver->minor, ver->patch);
}

// 切换是否聚焦的状态
void my_group_set_focused_state(bool can_focused, PageTypeHandle *page) {
    if (!can_focused) {
        for (int i = 0; i < GROUP_OBJ_MAX; ++i) {
            if (page->group_obj[i] == NULL) {
                break;
            } else {
                lv_group_add_obj(page->indev_group, page->group_obj[i]);
            }
        }
        // lv_indev_set_group(lv_win32_encoder_device_object, Pages.Page_Home.indev_group);
    } else {
        lv_group_remove_all_objs(page->indev_group);
    }
}

// 模式切换
void mode_switch_handle(GroupStruct *grp) {
    // 尼康D组E组不能调到TTL
    // SHOOT不可以调到TTL
// #if ProductModel == QZ_N
//     if (grp->group_name >= group_D || Setting.values[setting_shoot]) {
//         if (grp->mode < mode_MAX) {
//             grp->mode++;
//         } else {
//             grp->mode = mode_M;
//         }
//     } else {
// #endif
    if (grp->mode < mode_MAX) {
        grp->mode++;
        if (grp->group_name >= group_D || Setting.values[setting_shoot]) {
            if (grp->mode == mode_TTL) {
                grp->mode++;
            }
        }
    } else {
        grp->mode = mode_MIN;
    }
// #if ProductModel == QZ_N
//     }
// #endif
}

// 造型灯模式切换
void lamp_mode_switch_handle(GroupStruct *grp) {
    if (grp->stylish_lamp_mode < lamp_manual) {
        grp->stylish_lamp_mode++;
    } else {
        grp->stylish_lamp_mode = lamp_off;
    }
}

/**
 * @brief uint8_t负数映射编码（0映射128）
 * @param value int8_t
 * @return uint8_t编码值
 */
uint8_t int8_2_uint8(int8_t value, uint8_t level_max) {
    return (uint8_t) (value + (level_max + 1) / 2);
}

/**
 * @brief int8_t映射编码（128映射0）
 * @param value int8_t
 * @return uint8_t编码值
 */
uint8_t uint8_2_int8(uint8_t value, uint8_t level_max) {
    return (int8_t) (value - (level_max + 1) / 2);
}

/**
 * @brief uint8_t负数映射解码（0映射-128）
 * @param value uint8_t
 * @return int8_t解码值
 */
int8_t decode_signed(uint8_t value) {
    return (int8_t) (value - 128);
}
