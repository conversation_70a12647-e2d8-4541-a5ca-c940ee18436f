//
// Created by <PERSON><PERSON> on 2024/6/15.
//

#include <stdio.h>
#include "page_home.h"
#include "comp_slider.h"
#include "comp_status_bar.h"
#include "comp_page_indicator.h"
#include "comp_border_focused.h"
#include "my_slider.h"
#include "utils.h"

lv_obj_t *handle_home_top, *drag_home_bottom, *handle_home_left, *drag_home_right;
lv_obj_t *status_bar;

static uint16_t pressing_count;
static bool is_short_clicked;

// //滑动回调
// static void slider_cb(lv_event_t *e) {
//     lv_obj_t *s1 = (lv_obj_t *) e->target;
//     LV_LOG("value=%d\n", lv_myslider_get_value(s1));//获取值
// }

/**
 * @brief 群控类型事件回调
 * @param e 事件
 */
static void ctrl_type_event_cb(lv_event_t *e) {
    lv_event_code_t code = lv_event_get_code(e);
    lv_obj_t *img_type = lv_event_get_target(e);
    if (code == LV_EVENT_PRESSING) {
        lv_point_t vect;
        lv_indev_get_vect(lv_indev_get_act(), &vect);
        // LV_LOG("vect.x: %d, vect.y: %d\n", vect.x, vect.y);
        pressing_count++;
        // LV_LOG("count_pressing: %d, ", pressing_count);
        if (LV_ABS(vect.x) < 2 && LV_ABS(vect.y) < 2 && pressing_count < Short_Click_Sensitivity - 2) {
            // LV_LOG("is_short_clicked\n");
            is_short_clicked = true;
        } else {
            // LV_LOG("not_short_clicked\n");
            is_short_clicked = false;
        }
    } else if (code == LV_EVENT_RELEASED) {
        pressing_count = 0;
    } else if (code == LV_EVENT_SHORT_CLICKED) {
        const uint8_t can_short_click = (uintptr_t) lv_event_get_param(e);
        if (can_short_click == 1) {
            is_short_clicked = true;
        }
        if (!is_short_clicked) {
            return;
        }
        Param.ctrl_type = !Param.ctrl_type;
        for (int i = 0; i < GroupItemCount; ++i) {
            // 避免操作关闭的组
            if (!Param.group[i].is_added_to_list) {
                continue;
            }
            SliderStruct *slider_struct;
            // 防止slider_struct->level_max指向为空导致的死机
            if (lv_obj_get_user_data(Param.group[i].slider_home.slider_bg) != NULL) {
                slider_struct = lv_obj_get_user_data(Param.group[i].slider_home.slider_bg);
            } else {
                LV_LOG_USER("continue slider_struct is NULL\n");
                continue;
            }
            if (Param.ctrl_type == type_flash) {
                slider_struct->level_type = level_flash;
            } else if (Param.ctrl_type == type_lamp) {
                slider_struct->level_type = level_lamp;
            }
            uint8_t from_ctrl_type = 1;
            LV_LOG_USER("from_ctrl_type = 1");
            lv_event_send(Param.group[i].slider_home.slider_bg, LV_EVENT_VALUE_CHANGED,
                          (void *) (uintptr_t) from_ctrl_type);
            lv_event_send(Param.group[i].slider_home.type_obj, LV_EVENT_VALUE_CHANGED, NULL);
        }
        lv_event_send(img_type, LV_EVENT_VALUE_CHANGED, NULL);
    } else if (code == LV_EVENT_VALUE_CHANGED) {
        if (Param.ctrl_type == type_lamp) {
            lv_img_set_src(img_type, &icon_lamp);
        } else {
            lv_img_set_src(img_type, &icon_flash);
        }
    }
}

// 群控程序
void grp_ctrl_pgr(OprValueEnum grp_ctrl) {
    int8_t offset_value = 0;
    if (grp_ctrl == opr_value_add) {
        offset_value = 1;
    } else if (grp_ctrl == opr_value_sub) {
        offset_value = -1;
    }
    for (int i = 0; i < GroupItemCount; ++i) {
        // 没有加入列表的不操作
        if (!Param.group[i].is_added_to_list) {
            continue;
        }
        if (Param.ctrl_type == type_flash) {
            if (Setting.values[setting_tcm] && Param.group[i].group_name <= group_C && Param.group[i].mode == mode_M) {
                Param.group[i].flash_level_M_tcm = (int8_t) level_adjust_handle(offset_value,
                    Param.group[i].flash_level_M_tcm,
                    MIN_FLASH_LEVEL_M_TCM_INT_8,
                    MAX_FLASH_LEVEL_M_TCM_INT_8, 1);
                slider_set_range(Param.group[i].slider_home.slider_bg, range_tcm);
                lv_slider_set_value(Param.group[i].slider_home.slider_bg,
                                    int8_2_uint8(Param.group[i].flash_level_M_tcm,MAX_FLASH_LEVEL_M_TCM), LV_ANIM_OFF);
                LV_LOG_USER("int8_2_uint8(Param.group[i].flash_level_M_tcm,MAX_FLASH_LEVEL_M_TCM) %d",
                            int8_2_uint8(Param.group[i].flash_level_M_tcm,MAX_FLASH_LEVEL_M_TCM));
            } else {
                uint8_t *flash_level = get_flash_level(&Param.group[i]);
                LV_LOG_USER("*flash_level1: %d", *flash_level);
                *flash_level = level_adjust_handle(offset_value, *flash_level, 0, get_flash_level_max(&Param.group[i]),
                                                   1);
                if (Param.group[i].mode == mode_M) {
                    if (Setting.roller_values[roller_power_step] == step_0_3) {
                        // 应用0.3步进处理
                        *flash_level = level_step_0_3_handle(offset_value, *flash_level);
                        slider_set_range(Param.group[i].slider_home.slider_bg, range_m_0_3);
                    } else {
                        slider_set_range(Param.group[i].slider_home.slider_bg, range_m);
                    }
                } else if (Param.group[i].mode == mode_TTL) {
                    slider_set_range(Param.group[i].slider_home.slider_bg, range_ttl);
                }
                lv_slider_set_value(Param.group[i].slider_home.slider_bg, *flash_level, LV_ANIM_OFF);
            }
        } else if (Param.ctrl_type == type_lamp) {
            if (Param.group[i].stylish_lamp_mode == lamp_manual) {
                Param.group[i].stylish_lamp_level = level_adjust_handle(offset_value, Param.group[i].stylish_lamp_level,
                                                                        MIN_LAMP_LEVEL, MAX_LAMP_LEVEL, 1);
            }
            lv_slider_set_value(Param.group[i].slider_home.slider_bg, Param.group[i].stylish_lamp_level, LV_ANIM_OFF);
        }
        uint8_t param = 1;
        lv_event_send(Param.group[i].slider_home.slider_bg, LV_EVENT_VALUE_CHANGED, (void *) (uintptr_t) param);
    }
}

// 群控增加
static void grp_add_event_cb(lv_event_t *e) {
    lv_event_code_t code = lv_event_get_code(e);
    if (code == LV_EVENT_SHORT_CLICKED || code == LV_EVENT_LONG_PRESSED_REPEAT) {
        grp_ctrl_pgr(opr_value_add);
    }
}

// 群控减少
static void grp_reduce_event_cb(lv_event_t *e) {
    lv_event_code_t code = lv_event_get_code(e);
    if (code == LV_EVENT_SHORT_CLICKED || code == LV_EVENT_LONG_PRESSED_REPEAT) {
        grp_ctrl_pgr(opr_value_sub);
    }
}

// 编码器操作
static void grp_ctrl_key_cb(lv_event_t *e) {
    char c = *((char *) lv_event_get_param(e));
    if (c == LV_KEY_ESC) {
        // 非编辑状态切到Multi页
        if (!lv_group_get_editing(lv_group_get_default())) {
            // 创建页面
            pm_creat_page(PageManager.main_page->pos[pos_left].page, PageManager.main_page, LV_ALIGN_OUT_LEFT_MID,
                          lv_color_black());
            page_click_anim(pos_left, &Pages.page[Page_Multi], anim_slide);
        } else {
            uint8_t can_short_click = 1;
            // 编辑状态切控制模式
            lv_event_send(Param.ctrl_type_img, LV_EVENT_SHORT_CLICKED, (void *) (uintptr_t) can_short_click);
        }
    } else if (c == LV_KEY_END) {
        // 创建页面
        pm_click_to_page_control_center();
    } else if (c == LV_KEY_LEFT) {
        grp_ctrl_pgr(opr_value_add);
    } else if (c == LV_KEY_RIGHT) {
        grp_ctrl_pgr(opr_value_sub);
    }
}

void grp_ctrl_layout(lv_obj_t *parent) {
    lv_obj_t *h_layout = lv_layout_custom_create(parent, LV_FLEX_FLOW_ROW);
    lv_obj_set_style_bg_opa(h_layout, LV_OPA_TRANSP, 0);
    lv_obj_set_size(h_layout, LV_PCT(100), 60);
    lv_obj_set_flex_align(h_layout, LV_FLEX_ALIGN_SPACE_AROUND, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_CENTER);

    lv_obj_t *img_reduce = lv_img_custom_create(h_layout, &icon_reduce_40);
    lv_obj_add_flag(img_reduce, LV_OBJ_FLAG_CLICKABLE);
    lv_obj_add_event_cb(img_reduce, grp_reduce_event_cb, LV_EVENT_ALL, NULL);
    lv_obj_set_style_pad_all(img_reduce, 10, 0);

    lv_obj_t *img_type = NULL;
    if (Param.ctrl_type == type_lamp) {
        img_type = lv_img_custom_create(h_layout, &icon_lamp);
    } else {
        img_type = lv_img_custom_create(h_layout, &icon_flash);
    }
    lv_obj_add_flag(img_type, LV_OBJ_FLAG_CLICKABLE);
    lv_obj_add_event_cb(img_type, ctrl_type_event_cb, LV_EVENT_ALL, NULL);
    lv_obj_set_ext_click_area(img_type, 10);
    Param.ctrl_type_img = img_type;

    lv_obj_t *img_add = lv_img_custom_create(h_layout, &icon_add_40);
    lv_obj_add_flag(img_add, LV_OBJ_FLAG_CLICKABLE);
    lv_obj_add_event_cb(img_add, grp_add_event_cb, LV_EVENT_ALL, NULL);
    lv_obj_set_style_pad_all(img_add, 10, 0);

    lv_obj_t *h_layout_border = lv_obj_custom_trans_create(h_layout, LV_PCT(82), 56);
    lv_obj_add_flag(h_layout_border, LV_OBJ_FLAG_IGNORE_LAYOUT);
    lv_obj_center(h_layout_border);
    lv_obj_add_event_cb(h_layout_border, grp_ctrl_key_cb, LV_EVENT_KEY, NULL);
    lv_obj_clear_flag(h_layout_border, LV_OBJ_FLAG_CLICKABLE);
    // TODO 频繁调用绘图事件
    border_focused_obj_init(h_layout_border, &Pages.page[Page_Home], Focused_Editing_Mode | Focused_Opr_Parent);
}

#define TEST_SLIDER 0
#if TEST_SLIDER
static uint16_t pressing_count;
static bool is_short_clicked;

void my_slider_cb(lv_event_t *e) {
    lv_event_code_t code = lv_event_get_code(e);
    lv_obj_t *slider = lv_event_get_target(e);
    // if (code == LV_EVENT_PRESSED ||
    //     code == LV_EVENT_PRESSING ||
    //     code == LV_EVENT_RELEASED ||
    //     code == LV_EVENT_KEY) {
    //     lv_event_stop_bubbling(e);
    // }
    // LV_LOG_USER("is_sliding %d",is_sliding);
    if (code == LV_EVENT_PRESSING) {
        LV_LOG_USER("my_slider_cb LV_EVENT_PRESSING");
        lv_point_t vect;
        lv_indev_get_vect(lv_indev_get_act(), &vect);
        LV_LOG("vect.x: %d, vect.y: %d\n", vect.x, vect.y);
        pressing_count++;
        LV_LOG("count_pressing: %d, ", pressing_count);
        if (LV_ABS(vect.x) < 2 && LV_ABS(vect.y) < 2 && pressing_count < 5) {
            LV_LOG("is_short_clicked\n");
            is_short_clicked = true;
        } else {
            LV_LOG("not_short_clicked\n");
            is_short_clicked = false;
        }
    } else if (code == LV_EVENT_RELEASED) {
        pressing_count = 0;
        LV_LOG_USER("my_slider_cb LV_EVENT_RELEASED");
    } else if (code == LV_EVENT_VALUE_CHANGED) {
        LV_LOG_USER("slider %d", lv_slider_get_value(slider));
        LV_LOG_USER("my_slider_cb LV_EVENT_VALUE_CHANGED");
    } else if (code == LV_EVENT_CLICKED) {
        LV_LOG_USER("my_slider_cb LV_EVENT_CLICKED");
    } else if (code == LV_EVENT_SHORT_CLICKED) {
        if (!is_short_clicked) {
            LV_LOG_USER("----------my_slider_cb LV_EVENT_SHORT_CLICKED return");
            return;
        }
        LV_LOG_USER("my_slider_cb LV_EVENT_SHORT_CLICKED");
    }
}
#endif


void page_home_init(lv_obj_t *page) {
#if TEST_SLIDER
    // lv_obj_t *my_slider = my_slider_create(page);
    lv_obj_t *my_slider = lv_obj_create(page);
    lv_obj_center(my_slider);
    // lv_obj_set_style_bg_opa(my_slider, 0, LV_PART_KNOB);
    // lv_group_remove_obj(my_slider);
    // lv_slider_set_range(my_slider, -100, 0);
    // // 聚焦边框创建
    // border_focused_obj_init(my_slider, &Pages.page[Page_Home],
    //                         Focused_Editing_Mode | Focused_Scroll_Parent | Focused_Opr_Parent);
    lv_obj_add_event_cb(my_slider, my_slider_cb, LV_EVENT_ALL, NULL);
    return;
#endif

    LV_LOG_USER("page_home_init\n");
    // 状态栏不存在则创建
    if (!lv_obj_is_valid(status_bar)) {
        LV_LOG_USER("status_bar is invalid\n");
        status_bar = NULL;
        status_bar = comp_status_bar_init();
        update_status_bar();
        lv_obj_set_parent(status_bar, page);
    }
    lv_obj_t *v_layout = lv_layout_custom_create(page, LV_FLEX_FLOW_COLUMN);
    lv_obj_set_style_pad_top(v_layout, status_bar_h + 10, 0);
    lv_obj_set_size(v_layout, LV_PCT(100), LV_PCT(100));
    lv_obj_set_style_bg_opa(v_layout, LV_OPA_TRANSP, 0);
    lv_obj_set_style_pad_gap(v_layout, 8, 0);
    lv_obj_set_style_pad_hor(v_layout, 18, 0);
    lv_obj_set_flex_align(v_layout, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    // lv_obj_set_scroll_snap_y(v_layout, LV_SCROLL_SNAP_START);
    // lv_obj_set_style_border_width(v_layout, 1, 0);

    // 滑动条组
    slider_layout_home(v_layout);

    // 页面指示器
    comp_page_indicator(v_layout, LV_FLEX_FLOW_ROW_REVERSE);

    // 群控
    grp_ctrl_layout(v_layout);

    // 添加下拉手柄
    // pm_create_handle(drag_home_right, page, pos_right);
    handle_home_left = pm_create_handle(page, pos_left);
    handle_home_top = pm_create_handle(page, pos_top);
    // pm_create_handle(drag_home_bottom, page, pos_bottom);
    //
    // lv_obj_t *slider_txt = lv_myslider_get_text(s1);//获取文本对象
    // // lv_obj_set_style_text_opa(slider_txt, 0, 0);//设置透明度为0可以隐藏
    //
    // lv_obj_add_event_cb(s1, slider_cb, LV_EVENT_VALUE_CHANGED, 0);//设置回调


    // lv_obj_t *label = lv_label_create(page);
    // lv_label_set_text(label, "Home");
    // lv_obj_align(label, LV_ALIGN_TOP_MID, 0, 20);
    // lv_obj_set_style_text_color(label, lv_color_hex(0xFFFF00), 0); // 设置文字颜色为白色
    // lv_obj_set_style_text_font(label, &lv_font_montserrat_12, 0);

    // lv_group_focus_obj(Pages.Page_Home.group_obj[0]);
    // lv_obj_clear_state(Pages.Page_Home.group_obj[0], LV_STATE_FOCUSED);
    // lv_group_remove_all_objs(Pages.Page_Home.indev_group);
    // for (int i = 0; i < GROUP_OBJ_MAX; ++i) {
    //     if (Pages.Page_Home.group_obj[i] == NULL) {
    //         break;
    //     } else {
    //         lv_group_add_obj(Pages.Page_Home.indev_group, Pages.Page_Home.group_obj[i]);
    //     }
    // }
    // lv_indev_set_group(lv_win32_encoder_device_object, Pages.Page_Home.indev_group);

}
