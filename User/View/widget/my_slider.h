/**
 * @file my_slider.h
 *
 */

#ifndef MY_SLIDER_H
#define MY_SLIDER_H

#ifdef __cplusplus
extern "C" {
#endif

/*********************
 *      INCLUDES
 *********************/
#include "src/widgets/lv_slider.h"

//#if MY_USE_SLIDER != 0

/*Testing of dependencies*/
#if LV_USE_BAR == 0
#error "lv_slider: lv_bar is required. Enable it in lv_conf.h (MY_USE_BAR 1)"
#endif

/*********************
 *      DEFINES
 *********************/

/**********************
 *      TYPEDEFS
 **********************/

 
typedef struct{
    lv_slider_t bar;                 /**< Add the ancestor's type first */
    int32_t drag_start_value;   // 拖拽时 knob 的起始值
    lv_point_t drag_start_point; // 拖拽时起始点
}my_slider_t;

extern const lv_obj_class_t my_slider_class;

/**********************
 * GLOBAL PROTOTYPES
 **********************/

/**
 * Create a custom slider object
 * @param parent    pointer to an object, it will be the parent of the new custom slider.
 * @return          pointer to the created custom slider
 */
lv_obj_t * my_slider_create(lv_obj_t * parent);

/*=====================
 * Setter functions
 *====================*/

/**
 * Set a new value on the custom slider
 * @param obj       pointer to a custom slider object
 * @param value     the new value
 * @param anim      MY_ANIM_ON: set the value with an animation; MY_ANIM_OFF: change the value immediately
 */
void my_slider_set_value(lv_obj_t * obj, int32_t value, lv_anim_enable_t anim);

/**
 * Set a new value for the left knob of a custom slider
 * @param obj       pointer to a custom slider object
 * @param value     new value
 * @param anim      MY_ANIM_ON: set the value with an animation; MY_ANIM_OFF: change the value immediately
 */
void my_slider_set_left_value(lv_obj_t * obj, int32_t value, lv_anim_enable_t anim);

/**
 * Set minimum and the maximum values of a bar
 * @param obj       pointer to the custom slider object
 * @param min       minimum value
 * @param max       maximum value
 */
void my_slider_set_range(lv_obj_t * obj, int32_t min, int32_t max);

/**
 * Set the mode of custom slider.
 * @param obj       pointer to a custom slider object
 * @param mode      the mode of the custom slider. See ::lv_slider_mode_t
 */
void my_slider_set_mode(lv_obj_t * obj, lv_slider_mode_t mode);

/*=====================
 * Getter functions
 *====================*/

/**
 * Get the value of the main knob of a custom slider
 * @param obj       pointer to a custom slider object
 * @return          the value of the main knob of the custom slider
 */
int32_t my_slider_get_value(const lv_obj_t * obj);

/**
 * Get the value of the left knob of a custom slider
 * @param obj       pointer to a custom slider object
 * @return          the value of the left knob of the custom slider
 */
int32_t my_slider_get_left_value(const lv_obj_t * obj);

/**
 * Get the minimum value of a custom slider
 * @param obj       pointer to a custom slider object
 * @return          the minimum value of the custom slider
 */
int32_t my_slider_get_min_value(const lv_obj_t * obj);

/**
 * Get the maximum value of a custom slider
 * @param obj       pointer to a custom slider object
 * @return          the maximum value of the custom slider
 */
int32_t my_slider_get_max_value(const lv_obj_t * obj);

/**
 * Give the custom slider is being dragged or not
 * @param obj       pointer to a custom slider object
 * @return          true: drag in progress false: not dragged
 */
bool my_slider_is_dragged(const lv_obj_t * obj);

///**
// * Get the mode of the custom slider.
// * @param custom slider       pointer to a custom slider object
// * @return          see ::lv_slider_mode_t
// */
//lv_slider_mode_t my_slider_get_mode(lv_obj_t * slider);

/**
 * Give the custom slider is in symmetrical mode or not
 * @param obj       pointer to custom slider object
 * @return          true: in symmetrical mode false : not in
*/
bool my_slider_is_symmetrical(lv_obj_t * obj);

/**********************
 *      MACROS
 **********************/

//#endif /*MY_USE_SLIDER*/

#ifdef __cplusplus
} /*extern "C"*/
#endif

#endif /*MY_SLIDER_H*/
