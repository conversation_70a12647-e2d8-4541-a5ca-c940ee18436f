//
// Created by <PERSON><PERSON> on 2024/11/28.
//

#ifndef QZ_N_UTILS_H
#define QZ_N_UTILS_H

#include <stdio.h>
#include "tinyprintf.h"
#include <stdlib.h>
#include "lvgl.h"
#include "user.h"

char *get_str_flash_level_M(uint8_t flash_level_M);

char *get_decimals_str_flash_level_M(uint8_t flash_level_M);

void get_str_flash_sub_level_M(uint8_t flash_level_M, char *buffer, size_t buffer_size);

char *get_str_flash_level_TTL(uint8_t flash_level_TTL);

void free_and_clear(void *str);

void free_struct_cb(lv_event_t *e);

uint8_t *get_flash_level(GroupStruct *grp);

uint8_t get_flash_level_max(GroupStruct *grp);

void mode_label_color_handle(lv_obj_t *label, FlashModeEnum mode);

int16_t level_step_0_3_handle(int16_t offset, int16_t level);

int16_t level_adjust_handle(int16_t offset, int16_t level, int16_t level_min, int16_t level_max, uint8_t step);

lv_coord_t cal_slider_width_by_level(uint16_t slider_width, uint8_t level, uint8_t range);

char *get_str_flash_level_Multi(uint8_t flash_level_Multi);

char *convert_times_arr_to_str(uint8_t *times_arr, int arr_size);

int binary_search_index(const uint8_t *nums_arr, int arr_size, uint8_t target_num);

void my_group_set_focused_state(bool can_focused, PageTypeHandle *page);

void mode_switch_handle(GroupStruct *grp);

void lamp_mode_switch_handle(GroupStruct *grp);

void version_to_string(const Version *ver, char *buffer, size_t buffer_size);

uint8_t get_m_level_max();

uint8_t get_multi_level_range();

uint8_t int8_2_uint8(int8_t value, uint8_t level_max);

uint8_t uint8_2_int8(uint8_t value, uint8_t level_max);

int8_t decode_signed(uint8_t value);

int trans_0_1_level(uint16_t x);

int trans_0_3_level(uint16_t y);

#endif //QZ_N_UTILS_H
