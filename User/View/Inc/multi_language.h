//
// Created by <PERSON><PERSON> on 2025/3/23.
//

#ifndef UI_QZ_N_MULTI_LANGUAGE_H
#define UI_QZ_N_MULTI_LANGUAGE_H

// 多语言枚举类型
typedef enum {
    // 中文
    lang_ch,
    // 英文
    lang_en,
    Lang_Count,
    // 德语
    lang_de,
    // 西班牙语
    lang_es,
    // 意大利语
    lang_it,
    // 法语
    lang_fr,
    // 俄语
    lang_ru,
    // 荷兰语
    lang_ne,
    // 日语
    lang_jp,
    // 语言总数
    // Lang_Count
} LanguageEnum;

// 文本标识符枚举，用于识别不同的文本项
typedef enum {
    // 距离选择提示
    text_distance_warning,
    // 充电提示音提示
    text_beep_info,
    // 最小功率
    text_min_power,
    // 步进文本
    text_step,
    // 扫描频道
    text_scan_channel,
    // 频道
    text_channel,
    // 识别码
    text_id,
    // 无线同步
    text_wireless_sync,
    // 前帘同步
    text_front_curtain_sync,
    // 后帘同步
    text_rear_curtain_sync,
    // 高速同步
    text_high_speed_sync,
    // 待机
    text_standby,
    // 关机
    text_shutdown,
    // 自动焦距
    text_auto,
    // 无线同步提示
    text_wireless_sync_info,
    // 恢复出厂设置
    text_factory_reset,
    // 恢复出厂设置警告
    text_factory_reset_warning,
    // 恢复中
    text_factory_resetting,
    // 确认
    text_confirm,
    // 取消
    text_cancel,
    // 待机选项
    text_standby_option,
    // 关机选项
    text_shutdown_option,
    // 识别码选项
    text_id_option,
    // 解锁提示
    text_unlock_info,
    // 菜单
    text_menu,
    // 更多组别
    text_more,
    // 亮度
    text_brightness,
    // 版本
    text_version,
    // 型号
    text_model,
    // 重置
    text_reset,
    // 关闭
    text_off,
    // 语言
    text_language,
    // 文本项总数
    Text_Count
} TextIdEnum;

// 初始化多语言系统
void multi_language_init(void);

// 通过文本ID获取当前语言的文本
char *get_text(TextIdEnum text_id);

// 切换语言
void set_language(LanguageEnum lang);

// 获取当前语言
LanguageEnum get_current_language(void);

#endif //UI_QZ_N_MULTI_LANGUAGE_H
