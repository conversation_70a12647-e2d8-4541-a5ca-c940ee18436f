//
// Created by <PERSON><PERSON> on 2024/8/1.
//

#ifndef LV_PORT_WIN_CODEBLOCKS_V8_COMP_GRP_SWITCH_H
#define LV_PORT_WIN_CODEBLOCKS_V8_COMP_GRP_SWITCH_H

#include "lvgl_custom_function.h"
#include "user.h"

#define POINT_GAP   12

typedef enum {
    switch_home,
    switch_multi
} SwitchEnumType;

typedef struct {
    GroupStruct *group;
    SwitchEnumType switch_type;
} SwitchStructType;

void comp_group_list_multi(lv_obj_t *parent);

void comp_group_list_home(lv_obj_t *parent);

lv_obj_t *comp_color_point_init(lv_obj_t *parent, GroupNameEnum group_name);

#endif //LV_PORT_WIN_CODEBLOCKS_V8_COMP_GRP_SWITCH_H
