//
// Created by <PERSON><PERSON> on 2024/7/2.
//

#ifndef LV_PORT_WIN_CODEBLOCKS_V8_COMP_SLIDER_H
#define LV_PORT_WIN_CODEBLOCKS_V8_COMP_SLIDER_H

#include "user.h"

extern const lv_coord_t opt_slider_layout_height;

typedef enum {
    slider_event_none,
    slider_event_slider,
    slider_event_list
} SliderEventEnum;

typedef enum {
    range_default,
    range_m,
    range_m_0_3,
    range_ttl,
    range_tcm,
    range_lamp,
    range_zoom,
    range_multi,
    range_brightness
} SliderRangeEnum;

void slider_set_range(lv_obj_t *slider, SliderRangeEnum slider_range_type);

void slider_layout_home(lv_obj_t *parent);

lv_obj_t *slider_layout_grp_page(lv_obj_t *page, GroupStruct *grp);

SliderObjStruct comp_group_slider_init(lv_obj_t *parent_layout, GroupStruct *group);

SliderObjStruct comp_option_slider_init(lv_obj_t *parent, const void *img_src, GroupStruct *group);

#endif //LV_PORT_WIN_CODEBLOCKS_V8_COMP_SLIDER_H
