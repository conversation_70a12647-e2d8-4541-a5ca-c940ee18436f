//
// Created by <PERSON><PERSON> on 2024/6/14.
//

#ifndef LV_PORT_WIN_CODEBLOCKS_V8_USER_H
#define LV_PORT_WIN_CODEBLOCKS_V8_USER_H

// #include "my_main.h"
#include <stdio.h>
#include <stdlib.h>
#include "lvgl.h"
#include "lvgl_custom_function.h"
#include "page_home.h"
#include "page_control_center.h"
#include "page_group_info.h"
#include "page_welcome.h"
#include "page_manager.h"
#include "page_multi.h"
#include "page_charging.h"

#define QZ_N 1
#define QZ_F 0

#define ProductModel QZ_N

#define LAMP_PROP 0

#define MODULE_BT 0

typedef struct {
    // 高8位主版本号
    uint8_t major;
    // 中8位次版本号
    uint8_t minor;
    // 低8位修订号
    uint8_t patch;
} Version;
extern Version Ver;

#if ProductModel == QZ_N
#define MAX_FLASH_LEVEL_TTL 18
#elif ProductModel == QZ_F
#define MAX_FLASH_LEVEL_TTL 30
#endif
#define ZERO_TTL (MAX_FLASH_LEVEL_TTL / 2)
#define MAX_FLASH_LEVEL_INDEX   3
#define MAX_FLASH_LEVEL_M_TCM 60
#define MAX_FLASH_LEVEL_M_TCM_INT_8  30
#define MIN_FLASH_LEVEL_M_TCM_INT_8  (-MAX_FLASH_LEVEL_M_TCM_INT_8)
// #define MAX_FLASH_LEVEL_MULTI   6
#define MIN_LAMP_LEVEL  1
#define MAX_LAMP_LEVEL  10
#define MAX_ZOOM_LEVEL  9
#define MIN_BRIGHTNESS_LEVEL    1
#define MAX_BRIGHTNESS_LEVEL    10
#define Setting_Item_List_MAX 10
#define CH_MAX 32

#define RADIUS_DEFAULT 32

extern char *group_text[];
extern char *mode_text[];
extern char *flash_level_M_text[];
extern char *zoom_level_text[];
#if ProductModel == QZ_F
extern char *zoom_aps_level_text[];
#endif
extern uint8_t multi_times_arr[];
extern uint8_t multi_hz_arr[];
extern uint8_t times_arr_size;
extern uint8_t hz_arr_size;
extern const uint8_t TTL_flash_level[];
extern uint8_t max_level[];

typedef enum {
    Ref_None = 0x0000,
    Ref_Level_Hz_Times = 0x0001,
    Ref_Mode = 0x0002,
    Ref_Multi_Home = 0x0004,
    Ref_Zoom = 0x0008,
} RefreshEnum;

typedef enum {
    RF_ref_none = 0x0000,
    RF_zoom_0x5A = 0x0001,
    RF_mode_level_0x8B = 0x0002,
    RF_Multi_param_0x8D = 0x0004,
    RF_lamp_beep_0x8A = 0x0008,
} RFSendEnum;

typedef enum {
    group_A,
    group_B,
    group_C,
    group_D,
    group_E,
    GroupItemCount
} GroupNameEnum;

typedef enum {
    mode_MIN,
    mode_OFF = mode_MIN,
    mode_TTL,
    mode_M,
    mode_MAX = mode_M,
} FlashModeEnum;

typedef enum {
    slider_home,
    slider_option
} SliderTypeEnum;

typedef enum {
    level_flash,
    level_lamp,
    level_zoom
} LevelTypeEnum;

typedef enum {
    type_flash,
    type_lamp
} CtrlTypeEnum;

typedef enum {
    ft_slider_multi,
    ft_slider_brightness
} FineTuningSliderEnum;

typedef enum {
    lamp_off,
#if LAMP_PROP
    lamp_prop,
#endif
    lamp_manual
} LampModeEnum;

typedef enum {
    battery_normal,
    battery_charging,
    battery_complete,
    battery_low,
    battery_off
} BatteryStatus;

typedef struct {
    lv_obj_t *type_obj, *slider_bg, *grp_label, *mode_label, *level_label, *span_group, *mask, *tcm_obj;
    lv_span_t *span_group_level, *span_group_sub_level;
} SliderObjStruct;

typedef struct {
    lv_obj_t *mode_label, *lamp_img;
} TypeObjStruct;

typedef struct {
    GroupNameEnum group_name;
    bool is_added_to_list;
    FlashModeEnum mode;
    bool is_turn_on_Multi;
    // bool is_OFF_mode;
    uint8_t flash_level_M;
    uint8_t flash_level_M_last;
    // (-3.0 ~ +3.0)(0x62~0x9E)
    int8_t flash_level_M_tcm;
    uint8_t flash_level_TTL;

    LampModeEnum stylish_lamp_mode;
    // bool stylish_lamp_switch;
    // bool stylish_lamp_prop;
    uint8_t stylish_lamp_level;

    uint8_t zoom_level;
    uint8_t auto_zoom_level;
    bool is_auto_zoom;

    SliderObjStruct slider_home;
    SliderObjStruct slider_option_flash_level;
    SliderObjStruct slider_option_lamp_level;
    SliderObjStruct slider_option_zoom_level;
    lv_obj_t *slider_grp_layout;
    lv_obj_t *mode_layout;
    lv_obj_t *home_switch;
    lv_obj_t *multi_switch;
    lv_tileview_tile_t *tile_gpr;
    lv_obj_t *icon_tile_up;
    lv_obj_t *icon_tile_down;
} GroupStruct;

typedef struct {
    uint8_t flash_level;
    uint8_t flash_times;
    uint8_t flash_freq;
    uint8_t max_times;
    lv_obj_t *label_flash_level;
    lv_obj_t *times_roller;
    lv_obj_t *hz_roller;
    lv_obj_t *slider;
} MultiStruct;

typedef struct {
    uint8_t level;
    lv_obj_t *label_level;
    lv_obj_t *slider;
} BrightnessStruct;

typedef enum {
    dist_0_10,
    dist_1_100,
    Dist_Item_Count,
} DistEnum;

typedef enum {
    sync_front,
// #if ProductModel != QZ_N
//     sync_rear,
// #endif
    sync_high_speed,
    Sync_Item_Count,
    sync_rear,
} SyncEnum;

#if ProductModel == QZ_F
typedef enum {
    zoom_aps,
    zoom_135,
    Zoom_Disp_Item_Count,
} ZoomDispEnum;
#endif

// typedef enum {
//     disp_fractions,
//     disp_decimals,
//     Disp_Item_Count
// } DispTypeEnum;
typedef enum {
    step_0_1,
    step_0_3,
    Power_Step_Count
} PowerStepEnum;

typedef enum {
    beep_on,
    beep_off,
    Beep_Item_Count
} BeepEnum;

// Prontor-Compur connection
typedef enum {
    pc_in,
    pc_out,
    PC_Item_Count
} PCEnum;

typedef enum {
    Zoom_Item_Count = 10,
    zoom_auto = 0xFF,
    // TODO 只开中英文
    Language_Item_Count = 2
} GridItemsEnum;

typedef enum {
    setting_rf,
    setting_dist,
#if ProductModel == QZ_N
    setting_sync,
#elif ProductModel == QZ_F
    setting_zoom_disp,
#endif
    setting_beep,
    setting_zoom,
    setting_tcm,
    setting_shoot,
    // setting_2_5mm,
    setting_screen,
#if MODULE_BT
    setting_bluetooth,
#endif
    setting_disp_type,
    setting_language,
    setting_reset,
    SettingItemCount,
#if ProductModel == QZ_F
    // 仅用于初始化生成aps九宫格页
    setting_zoom_aps,
#endif

    setting_lock,
    setting_lamp,
} SettingEnum;

typedef enum {
    roller_setting_ch,
    roller_setting_id,
    roller_setting_standby,
    roller_setting_off,
    roller_min_power,
    roller_power_step,
    RollerItemCount
} RollerSettingTypeEnum;

typedef struct {
    uint8_t values[SettingItemCount];
    uint8_t last_sync_value;
    uint8_t last_camera_link_value;
    SettingEnum selected_item;

    // 群控焦距
    uint8_t zoom;

    bool lock;
    // 造型灯总开关
    bool lamp_main_switch;

    // 扫描出来的组别
    uint8_t scan_ch_arr[CH_MAX];
    // 滚动选择器对应值
    uint8_t roller_values[RollerItemCount];
    uint8_t last_min_power;
    bool is_loading_mask_valid;
} SettingStruct;

extern SettingStruct Setting;

typedef enum {
    opt_ctrl_center,
    opt_setting
} OptionEnum;

typedef struct {
    SettingEnum setting_type;
    char *str;
    const void *img_src;
    const bool had_switch;
    OptionEnum option_type;
    lv_obj_t *obj_bg;
    lv_obj_t *obj_img;
} OptionStruct;

typedef struct {
    SettingEnum setting_type;
    uint8_t own_id;
    lv_obj_t *obj_bg;
    lv_obj_t *obj_title_bg;
    lv_obj_t *label_title;
    lv_obj_t *label;
    lv_obj_t *img_title;
    lv_obj_t *img;
} SubItemStruct;

typedef struct {
    // 每个页面最多有Setting_Item_List_MAX个item(目前最大为九宫格布局的10个)
    SubItemStruct sub_item[Setting_Item_List_MAX];
    // 对应页面上的标题
    lv_obj_t *grid_title_bg;
} SettingItemStruct;

typedef struct {
    // 每一个子页
    SettingItemStruct item[SettingItemCount];
    // 滚动选择器
    lv_obj_t *roller[RollerItemCount];

    lv_anim_t rotate_img_anim;
    lv_obj_t *mask;
} SettingObjsStruct;

extern SettingObjsStruct SettingObjs;

typedef struct {
    uint16_t level_max;
    SliderTypeEnum slider_type;
    LevelTypeEnum level_type;
    GroupStruct *group;
} SliderStruct;

typedef struct PageNode PageTypeHandle;

typedef enum {
    Page_Home,
    Page_Control_Center,
    Page_Group_Info,
    Page_Welcome,
    Page_Multi,
    Page_Group_Switch,
    Page_Setting,
    Page_Setting_Detail,
    Page_Pop,
    PageItemCount
} PageNameEnum;

typedef struct {
    // TODO PageTypeHandle内的上下左右也改
    PageTypeHandle page[PageItemCount];
    uint8_t key_long_press;
    bool is_long_pressing;

    lv_obj_t *lock_mask;
    lv_obj_t *ch_layout;
} PagesStruct;

extern PagesStruct Pages;

typedef struct {
    lv_obj_t *ch_label;
    lv_obj_t *icon_single;
    lv_obj_t *icon_camera;
    lv_obj_t *icon_lamp;
    lv_obj_t *icon_sync;
    lv_obj_t *icon_battery;
    lv_timer_t *bat_timer;
} StatusBarStruct;

extern StatusBarStruct StatusBar;

typedef struct {
    char *product_model;
    GroupStruct group[GroupItemCount];
    MultiStruct multi;
    lv_obj_t *grp_tile_view;
    lv_obj_t *ctrl_type_img;
    lv_obj_t *more_label_obj;
    CtrlTypeEnum ctrl_type;
    BrightnessStruct brightness;

    BatteryStatus battery_status;
    BatteryStatus last_battery_status;
    uint8_t electricity_level;
    uint8_t last_electricity_level;

    // 判断是频闪页还是主页
    bool is_Multi_page;
    SyncEnum status_bar_sync;

    bool is_charging_page;
    bool is_wireless_sync;

    lv_obj_t *standby_obj;
    bool is_standby_page;
    bool is_grp_color_on;

    // 相机控制闪光灯刷新参数
    RefreshEnum ref_param;
    bool ref_to_multi;

    // RF发送
    uint8_t RF_send_ref;

    // 当前开启组别数
    uint8_t grp_count;
} ParamStruct;

extern ParamStruct Param;

// typedef enum {
//     MY_KEY_UP        = 17,  /*0x11*/
//     MY_KEY_DOWN      = 18,  /*0x12*/
//     MY_KEY_RIGHT     = 19,  /*0x13*/
//     MY_KEY_LEFT      = 20,  /*0x14*/
//     MY_KEY_ESC       = 27,  /*0x1B*/
//     MY_KEY_DEL       = 127, /*0x7F*/
//     MY_KEY_BACKSPACE = 8,   /*0x08*/
//     MY_KEY_ENTER     = 10,  /*0x0A, '\n'*/
//     MY_KEY_NEXT      = 9,   /*0x09, '\t'*/
//     MY_KEY_PREV      = 11,  /*0x0B, '*/
//     MY_KEY_HOME      = 2,   /*0x02, STX*/
//     MY_KEY_END       = 3,   /*0x03, ETX*/
//     // MY_KEY_ENTER_LONG_PRESSED =
// } MyKeyEnum;

void user_main();

#endif //LV_PORT_WIN_CODEBLOCKS_V8_USER_H
