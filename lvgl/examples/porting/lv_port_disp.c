/**
 * @file lv_port_disp_templ.c
 *
 */

/*Copy this file as "lv_port_disp.c" and set this value to "1" to enable content*/
#if 1

/*********************
 *      INCLUDES
 *********************/
#include "lv_port_disp.h"
#include "amoled_qspi.h"
#include <stdbool.h>

/*********************
 *      DEFINES
 *********************/
#ifndef MY_DISP_HOR_RES
#warning Please define or replace the macro MY_DISP_HOR_RES with the actual screen width, default value 320 is used for now.
#define MY_DISP_HOR_RES    320
#endif

#ifndef MY_DISP_VER_RES
#warning Please define or replace the macro MY_DISP_HOR_RES with the actual screen height, default value 240 is used for now.
#define MY_DISP_VER_RES    240
#endif

/**********************
 *      TYPEDEFS
 **********************/

/**********************
 *  STATIC PROTOTYPES
 **********************/
static void disp_init(void);

static void disp_flush(lv_disp_drv_t *disp_drv, const lv_area_t *area, lv_color_t *color_p);
//static void gpu_fill(lv_disp_drv_t * disp_drv, lv_color_t * dest_buf, lv_coord_t dest_width,
//        const lv_area_t * fill_area, lv_color_t color);

/**********************
 *  STATIC VARIABLES
 **********************/

/**********************
 *      MACROS
 **********************/

/**********************
 *   GLOBAL FUNCTIONS
 **********************/

// 解决局部刷新错乱bug
// https://blog.csdn.net/Jinbxf/article/details/130724784
void example_rounder_cb(lv_disp_drv_t *disp_drv, lv_area_t *area) {
    if (area->x1 % 2 != 0) {
        area->x1 += 1;
    }
    if (area->y1 % 2 != 0) {
        area->y1 += 1;
    }

    uint32_t width = (area->x2 - area->x1 + 1);
    uint32_t height = (area->y2 - area->y1 + 1);
    if (width % 2 != 0) {
        area->x2 -= 1;
    }
    if (height % 2 != 0) {
        area->y2 -= 1;
    }
}


void lv_port_disp_init(void) {
    /*-------------------------
     * Initialize your display
     * -----------------------*/
    disp_init();

    /*-----------------------------
     * Create a buffer for drawing
     *----------------------------*/

    /**
     * LVGL requires a buffer where it internally draws the widgets.
     * Later this buffer will passed to your display driver's `flush_cb` to copy its content to your display.
     * The buffer has to be greater than 1 display row
     *
     * There are 3 buffering configurations:
     * 1. Create ONE buffer:
     *      LVGL will draw the display's content here and writes it to your display
     *
     * 2. Create TWO buffer:
     *      LVGL will draw the display's content to a buffer and writes it your display.
     *      You should use DMA to write the buffer's content to the display.
     *      It will enable LVGL to draw the next part of the screen to the other buffer while
     *      the data is being sent form the first buffer. It makes rendering and flushing parallel.
     *
     * 3. Double buffering
     *      Set 2 screens sized buffers and set disp_drv.full_refresh = 1.
     *      This way LVGL will always provide the whole rendered screen in `flush_cb`
     *      and you only need to change the frame buffer's address.
     */

    /* Example for 1) */
    static lv_disp_draw_buf_t draw_buf_dsc_1;
    static lv_color_t buf_1[MY_DISP_HOR_RES * MY_DISP_VER_RES];                          /*A buffer for 10 rows*/
    lv_disp_draw_buf_init(&draw_buf_dsc_1, buf_1, NULL,
                          MY_DISP_HOR_RES * MY_DISP_VER_RES);   /*Initialize the display buffer*/

    /* Example for 2) */
    // static lv_disp_draw_buf_t draw_buf_dsc_2;
    // static lv_color_t buf_2_1[MY_DISP_HOR_RES * MY_DISP_VER_RES / 2];                        /*A buffer for 10 rows*/
    // static lv_color_t buf_2_2[
    //         MY_DISP_HOR_RES * MY_DISP_VER_RES / 2];                        /*An other buffer for 10 rows*/
    // lv_disp_draw_buf_init(&draw_buf_dsc_2, buf_2_1, buf_2_2,
    //                       MY_DISP_HOR_RES * MY_DISP_VER_RES / 2);   /*Initialize the display buffer*/

    /* Example for 3) also set disp_drv.full_refresh = 1 below*/
    // static lv_disp_draw_buf_t draw_buf_dsc_3;
    // static lv_color_t buf_3_1[MY_DISP_HOR_RES * MY_DISP_VER_RES / 8];            /*A screen sized buffer*/
    // static lv_color_t buf_3_2[MY_DISP_HOR_RES * MY_DISP_VER_RES / 8];            /*Another screen sized buffer*/
    // lv_disp_draw_buf_init(&draw_buf_dsc_3, buf_3_1, buf_3_2,
    //                       MY_DISP_VER_RES * MY_DISP_HOR_RES / 8);   /*Initialize the display buffer*/

    /*-----------------------------------
     * Register the display in LVGL
     *----------------------------------*/

    static lv_disp_drv_t disp_drv;                         /*Descriptor of a display driver*/
    lv_disp_drv_init(&disp_drv);                    /*Basic initialization*/

    /*Set up the functions to access to your display*/
    disp_drv.rounder_cb = example_rounder_cb;

    /*Set the resolution of the display*/
    disp_drv.hor_res = MY_DISP_HOR_RES;
    disp_drv.ver_res = MY_DISP_VER_RES;

    /*Used to copy the buffer's content to the display*/
    disp_drv.flush_cb = disp_flush;

    /*Set a display buffer*/
    disp_drv.draw_buf = &draw_buf_dsc_1;

    /*Required for Example 3)*/
    //disp_drv.full_refresh = 1;
    // 屏幕旋转
    // disp_drv.sw_rotate = 1;   // add for rotation
    // disp_drv.rotated = LV_DISP_ROT_180;   // add for rotation
    // lv_disp_t * disp;
    // disp = lv_disp_drv_register(&disp_drv); /*Register the driver and save the created display objects*/
    // lv_timer_del(disp->refr_timer);
    // disp->refr_timer = NULL;

    /* Fill a memory array with a color if you have GPU.
     * Note that, in lv_conf.h you can enable GPUs that has built-in support in LVGL.
     * But if you have a different GPU you can use with this callback.*/
    //disp_drv.gpu_fill_cb = gpu_fill;

    /*Finally register the driver*/
    lv_disp_drv_register(&disp_drv);
}

/**********************
 *   STATIC FUNCTIONS
 **********************/

/*Initialize your display and the required peripherals.*/
static void disp_init(void) {
    /*You code here*/
}

volatile bool disp_flush_enabled = true;

/* Enable updating the screen (the flushing process) when disp_flush() is called by LVGL
 */
void disp_enable_update(void) {
    disp_flush_enabled = true;
}

/* Disable updating the screen (the flushing process) when disp_flush() is called by LVGL
 */
void disp_disable_update(void) {
    disp_flush_enabled = false;
}

uint32_t break_count = 0;

/*Flush the content of the internal buffer the specific area on the display
 *You can use DMA or any hardware acceleration to do this operation in the background but
 *'lv_disp_flush_ready()' has to be called when finished.*/
#if 0
static void disp_flush(lv_disp_drv_t *disp_drv, const lv_area_t *area, lv_color_t *color_p) {
    if (disp_flush_enabled) {
        /*The most simple case (but also the slowest) to put all pixels to the screen one-by-one*/
        // Test_1_WriteH;
        // amoled_fill_color((uint16_t) area->x1, (uint16_t) area->y1, (uint16_t) area->x2, (uint16_t) area->y2,
        //                   (uint16_t *) (color_p));
        // Test_1_WriteL;
        while (1) {
            break_count++;
            if (HAL_GPIO_ReadPin(AMOLED_TE_GPIO_Port, AMOLED_TE_Pin)) {
                amoled_fill_color((uint16_t) area->x1, (uint16_t) area->y1, (uint16_t) area->x2, (uint16_t) area->y2,
                                  (uint16_t *) (color_p));
                break_count = 0;
                break;
            }
            // 避免卡死在while
            // 实测最大是580000左右
            if (break_count > 1000000) {
                // amoled_fill_color((uint16_t) area->x1, (uint16_t) area->y1, (uint16_t) area->x2, (uint16_t) area->y2,
                //                   (uint16_t *) (color_p));
                break_count = 0;
                break;
            }
        }
        amoled_fill_color((uint16_t) area->x1, (uint16_t) area->y1, (uint16_t) area->x2, (uint16_t) area->y2,
                          (uint16_t *) (color_p));
        int32_t x;
        int32_t y;
        for(y = area->y1; y <= area->y2; y++) {
            for(x = area->x1; x <= area->x2; x++) {
                /*Put a pixel to the display. For example:*/
                /*put_px(x, y, *color_p)*/
                color_p++;
            }
        }
    }

    // amoled_fill_color(area->x1, area->y1, area->x2, area->y2, (long) color_p);


    /*IMPORTANT!!!
     *Inform the graphics library that you are ready with the flushing*/
    lv_disp_flush_ready(disp_drv);
}
#else
static void disp_flush(lv_disp_drv_t *disp_drv, const lv_area_t *area, lv_color_t *color_p) {
    if (disp_flush_enabled) {
        /*The most simple case (but also the slowest) to put all pixels to the screen one-by-one*/
        // Test_1_WriteH;
        amoled_fill_color((uint16_t) area->x1, (uint16_t) area->y1, (uint16_t) area->x2, (uint16_t) area->y2,
                          (uint16_t *) (color_p));
        // Test_1_WriteL;
        // while (1) {
        //     break_count++;
        //     if (HAL_GPIO_ReadPin(AMOLED_TE_GPIO_Port, AMOLED_TE_Pin)) {
        //         amoled_fill_color((uint16_t) area->x1, (uint16_t) area->y1, (uint16_t) area->x2, (uint16_t) area->y2,
        //                           (uint16_t *) (color_p));
        //         break_count = 0;
        //         break;
        //     }
        //     // 避免卡死在while
        //     // 实测最大是580000左右
        //     if (break_count > 1000000) {
        //         // amoled_fill_color((uint16_t) area->x1, (uint16_t) area->y1, (uint16_t) area->x2, (uint16_t) area->y2,
        //         //                   (uint16_t *) (color_p));
        //         break_count = 0;
        //         break;
        //     }
        // }
        // amoled_fill_color((uint16_t) area->x1, (uint16_t) area->y1, (uint16_t) area->x2, (uint16_t) area->y2,
        //                   (uint16_t *) (color_p));
        // int32_t x;
        // int32_t y;
        // for(y = area->y1; y <= area->y2; y++) {
        //     for(x = area->x1; x <= area->x2; x++) {
        //         /*Put a pixel to the display. For example:*/
        //         /*put_px(x, y, *color_p)*/
        //         color_p++;
        //     }
        // }
    }

    // amoled_fill_color(area->x1, area->y1, area->x2, area->y2, (long) color_p);


    /*IMPORTANT!!!
     *Inform the graphics library that you are ready with the flushing*/
    // lv_disp_flush_ready(disp_drv);
}
#endif
/*OPTIONAL: GPU INTERFACE*/

/*If your MCU has hardware accelerator (GPU) then you can use it to fill a memory with a color*/
//static void gpu_fill(lv_disp_drv_t * disp_drv, lv_color_t * dest_buf, lv_coord_t dest_width,
//                    const lv_area_t * fill_area, lv_color_t color)
//{
//    /*It's an example code which should be done by your GPU*/
//    int32_t x, y;
//    dest_buf += dest_width * fill_area->y1; /*Go to the first line*/
//
//    for(y = fill_area->y1; y <= fill_area->y2; y++) {
//        for(x = fill_area->x1; x <= fill_area->x2; x++) {
//            dest_buf[x] = color;
//        }
//        dest_buf+=dest_width;    /*Go to the next line*/
//    }
//}

#else /*Enable this file at the top*/

/*This dummy typedef exists purely to silence -Wpedantic.*/
typedef int keep_pedantic_happy;
#endif
