/*******************************************************************************
 * Size: 10 px
 * Bpp: 4
 * Opts: --no-compress --no-prefilter --bpp 4 --size 10 --font <PERSON>-Medium.ttf -r 0x20-0x7F,0xB0,0x2022 --font <PERSON><PERSON>ome5-Solid+Brands+Regular.woff -r 61441,61448,61451,61452,61452,61453,61457,61459,61461,61465,61468,61473,61478,61479,61480,61502,61507,61512,61515,61516,61517,61521,61522,61523,61524,61543,61544,61550,61552,61553,61556,61559,61560,61561,61563,61587,61589,61636,61637,61639,61641,61664,61671,61674,61683,61724,61732,61787,61931,62016,62017,62018,62019,62020,62087,62099,62212,62189,62810,63426,63650 --format lvgl -o lv_font_montserrat_10.c --force-fast-kern-format
 ******************************************************************************/

#ifdef LV_LVGL_H_INCLUDE_SIMPLE
    #include "lvgl.h"
#else
    #include "../../lvgl.h"
#endif

#ifndef LV_FONT_MONTSERRAT_10
    #define LV_FONT_MONTSERRAT_10 1
#endif

#if LV_FONT_MONTSERRAT_10

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+0020 " " */

    /* U+0021 "!" */
    0x3e, 0x2d, 0x2c, 0x1c, 0x5, 0x1, 0x2d,

    /* U+0022 "\"" */
    0x57, 0x84, 0x56, 0x83, 0x23, 0x41,

    /* U+0023 "#" */
    0x0, 0xb0, 0x28, 0x0, 0xb, 0x4, 0x60, 0x4a,
    0xea, 0xdc, 0x80, 0x28, 0x8, 0x20, 0x8c, 0xdb,
    0xeb, 0x40, 0x64, 0xb, 0x0, 0x8, 0x30, 0xb0,
    0x0,

    /* U+0024 "$" */
    0x0, 0x13, 0x0, 0x8, 0xde, 0xc3, 0x5b, 0x27,
    0x11, 0x4d, 0x57, 0x0, 0x6, 0xce, 0x80, 0x0,
    0x29, 0x9a, 0x32, 0x27, 0x5b, 0x3c, 0xde, 0xb2,
    0x0, 0x27, 0x0,

    /* U+0025 "%" */
    0x29, 0x92, 0x2, 0x90, 0x9, 0x11, 0x90, 0xa1,
    0x0, 0x82, 0x28, 0x74, 0x0, 0x1, 0x88, 0x49,
    0x68, 0x40, 0x0, 0xb, 0x29, 0xa, 0x0, 0x8,
    0x32, 0x80, 0xa0, 0x3, 0x80, 0x8, 0x87, 0x0,

    /* U+0026 "&" */
    0x3, 0xcb, 0x70, 0x0, 0xa4, 0xd, 0x0, 0x5,
    0xba, 0x60, 0x0, 0x7c, 0xc0, 0x10, 0x5a, 0x7,
    0xbb, 0x37, 0x80, 0xa, 0xe0, 0xa, 0xcc, 0x97,
    0x70, 0x0, 0x0, 0x0,

    /* U+0027 "'" */
    0x57, 0x56, 0x23,

    /* U+0028 "(" */
    0x2, 0xc0, 0x9, 0x60, 0xd, 0x10, 0xe, 0x0,
    0xe, 0x0, 0xe, 0x0, 0xd, 0x10, 0x9, 0x60,
    0x2, 0xc0,

    /* U+0029 ")" */
    0x68, 0x0, 0xe0, 0xb, 0x30, 0x95, 0x8, 0x60,
    0x95, 0xb, 0x30, 0xe0, 0x68, 0x0,

    /* U+002A "*" */
    0x24, 0x42, 0x4d, 0xd4, 0x79, 0x97, 0x2, 0x20,

    /* U+002B "+" */
    0x0, 0x0, 0x0, 0x0, 0x85, 0x0, 0x3b, 0xdc,
    0xb1, 0x0, 0x85, 0x0, 0x0, 0x85, 0x0,

    /* U+002C "," */
    0x35, 0x4a, 0x55,

    /* U+002D "-" */
    0x5c, 0xc3,

    /* U+002E "." */
    0x2, 0x6a,

    /* U+002F "/" */
    0x0, 0x2, 0xb0, 0x0, 0x85, 0x0, 0xd, 0x0,
    0x4, 0x90, 0x0, 0xa3, 0x0, 0xd, 0x0, 0x5,
    0x80, 0x0, 0xb2, 0x0, 0x1c, 0x0, 0x0,

    /* U+0030 "0" */
    0x4, 0xdd, 0xb1, 0x1, 0xe2, 0x6, 0xb0, 0x69,
    0x0, 0xe, 0x17, 0x80, 0x0, 0xd2, 0x69, 0x0,
    0xe, 0x11, 0xe2, 0x6, 0xb0, 0x4, 0xdd, 0xb1,
    0x0,

    /* U+0031 "1" */
    0xbe, 0xa0, 0x5a, 0x5, 0xa0, 0x5a, 0x5, 0xa0,
    0x5a, 0x5, 0xa0,

    /* U+0032 "2" */
    0x4c, 0xdd, 0x50, 0x42, 0x1, 0xf0, 0x0, 0x0,
    0xf0, 0x0, 0xa, 0x80, 0x0, 0xa9, 0x0, 0xb,
    0x80, 0x0, 0x8f, 0xdd, 0xd5,

    /* U+0033 "3" */
    0x8d, 0xde, 0xe0, 0x0, 0xc, 0x40, 0x0, 0x98,
    0x0, 0x0, 0xbd, 0x90, 0x0, 0x0, 0xd3, 0x51,
    0x1, 0xe2, 0x6d, 0xdd, 0x60,

    /* U+0034 "4" */
    0x0, 0x7, 0xa0, 0x0, 0x5, 0xc0, 0x0, 0x3,
    0xd1, 0x31, 0x1, 0xd2, 0xb, 0x30, 0x8d, 0xcc,
    0xfd, 0x70, 0x0, 0xb, 0x30, 0x0, 0x0, 0xb3,
    0x0,

    /* U+0035 "5" */
    0xf, 0xdd, 0xd0, 0x1d, 0x0, 0x0, 0x2c, 0x0,
    0x0, 0x3e, 0xdc, 0x60, 0x0, 0x1, 0xd4, 0x31,
    0x0, 0xc4, 0x5c, 0xdd, 0x80,

    /* U+0036 "6" */
    0x2, 0xbd, 0xd4, 0x1e, 0x40, 0x0, 0x6a, 0x0,
    0x0, 0x7a, 0xab, 0xa1, 0x6e, 0x10, 0x5c, 0x1d,
    0x0, 0x3c, 0x4, 0xcc, 0xb2,

    /* U+0037 "7" */
    0xbd, 0xdd, 0xe8, 0xb4, 0x0, 0xd3, 0x0, 0x4,
    0xc0, 0x0, 0xc, 0x40, 0x0, 0x3d, 0x0, 0x0,
    0xa6, 0x0, 0x1, 0xe0, 0x0,

    /* U+0038 "8" */
    0x7, 0xcc, 0xb2, 0x3d, 0x0, 0x6a, 0x2d, 0x0,
    0x79, 0xb, 0xec, 0xf2, 0x6a, 0x0, 0x4d, 0x79,
    0x0, 0x3e, 0x9, 0xcb, 0xc4,

    /* U+0039 "9" */
    0x1a, 0xcc, 0x60, 0x96, 0x0, 0xb3, 0x97, 0x0,
    0xc9, 0x9, 0xbb, 0x8a, 0x0, 0x0, 0x88, 0x0,
    0x2, 0xe2, 0x2d, 0xdc, 0x40,

    /* U+003A ":" */
    0x6a, 0x1, 0x0, 0x2, 0x6a,

    /* U+003B ";" */
    0x6a, 0x1, 0x0, 0x0, 0x6a, 0x38, 0x32,

    /* U+003C "<" */
    0x0, 0x0, 0x10, 0x0, 0x5a, 0xa1, 0x3e, 0x61,
    0x0, 0x6, 0xb9, 0x30, 0x0, 0x2, 0x81,

    /* U+003D "=" */
    0x3b, 0xbb, 0xb1, 0x0, 0x0, 0x0, 0x3b, 0xbb,
    0xb1,

    /* U+003E ">" */
    0x10, 0x0, 0x0, 0x2b, 0xa4, 0x0, 0x0, 0x18,
    0xe1, 0x4, 0xab, 0x50, 0x37, 0x10, 0x0,

    /* U+003F "?" */
    0x3c, 0xdd, 0x50, 0x52, 0x1, 0xf0, 0x0, 0x3,
    0xd0, 0x0, 0x3d, 0x20, 0x0, 0x85, 0x0, 0x0,
    0x10, 0x0, 0x0, 0xb4, 0x0,

    /* U+0040 "@" */
    0x0, 0x4a, 0x99, 0xa7, 0x0, 0x6, 0x90, 0x0,
    0x3, 0xa0, 0x1b, 0x7, 0xcb, 0x9b, 0x47, 0x65,
    0x4b, 0x0, 0x8b, 0xa, 0x73, 0x77, 0x0, 0x3b,
    0xa, 0x65, 0x3b, 0x0, 0x8b, 0xa, 0x1b, 0x6,
    0xcb, 0x6c, 0xb3, 0x6, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x4a, 0x9a, 0xa2, 0x0,

    /* U+0041 "A" */
    0x0, 0x1, 0xf6, 0x0, 0x0, 0x0, 0x88, 0xc0,
    0x0, 0x0, 0xd, 0x9, 0x40, 0x0, 0x6, 0x70,
    0x2b, 0x0, 0x0, 0xdc, 0xcc, 0xe3, 0x0, 0x59,
    0x0, 0x4, 0xa0, 0xc, 0x30, 0x0, 0xd, 0x10,

    /* U+0042 "B" */
    0xfc, 0xcc, 0xb2, 0xf, 0x0, 0x7, 0xa0, 0xf0,
    0x0, 0x88, 0xf, 0xcc, 0xdf, 0x30, 0xf0, 0x0,
    0x2e, 0xf, 0x0, 0x1, 0xf0, 0xfc, 0xcc, 0xc5,
    0x0,

    /* U+0043 "C" */
    0x1, 0x9d, 0xdc, 0x30, 0xd6, 0x0, 0x35, 0x5b,
    0x0, 0x0, 0x7, 0x80, 0x0, 0x0, 0x5b, 0x0,
    0x0, 0x0, 0xd6, 0x0, 0x35, 0x1, 0x9d, 0xdc,
    0x30,

    /* U+0044 "D" */
    0xfd, 0xdd, 0xb3, 0xf, 0x0, 0x3, 0xe2, 0xf0,
    0x0, 0x6, 0x9f, 0x0, 0x0, 0x4b, 0xf0, 0x0,
    0x6, 0x9f, 0x0, 0x3, 0xe2, 0xfd, 0xdd, 0xb3,
    0x0,

    /* U+0045 "E" */
    0xfd, 0xdd, 0xc0, 0xf0, 0x0, 0x0, 0xf0, 0x0,
    0x0, 0xfc, 0xcc, 0x70, 0xf0, 0x0, 0x0, 0xf0,
    0x0, 0x0, 0xfd, 0xdd, 0xd1,

    /* U+0046 "F" */
    0xfd, 0xdd, 0xcf, 0x0, 0x0, 0xf0, 0x0, 0xf,
    0xdd, 0xd7, 0xf0, 0x0, 0xf, 0x0, 0x0, 0xf0,
    0x0, 0x0,

    /* U+0047 "G" */
    0x1, 0x9d, 0xdc, 0x40, 0xd7, 0x0, 0x25, 0x5b,
    0x0, 0x0, 0x7, 0x80, 0x0, 0x7, 0x5b, 0x0,
    0x1, 0xd0, 0xd6, 0x0, 0x3d, 0x1, 0x9d, 0xdc,
    0x50,

    /* U+0048 "H" */
    0xf0, 0x0, 0xf, 0x1f, 0x0, 0x0, 0xf1, 0xf0,
    0x0, 0xf, 0x1f, 0xdd, 0xdd, 0xf1, 0xf0, 0x0,
    0xf, 0x1f, 0x0, 0x0, 0xf1, 0xf0, 0x0, 0xf,
    0x10,

    /* U+0049 "I" */
    0xf0, 0xf0, 0xf0, 0xf0, 0xf0, 0xf0, 0xf0,

    /* U+004A "J" */
    0x4, 0xdd, 0xf2, 0x0, 0x0, 0xd2, 0x0, 0x0,
    0xd2, 0x0, 0x0, 0xd2, 0x0, 0x0, 0xd2, 0x6,
    0x1, 0xe0, 0x8, 0xdd, 0x60,

    /* U+004B "K" */
    0xf0, 0x0, 0xa8, 0xf, 0x0, 0x99, 0x0, 0xf0,
    0x99, 0x0, 0xf, 0x9f, 0x40, 0x0, 0xfb, 0x4e,
    0x20, 0xf, 0x10, 0x5d, 0x10, 0xf0, 0x0, 0x6b,
    0x0,

    /* U+004C "L" */
    0xf0, 0x0, 0xf, 0x0, 0x0, 0xf0, 0x0, 0xf,
    0x0, 0x0, 0xf0, 0x0, 0xf, 0x0, 0x0, 0xfd,
    0xdd, 0xa0,

    /* U+004D "M" */
    0xf2, 0x0, 0x0, 0x97, 0xfc, 0x0, 0x3, 0xf7,
    0xfa, 0x50, 0xc, 0xa7, 0xf1, 0xd0, 0x69, 0x77,
    0xf0, 0x79, 0xd1, 0x77, 0xf0, 0xd, 0x60, 0x77,
    0xf0, 0x1, 0x0, 0x77,

    /* U+004E "N" */
    0xf4, 0x0, 0xf, 0x1f, 0xe2, 0x0, 0xf1, 0xf6,
    0xd0, 0xf, 0x1f, 0x9, 0xa0, 0xf1, 0xf0, 0xb,
    0x7f, 0x1f, 0x0, 0x1d, 0xf1, 0xf0, 0x0, 0x3f,
    0x10,

    /* U+004F "O" */
    0x1, 0x9d, 0xdc, 0x40, 0xd, 0x60, 0x2, 0xd4,
    0x5b, 0x0, 0x0, 0x4b, 0x78, 0x0, 0x0, 0x1e,
    0x5b, 0x0, 0x0, 0x4b, 0xd, 0x60, 0x2, 0xd4,
    0x1, 0x9d, 0xdc, 0x40,

    /* U+0050 "P" */
    0xfd, 0xdd, 0x90, 0xf0, 0x0, 0xa7, 0xf0, 0x0,
    0x5a, 0xf0, 0x0, 0xb6, 0xfd, 0xdc, 0x70, 0xf0,
    0x0, 0x0, 0xf0, 0x0, 0x0,

    /* U+0051 "Q" */
    0x1, 0x9d, 0xdc, 0x40, 0x0, 0xc7, 0x0, 0x3d,
    0x40, 0x5b, 0x0, 0x0, 0x4b, 0x7, 0x80, 0x0,
    0x1, 0xe0, 0x5a, 0x0, 0x0, 0x4c, 0x0, 0xd6,
    0x0, 0x2d, 0x40, 0x1, 0xad, 0xdd, 0x40, 0x0,
    0x0, 0x4, 0xab, 0xa0,

    /* U+0052 "R" */
    0xfd, 0xdd, 0x90, 0xf0, 0x0, 0xa7, 0xf0, 0x0,
    0x5a, 0xf0, 0x0, 0xb7, 0xfc, 0xcf, 0x90, 0xf0,
    0x5, 0xb0, 0xf0, 0x0, 0x97,

    /* U+0053 "S" */
    0x8, 0xdc, 0xc3, 0x5b, 0x0, 0x11, 0x4d, 0x20,
    0x0, 0x6, 0xde, 0x90, 0x0, 0x1, 0x9a, 0x33,
    0x0, 0x5b, 0x3b, 0xcc, 0xb2,

    /* U+0054 "T" */
    0xcd, 0xee, 0xda, 0x0, 0x97, 0x0, 0x0, 0x97,
    0x0, 0x0, 0x97, 0x0, 0x0, 0x97, 0x0, 0x0,
    0x97, 0x0, 0x0, 0x97, 0x0,

    /* U+0055 "U" */
    0xf, 0x0, 0x1, 0xe0, 0xf0, 0x0, 0x1e, 0xf,
    0x0, 0x1, 0xe0, 0xf0, 0x0, 0x1e, 0xe, 0x0,
    0x2, 0xd0, 0xa7, 0x0, 0x98, 0x1, 0xad, 0xd9,
    0x0,

    /* U+0056 "V" */
    0xc, 0x40, 0x0, 0x1d, 0x0, 0x5b, 0x0, 0x8,
    0x70, 0x0, 0xe2, 0x0, 0xe1, 0x0, 0x7, 0x90,
    0x69, 0x0, 0x0, 0x1e, 0x1d, 0x20, 0x0, 0x0,
    0x9c, 0xb0, 0x0, 0x0, 0x2, 0xf4, 0x0, 0x0,

    /* U+0057 "W" */
    0x88, 0x0, 0xf, 0x40, 0x2, 0xc3, 0xd0, 0x5,
    0xea, 0x0, 0x86, 0xd, 0x20, 0xa4, 0xe0, 0xd,
    0x10, 0x88, 0xd, 0xa, 0x43, 0xc0, 0x2, 0xd5,
    0x90, 0x4a, 0x86, 0x0, 0xd, 0xd3, 0x0, 0xed,
    0x10, 0x0, 0x8e, 0x0, 0xa, 0xc0, 0x0,

    /* U+0058 "X" */
    0x5c, 0x0, 0x1d, 0x10, 0x98, 0xb, 0x50, 0x0,
    0xda, 0x90, 0x0, 0x6, 0xf2, 0x0, 0x1, 0xd7,
    0xc0, 0x0, 0xc5, 0xa, 0x80, 0x8a, 0x0, 0xd,
    0x30,

    /* U+0059 "Y" */
    0xb, 0x50, 0x0, 0xc3, 0x2, 0xd0, 0x6, 0x90,
    0x0, 0x88, 0x1d, 0x10, 0x0, 0xd, 0xb6, 0x0,
    0x0, 0x5, 0xd0, 0x0, 0x0, 0x4, 0xb0, 0x0,
    0x0, 0x4, 0xb0, 0x0,

    /* U+005A "Z" */
    0x6d, 0xdd, 0xdf, 0x10, 0x0, 0xb, 0x70, 0x0,
    0x8, 0xa0, 0x0, 0x4, 0xd0, 0x0, 0x2, 0xe2,
    0x0, 0x0, 0xd4, 0x0, 0x0, 0x8f, 0xdd, 0xdd,
    0x30,

    /* U+005B "[" */
    0xfb, 0x1f, 0x0, 0xf0, 0xf, 0x0, 0xf0, 0xf,
    0x0, 0xf0, 0xf, 0x0, 0xfb, 0x10,

    /* U+005C "\\" */
    0x3a, 0x0, 0x0, 0xc1, 0x0, 0x7, 0x60, 0x0,
    0x1c, 0x0, 0x0, 0xb2, 0x0, 0x5, 0x80, 0x0,
    0xd, 0x0, 0x0, 0xa3, 0x0, 0x4, 0x90,

    /* U+005D "]" */
    0x9e, 0x40, 0xb4, 0xb, 0x40, 0xb4, 0xb, 0x40,
    0xb4, 0xb, 0x40, 0xb4, 0x9e, 0x40,

    /* U+005E "^" */
    0x0, 0xa8, 0x0, 0x2, 0x9b, 0x0, 0x9, 0x25,
    0x60, 0x1b, 0x0, 0xb0,

    /* U+005F "_" */
    0x99, 0x99, 0x90,

    /* U+0060 "`" */
    0x3a, 0x30,

    /* U+0061 "a" */
    0x1b, 0xcd, 0x60, 0x1, 0x0, 0xe0, 0x1a, 0xaa,
    0xf1, 0x78, 0x0, 0xe1, 0x2c, 0xaa, 0xe1,

    /* U+0062 "b" */
    0x1e, 0x0, 0x0, 0x1, 0xe0, 0x0, 0x0, 0x1e,
    0xac, 0xd6, 0x1, 0xf3, 0x1, 0xe2, 0x1e, 0x0,
    0xa, 0x51, 0xf4, 0x1, 0xe2, 0x1d, 0x9c, 0xd5,
    0x0,

    /* U+0063 "c" */
    0x7, 0xdd, 0xa0, 0x5c, 0x0, 0x40, 0x87, 0x0,
    0x0, 0x5c, 0x0, 0x41, 0x7, 0xdd, 0xa0,

    /* U+0064 "d" */
    0x0, 0x0, 0xe, 0x0, 0x0, 0xe, 0x8, 0xdc,
    0x9e, 0x5c, 0x0, 0x7e, 0x87, 0x0, 0x1e, 0x5b,
    0x0, 0x6e, 0x8, 0xdb, 0x8e,

    /* U+0065 "e" */
    0x8, 0xcc, 0x90, 0x5a, 0x0, 0x87, 0x8c, 0xaa,
    0xa8, 0x5b, 0x0, 0x20, 0x7, 0xdc, 0xb1,

    /* U+0066 "f" */
    0x7, 0xc9, 0xe, 0x0, 0x9f, 0xb6, 0xf, 0x0,
    0xf, 0x0, 0xf, 0x0, 0xf, 0x0,

    /* U+0067 "g" */
    0x8, 0xdc, 0x9e, 0x5b, 0x0, 0x5f, 0x87, 0x0,
    0xf, 0x5c, 0x0, 0x6f, 0x7, 0xdc, 0x9f, 0x3,
    0x0, 0x4c, 0x1a, 0xcc, 0xb2,

    /* U+0068 "h" */
    0x1e, 0x0, 0x0, 0x1e, 0x0, 0x0, 0x1e, 0xac,
    0xd4, 0x1f, 0x30, 0x3d, 0x1e, 0x0, 0xe, 0x1e,
    0x0, 0xf, 0x1e, 0x0, 0xf,

    /* U+0069 "i" */
    0x2d, 0x0, 0x10, 0x1e, 0x1, 0xe0, 0x1e, 0x1,
    0xe0, 0x1e, 0x0,

    /* U+006A "j" */
    0x1, 0xe0, 0x0, 0x10, 0x0, 0xe0, 0x0, 0xe0,
    0x0, 0xe0, 0x0, 0xe0, 0x0, 0xe0, 0x1, 0xe0,
    0xad, 0x60,

    /* U+006B "k" */
    0x1e, 0x0, 0x0, 0x1, 0xe0, 0x0, 0x0, 0x1e,
    0x1, 0xb6, 0x1, 0xe2, 0xd5, 0x0, 0x1f, 0xde,
    0x20, 0x1, 0xf2, 0x5d, 0x0, 0x1e, 0x0, 0x7b,
    0x0,

    /* U+006C "l" */
    0x1e, 0x1e, 0x1e, 0x1e, 0x1e, 0x1e, 0x1e,

    /* U+006D "m" */
    0x1e, 0xab, 0xc5, 0xbb, 0xc2, 0x1f, 0x20, 0x5f,
    0x10, 0x69, 0x1e, 0x0, 0x2c, 0x0, 0x4b, 0x1e,
    0x0, 0x2c, 0x0, 0x4b, 0x1e, 0x0, 0x2c, 0x0,
    0x4b,

    /* U+006E "n" */
    0x1e, 0xab, 0xc4, 0x1f, 0x20, 0x3d, 0x1e, 0x0,
    0xe, 0x1e, 0x0, 0xf, 0x1e, 0x0, 0xf,

    /* U+006F "o" */
    0x7, 0xdd, 0xb1, 0x5c, 0x0, 0x7b, 0x87, 0x0,
    0x1e, 0x5c, 0x0, 0x7b, 0x7, 0xdd, 0xb1,

    /* U+0070 "p" */
    0x1e, 0xab, 0xd6, 0x1, 0xf3, 0x1, 0xd2, 0x1e,
    0x0, 0xa, 0x51, 0xf4, 0x1, 0xe2, 0x1e, 0xac,
    0xd5, 0x1, 0xe0, 0x0, 0x0, 0x1e, 0x0, 0x0,
    0x0,

    /* U+0071 "q" */
    0x8, 0xdc, 0x8e, 0x5c, 0x0, 0x7e, 0x87, 0x0,
    0x1e, 0x5c, 0x0, 0x7e, 0x8, 0xdc, 0x8e, 0x0,
    0x0, 0xe, 0x0, 0x0, 0xe,

    /* U+0072 "r" */
    0x1d, 0xaa, 0x1f, 0x30, 0x1e, 0x0, 0x1e, 0x0,
    0x1e, 0x0,

    /* U+0073 "s" */
    0x2c, 0xcc, 0x48, 0x80, 0x0, 0x2a, 0xca, 0x21,
    0x0, 0x6a, 0x6c, 0xcc, 0x30,

    /* U+0074 "t" */
    0xf, 0x0, 0x9f, 0xb6, 0xf, 0x0, 0xf, 0x0,
    0xe, 0x10, 0x7, 0xd9,

    /* U+0075 "u" */
    0x2d, 0x0, 0x1d, 0x2d, 0x0, 0x1d, 0x2d, 0x0,
    0x1d, 0xe, 0x10, 0x6d, 0x6, 0xdb, 0x9d,

    /* U+0076 "v" */
    0xc, 0x30, 0x9, 0x50, 0x5a, 0x1, 0xd0, 0x0,
    0xd2, 0x86, 0x0, 0x6, 0x9d, 0x0, 0x0, 0xe,
    0x80, 0x0,

    /* U+0077 "w" */
    0xb2, 0x1, 0xf1, 0x2, 0xb5, 0x80, 0x7b, 0x80,
    0x85, 0xd, 0xd, 0x1d, 0xd, 0x0, 0x89, 0x90,
    0x99, 0x80, 0x2, 0xf2, 0x2, 0xf2, 0x0,

    /* U+0078 "x" */
    0x5b, 0x3, 0xc0, 0x8, 0x9c, 0x10, 0x0, 0xe7,
    0x0, 0xa, 0x7c, 0x20, 0x79, 0x2, 0xd1,

    /* U+0079 "y" */
    0xc, 0x30, 0x9, 0x50, 0x5a, 0x1, 0xd0, 0x0,
    0xd2, 0x77, 0x0, 0x6, 0x9d, 0x0, 0x0, 0xe,
    0x80, 0x0, 0x0, 0xd1, 0x0, 0xc, 0xd6, 0x0,
    0x0,

    /* U+007A "z" */
    0x6b, 0xbe, 0xb0, 0x2, 0xd1, 0x1, 0xd2, 0x0,
    0xc4, 0x0, 0x8e, 0xbb, 0x90,

    /* U+007B "{" */
    0x4, 0xd3, 0x9, 0x50, 0xa, 0x50, 0xa, 0x40,
    0x5f, 0x10, 0xa, 0x40, 0xa, 0x50, 0x9, 0x50,
    0x4, 0xd3,

    /* U+007C "|" */
    0xee, 0xee, 0xee, 0xee, 0xe0,

    /* U+007D "}" */
    0xab, 0x0, 0xd2, 0xd, 0x20, 0xc2, 0x9, 0xc0,
    0xc2, 0xd, 0x20, 0xd2, 0xab, 0x0,

    /* U+007E "~" */
    0x1a, 0x91, 0x62, 0x44, 0x29, 0x90,

    /* U+00B0 "°" */
    0x7, 0x81, 0x62, 0x8, 0x62, 0x8, 0x7, 0x81,

    /* U+2022 "•" */
    0x19, 0x23, 0xe4,

    /* U+F001 "" */
    0x0, 0x0, 0x0, 0x4, 0x9c, 0x10, 0x0, 0x16,
    0xbf, 0xff, 0xf2, 0x0, 0x3f, 0xff, 0xff, 0xff,
    0x20, 0x5, 0xff, 0xd9, 0x41, 0xf2, 0x0, 0x5f,
    0x20, 0x0, 0x1f, 0x20, 0x5, 0xe0, 0x0, 0x1,
    0xf2, 0x0, 0x5e, 0x0, 0x7, 0x9f, 0x20, 0x48,
    0xe0, 0x7, 0xff, 0xf2, 0xaf, 0xfe, 0x0, 0x2b,
    0xd8, 0x7, 0xff, 0x90, 0x0, 0x0, 0x0, 0x1,
    0x10, 0x0, 0x0, 0x0, 0x0,

    /* U+F008 "" */
    0x41, 0x88, 0x88, 0x88, 0x14, 0xeb, 0xe7, 0x77,
    0x7e, 0xbe, 0xa2, 0xd0, 0x0, 0xd, 0x2a, 0xeb,
    0xe3, 0x33, 0x3e, 0xbe, 0xb4, 0xfb, 0xbb, 0xbf,
    0x4b, 0xd9, 0xd0, 0x0, 0xd, 0x9d, 0xb5, 0xd0,
    0x0, 0xd, 0x5b, 0xb7, 0xff, 0xff, 0xff, 0x7b,

    /* U+F00B "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xd6, 0xff,
    0xff, 0xff, 0xff, 0xe7, 0xff, 0xff, 0xff, 0x67,
    0x52, 0x77, 0x77, 0x76, 0xef, 0xc6, 0xff, 0xff,
    0xfe, 0xff, 0xe7, 0xff, 0xff, 0xff, 0x67, 0x52,
    0x77, 0x77, 0x76, 0xef, 0xc6, 0xff, 0xff, 0xfe,
    0xff, 0xe7, 0xff, 0xff, 0xff, 0x78, 0x63, 0x88,
    0x88, 0x87,

    /* U+F00C "" */
    0x0, 0x0, 0x0, 0x0, 0x41, 0x0, 0x0, 0x0,
    0x6, 0xfd, 0x0, 0x0, 0x0, 0x6f, 0xf7, 0x7c,
    0x10, 0x6, 0xff, 0x70, 0xdf, 0xd1, 0x6f, 0xf7,
    0x0, 0x1d, 0xfe, 0xff, 0x70, 0x0, 0x1, 0xdf,
    0xf7, 0x0, 0x0, 0x0, 0x1c, 0x60, 0x0, 0x0,

    /* U+F00D "" */
    0x0, 0x0, 0x0, 0xc, 0xd1, 0x2, 0xea, 0xaf,
    0xd4, 0xef, 0x80, 0xaf, 0xff, 0x80, 0x2, 0xff,
    0xf1, 0x2, 0xef, 0xdf, 0xd1, 0xdf, 0x80, 0xaf,
    0xb6, 0x70, 0x0, 0x85,

    /* U+F011 "" */
    0x0, 0x0, 0xa6, 0x0, 0x0, 0x2, 0xa0, 0xea,
    0x29, 0x0, 0xe, 0xe1, 0xea, 0x5f, 0xa0, 0x7f,
    0x40, 0xea, 0x8, 0xf3, 0xbd, 0x0, 0xea, 0x1,
    0xf7, 0xcc, 0x0, 0xb7, 0x0, 0xf8, 0xaf, 0x0,
    0x0, 0x4, 0xf6, 0x4f, 0xa0, 0x0, 0x1d, 0xf1,
    0x9, 0xfd, 0x89, 0xef, 0x50, 0x0, 0x6d, 0xff,
    0xc4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F013 "" */
    0x0, 0x1, 0x88, 0x10, 0x0, 0x0, 0x5, 0xff,
    0x40, 0x0, 0x1e, 0xcf, 0xff, 0xfc, 0xd0, 0x7f,
    0xff, 0xdd, 0xff, 0xf7, 0x2d, 0xfa, 0x0, 0xbf,
    0xd1, 0xb, 0xf7, 0x0, 0x8f, 0xa0, 0x6f, 0xfe,
    0x55, 0xef, 0xf6, 0x4f, 0xff, 0xff, 0xff, 0xf3,
    0x6, 0x3a, 0xff, 0xa3, 0x60, 0x0, 0x3, 0xff,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F015 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0x90, 0xf8, 0x0, 0x0, 0x9, 0xf8, 0xec,
    0xf8, 0x0, 0x1, 0xbe, 0x5a, 0x5c, 0xf8, 0x0,
    0x2d, 0xd5, 0xef, 0xf6, 0xaf, 0x50, 0xda, 0x6f,
    0xff, 0xff, 0x87, 0xf1, 0x11, 0xff, 0xff, 0xff,
    0xf5, 0x10, 0x2, 0xff, 0xc3, 0x9f, 0xf6, 0x0,
    0x2, 0xff, 0xb0, 0x7f, 0xf6, 0x0, 0x1, 0xbb,
    0x70, 0x4b, 0xb3, 0x0,

    /* U+F019 "" */
    0x0, 0x0, 0x88, 0x20, 0x0, 0x0, 0x1, 0xff,
    0x60, 0x0, 0x0, 0x1, 0xff, 0x60, 0x0, 0x0,
    0x1, 0xff, 0x60, 0x0, 0x1, 0xff, 0xff, 0xff,
    0x60, 0x0, 0x5f, 0xff, 0xfa, 0x0, 0x0, 0x5,
    0xff, 0xb0, 0x0, 0x8b, 0xb9, 0x8b, 0x8b, 0xb9,
    0xdf, 0xff, 0xff, 0xfe, 0xdf, 0xcf, 0xff, 0xff,
    0xfc, 0xbe, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F01C "" */
    0x0, 0x24, 0x44, 0x44, 0x30, 0x0, 0x1, 0xef,
    0xff, 0xff, 0xf4, 0x0, 0xb, 0xc0, 0x0, 0x0,
    0x8e, 0x10, 0x6e, 0x10, 0x0, 0x0, 0xc, 0xa0,
    0xee, 0xcb, 0x10, 0xa, 0xcd, 0xf2, 0xff, 0xff,
    0xb8, 0x9f, 0xff, 0xf4, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf4, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xe1,

    /* U+F021 "" */
    0x0, 0x4, 0x87, 0x30, 0x5f, 0x2, 0xdf, 0xfe,
    0xfc, 0x7f, 0x1e, 0xd3, 0x0, 0x3c, 0xff, 0x9f,
    0x10, 0x5, 0xfe, 0xff, 0x44, 0x0, 0x2, 0x66,
    0x66, 0x12, 0x22, 0x0, 0x0, 0x11, 0xff, 0xff,
    0x50, 0x0, 0xda, 0xff, 0xa3, 0x10, 0x8, 0xf4,
    0xfc, 0xfb, 0x66, 0xbf, 0x80, 0xf5, 0x5c, 0xff,
    0xd5, 0x0, 0x31, 0x0, 0x0, 0x0, 0x0,

    /* U+F026 "" */
    0x0, 0x0, 0x70, 0x0, 0xbf, 0xab, 0xdf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0,
    0x3e, 0xf0, 0x0, 0x2c,

    /* U+F027 "" */
    0x0, 0x0, 0x70, 0x0, 0x0, 0xb, 0xf0, 0x0,
    0xab, 0xdf, 0xf0, 0x20, 0xff, 0xff, 0xf0, 0xa6,
    0xff, 0xff, 0xf0, 0x59, 0xff, 0xff, 0xf0, 0x92,
    0x0, 0x3e, 0xf0, 0x0, 0x0, 0x2, 0xc0, 0x0,

    /* U+F028 "" */
    0x0, 0x0, 0x0, 0x0, 0x40, 0x0, 0x0, 0x0,
    0x70, 0x0, 0xaa, 0x0, 0x0, 0xb, 0xf0, 0xa,
    0x4a, 0x70, 0xab, 0xdf, 0xf0, 0x23, 0xe2, 0xe0,
    0xff, 0xff, 0xf0, 0xa6, 0x95, 0xc2, 0xff, 0xff,
    0xf0, 0x59, 0x76, 0xc3, 0xff, 0xff, 0xf0, 0x92,
    0xc3, 0xe1, 0x0, 0x3e, 0xf0, 0x9, 0xa6, 0xb0,
    0x0, 0x2, 0xc0, 0x3, 0x3e, 0x20, 0x0, 0x0,
    0x0, 0x0, 0xc3, 0x0,

    /* U+F03E "" */
    0x24, 0x44, 0x44, 0x44, 0x42, 0xff, 0xef, 0xff,
    0xff, 0xff, 0xf3, 0xd, 0xff, 0xef, 0xff, 0xf8,
    0x4e, 0xfe, 0x25, 0xff, 0xff, 0x9d, 0xe2, 0x0,
    0x6f, 0xf9, 0x1, 0x20, 0x0, 0x4f, 0xf7, 0x44,
    0x44, 0x44, 0x7f, 0xcf, 0xff, 0xff, 0xff, 0xfc,

    /* U+F043 "" */
    0x0, 0x1a, 0x0, 0x0, 0x7, 0xf5, 0x0, 0x0,
    0xef, 0xc0, 0x0, 0x8f, 0xff, 0x60, 0x3f, 0xff,
    0xff, 0x1b, 0xff, 0xff, 0xf9, 0xfb, 0xff, 0xff,
    0xdd, 0x6e, 0xff, 0xfc, 0x7e, 0x59, 0xff, 0x60,
    0x9f, 0xff, 0x80, 0x0, 0x13, 0x10, 0x0,

    /* U+F048 "" */
    0x0, 0x0, 0x0, 0xe, 0x70, 0x3, 0xe4, 0xe7,
    0x4, 0xff, 0x5e, 0x75, 0xff, 0xf5, 0xec, 0xff,
    0xff, 0x5e, 0xff, 0xff, 0xf5, 0xea, 0xef, 0xff,
    0x5e, 0x71, 0xdf, 0xf5, 0xe7, 0x1, 0xcf, 0x59,
    0x50, 0x0, 0x92,

    /* U+F04B "" */
    0x88, 0x0, 0x0, 0x0, 0xf, 0xfe, 0x50, 0x0,
    0x0, 0xff, 0xff, 0xc3, 0x0, 0xf, 0xff, 0xff,
    0xf9, 0x10, 0xff, 0xff, 0xff, 0xfe, 0x5f, 0xff,
    0xff, 0xff, 0xfa, 0xff, 0xff, 0xff, 0xf8, 0xf,
    0xff, 0xff, 0xb2, 0x0, 0xff, 0xfd, 0x40, 0x0,
    0xe, 0xf7, 0x0, 0x0, 0x0, 0x11, 0x0, 0x0,
    0x0, 0x0,

    /* U+F04C "" */
    0x0, 0x0, 0x0, 0x0, 0xd, 0xff, 0x90, 0xdf,
    0xf9, 0xff, 0xfc, 0xf, 0xff, 0xcf, 0xff, 0xc0,
    0xff, 0xfc, 0xff, 0xfc, 0xf, 0xff, 0xcf, 0xff,
    0xc0, 0xff, 0xfc, 0xff, 0xfc, 0xf, 0xff, 0xcf,
    0xff, 0xc0, 0xff, 0xfc, 0xff, 0xfb, 0xf, 0xff,
    0xb8, 0xbb, 0x50, 0x8b, 0xb5,

    /* U+F04D "" */
    0x0, 0x0, 0x0, 0x0, 0xd, 0xff, 0xff, 0xff,
    0xf9, 0xff, 0xff, 0xff, 0xff, 0xcf, 0xff, 0xff,
    0xff, 0xfc, 0xff, 0xff, 0xff, 0xff, 0xcf, 0xff,
    0xff, 0xff, 0xfc, 0xff, 0xff, 0xff, 0xff, 0xcf,
    0xff, 0xff, 0xff, 0xfc, 0xff, 0xff, 0xff, 0xff,
    0xb8, 0xbb, 0xbb, 0xbb, 0xb5,

    /* U+F051 "" */
    0x0, 0x0, 0x0, 0xb, 0xa0, 0x0, 0xe7, 0xcf,
    0xb0, 0xe, 0x7c, 0xff, 0xc1, 0xe7, 0xcf, 0xff,
    0xdf, 0x7c, 0xff, 0xff, 0xf7, 0xcf, 0xff, 0x9e,
    0x7c, 0xff, 0x70, 0xe7, 0xcf, 0x60, 0xe, 0x77,
    0x50, 0x0, 0x95,

    /* U+F052 "" */
    0x0, 0x0, 0x2, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0x60, 0x0, 0x0, 0x9, 0xff, 0xf5, 0x0, 0x0,
    0x8f, 0xff, 0xff, 0x40, 0x6, 0xff, 0xff, 0xff,
    0xf3, 0xe, 0xff, 0xff, 0xff, 0xfa, 0x3, 0x66,
    0x66, 0x66, 0x62, 0xd, 0xff, 0xff, 0xff, 0xf9,
    0xf, 0xff, 0xff, 0xff, 0xfb, 0x6, 0x88, 0x88,
    0x88, 0x84,

    /* U+F053 "" */
    0x0, 0x0, 0x0, 0x0, 0x1, 0xc7, 0x0, 0x1d,
    0xf5, 0x1, 0xdf, 0x50, 0x1d, 0xf5, 0x0, 0x4f,
    0xd0, 0x0, 0x6, 0xfc, 0x0, 0x0, 0x6f, 0xc0,
    0x0, 0x6, 0xf9, 0x0, 0x0, 0x51,

    /* U+F054 "" */
    0x0, 0x0, 0x0, 0x3e, 0x30, 0x0, 0x2e, 0xf3,
    0x0, 0x2, 0xef, 0x30, 0x0, 0x2e, 0xe3, 0x0,
    0x9, 0xf8, 0x0, 0x8f, 0xa0, 0x8, 0xfa, 0x0,
    0x5f, 0xa0, 0x0, 0x6, 0x0, 0x0,

    /* U+F067 "" */
    0x0, 0x0, 0x10, 0x0, 0x0, 0x0, 0x8f, 0x40,
    0x0, 0x0, 0x9, 0xf5, 0x0, 0x0, 0x0, 0x9f,
    0x50, 0x0, 0x9b, 0xbd, 0xfc, 0xbb, 0x6f, 0xff,
    0xff, 0xff, 0xfb, 0x13, 0x3a, 0xf7, 0x33, 0x10,
    0x0, 0x9f, 0x50, 0x0, 0x0, 0x9, 0xf5, 0x0,
    0x0, 0x0, 0x39, 0x10, 0x0,

    /* U+F068 "" */
    0xbd, 0xdd, 0xdd, 0xdd, 0x8e, 0xff, 0xff, 0xff,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F06E "" */
    0x0, 0x4, 0x8a, 0x95, 0x0, 0x0, 0x1, 0xcf,
    0x84, 0x6e, 0xe3, 0x0, 0x1e, 0xf5, 0x8, 0x72,
    0xff, 0x40, 0xbf, 0xe0, 0x2d, 0xf5, 0xbf, 0xe0,
    0xdf, 0xe3, 0xff, 0xf6, 0xaf, 0xf1, 0x4f, 0xf3,
    0xaf, 0xd1, 0xef, 0x70, 0x5, 0xfd, 0x31, 0x2b,
    0xf7, 0x0, 0x0, 0x19, 0xdf, 0xea, 0x30, 0x0,

    /* U+F070 "" */
    0xb6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xfa,
    0x16, 0xaa, 0x83, 0x0, 0x0, 0x3, 0xef, 0xe6,
    0x49, 0xfb, 0x0, 0x0, 0x1, 0xbe, 0x49, 0x28,
    0xfd, 0x0, 0x1d, 0x40, 0x8f, 0xfe, 0x1f, 0xf9,
    0x4, 0xff, 0x50, 0x5f, 0xf1, 0xff, 0xb0, 0xa,
    0xfc, 0x0, 0x2d, 0xdf, 0xf2, 0x0, 0xa, 0xfa,
    0x10, 0x1b, 0xf7, 0x0, 0x0, 0x4, 0xbe, 0xe4,
    0x8, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x0,

    /* U+F071 "" */
    0x0, 0x0, 0x2, 0xe6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xe1, 0x0, 0x0, 0x0, 0x0, 0x4f,
    0xff, 0x90, 0x0, 0x0, 0x0, 0xd, 0xe7, 0xcf,
    0x20, 0x0, 0x0, 0x7, 0xfc, 0x8, 0xfb, 0x0,
    0x0, 0x1, 0xef, 0xd0, 0x9f, 0xf4, 0x0, 0x0,
    0x9f, 0xff, 0x5c, 0xff, 0xd0, 0x0, 0x2f, 0xff,
    0xe1, 0xaf, 0xff, 0x60, 0xb, 0xff, 0xfe, 0x2b,
    0xff, 0xfe, 0x0, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xf2, 0x1, 0x33, 0x33, 0x33, 0x33, 0x32, 0x0,

    /* U+F074 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xc1, 0xef, 0xd1, 0x3, 0xef, 0xfc, 0x99,
    0xfb, 0x2e, 0xec, 0xf8, 0x0, 0x54, 0xde, 0x25,
    0x70, 0x0, 0xc, 0xf4, 0x1, 0x10, 0x0, 0xbf,
    0x5c, 0x78, 0xd1, 0xff, 0xf6, 0xa, 0xff, 0xfd,
    0x78, 0x60, 0x0, 0x7c, 0xf6, 0x0, 0x0, 0x0,
    0x5, 0x60,

    /* U+F077 "" */
    0x0, 0x0, 0x30, 0x0, 0x0, 0x0, 0xaf, 0x60,
    0x0, 0x0, 0xaf, 0xef, 0x60, 0x0, 0xaf, 0x90,
    0xcf, 0x60, 0x9f, 0x80, 0x0, 0xcf, 0x57, 0x80,
    0x0, 0x0, 0xa4,

    /* U+F078 "" */
    0x11, 0x0, 0x0, 0x2, 0xc, 0xe2, 0x0, 0x5,
    0xf8, 0x3f, 0xe2, 0x5, 0xfd, 0x10, 0x3f, 0xe7,
    0xfd, 0x10, 0x0, 0x3f, 0xfd, 0x10, 0x0, 0x0,
    0x3b, 0x10, 0x0,

    /* U+F079 "" */
    0x0, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0xc3, 0xff, 0xff, 0xf5, 0x0, 0xbe, 0xfe, 0xb3,
    0x44, 0x4e, 0x60, 0x4, 0x3f, 0x34, 0x0, 0x0,
    0xd6, 0x0, 0x2, 0xf2, 0x0, 0x0, 0xd, 0x60,
    0x0, 0x2f, 0x20, 0x0, 0x8c, 0xea, 0xf1, 0x1,
    0xff, 0xff, 0xf7, 0xdf, 0xf7, 0x0, 0x4, 0x44,
    0x44, 0x11, 0xc7, 0x0,

    /* U+F07B "" */
    0x58, 0x88, 0x20, 0x0, 0x0, 0xff, 0xff, 0xe4,
    0x44, 0x41, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xcf, 0xff, 0xff, 0xff, 0xfc,

    /* U+F093 "" */
    0x0, 0x0, 0x33, 0x0, 0x0, 0x0, 0x3, 0xff,
    0x40, 0x0, 0x0, 0x3f, 0xff, 0xf4, 0x0, 0x2,
    0xff, 0xff, 0xff, 0x20, 0x0, 0x36, 0xff, 0x63,
    0x0, 0x0, 0x4, 0xff, 0x40, 0x0, 0x0, 0x4,
    0xff, 0x40, 0x0, 0x9a, 0xa5, 0xff, 0x5a, 0xa9,
    0xff, 0xff, 0xdd, 0xfe, 0xdf, 0xff, 0xff, 0xff,
    0xfc, 0xbe, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F095 "" */
    0x0, 0x0, 0x0, 0x0, 0xa8, 0x40, 0x0, 0x0,
    0x0, 0x5f, 0xff, 0x0, 0x0, 0x0, 0xc, 0xff,
    0xe0, 0x0, 0x0, 0x0, 0x6f, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0x50, 0x0, 0x0, 0x0, 0x5f,
    0xd0, 0x0, 0x39, 0x10, 0x4f, 0xf4, 0x0, 0xbf,
    0xfc, 0x9f, 0xf6, 0x0, 0xd, 0xff, 0xff, 0xe4,
    0x0, 0x0, 0x9f, 0xfd, 0x81, 0x0, 0x0, 0x1,
    0x31, 0x0, 0x0, 0x0, 0x0,

    /* U+F0C4 "" */
    0x0, 0x0, 0x0, 0x0, 0x6, 0xfe, 0x30, 0x5,
    0xc6, 0xe7, 0xbb, 0x5, 0xff, 0x4d, 0xbd, 0xb4,
    0xff, 0x40, 0x3c, 0xff, 0xff, 0x40, 0x0, 0x8,
    0xff, 0xb0, 0x0, 0x6f, 0xff, 0xdf, 0x80, 0xe,
    0x7b, 0xb2, 0xef, 0x80, 0xdb, 0xd9, 0x2, 0xef,
    0x73, 0xca, 0x10, 0x2, 0x72,

    /* U+F0C5 "" */
    0x0, 0x5d, 0xdd, 0x48, 0x0, 0x8, 0xff, 0xf6,
    0xf8, 0xcc, 0x8f, 0xff, 0x84, 0x3f, 0xe8, 0xff,
    0xff, 0xfc, 0xfe, 0x8f, 0xff, 0xff, 0xcf, 0xe8,
    0xff, 0xff, 0xfc, 0xfe, 0x8f, 0xff, 0xff, 0xcf,
    0xe7, 0xff, 0xff, 0xfc, 0xff, 0x46, 0x66, 0x66,
    0x3f, 0xff, 0xff, 0xf4, 0x0, 0x34, 0x44, 0x43,
    0x0, 0x0,

    /* U+F0C7 "" */
    0x2, 0x22, 0x22, 0x0, 0xe, 0xff, 0xff, 0xfe,
    0x20, 0xf5, 0x22, 0x22, 0xfe, 0x1f, 0x40, 0x0,
    0xe, 0xf8, 0xf7, 0x44, 0x44, 0xff, 0x9f, 0xff,
    0xff, 0xff, 0xf9, 0xff, 0xf6, 0xc, 0xff, 0x9f,
    0xff, 0x20, 0x9f, 0xf9, 0xff, 0xfc, 0x7f, 0xff,
    0x9a, 0xdd, 0xdd, 0xdd, 0xd4,

    /* U+F0C9 "" */
    0x67, 0x77, 0x77, 0x77, 0x4e, 0xff, 0xff, 0xff,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0x6, 0x77, 0x77,
    0x77, 0x74, 0xef, 0xff, 0xff, 0xff, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x45, 0x55, 0x55, 0x55, 0x3f,
    0xff, 0xff, 0xff, 0xfb, 0x11, 0x11, 0x11, 0x11,
    0x0,

    /* U+F0E0 "" */
    0x58, 0x88, 0x88, 0x88, 0x84, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x6f, 0xff, 0xff, 0xff, 0xf6, 0xc5,
    0xdf, 0xff, 0xfd, 0x5c, 0xfe, 0x6a, 0xff, 0xa5,
    0xef, 0xff, 0xf9, 0x55, 0x9f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xcf, 0xff, 0xff, 0xff, 0xfc,

    /* U+F0E7 "" */
    0x3, 0xaa, 0xa2, 0x0, 0x7, 0xff, 0xf2, 0x0,
    0x9, 0xff, 0xd0, 0x0, 0xb, 0xff, 0xd8, 0x81,
    0xe, 0xff, 0xff, 0xe1, 0xe, 0xff, 0xff, 0x60,
    0x0, 0x5, 0xfd, 0x0, 0x0, 0x9, 0xf4, 0x0,
    0x0, 0xd, 0xa0, 0x0, 0x0, 0xf, 0x20, 0x0,
    0x0, 0x2, 0x0, 0x0,

    /* U+F0EA "" */
    0x1, 0x79, 0x11, 0x0, 0xf, 0xfc, 0x9f, 0xf4,
    0x0, 0xff, 0xfd, 0xcc, 0x30, 0xf, 0xfa, 0x79,
    0x93, 0x40, 0xff, 0x8e, 0xff, 0x6f, 0x5f, 0xf8,
    0xef, 0xf7, 0x64, 0xff, 0x8e, 0xff, 0xff, 0xcf,
    0xf8, 0xef, 0xff, 0xfc, 0x46, 0x3e, 0xff, 0xff,
    0xc0, 0x0, 0xdf, 0xff, 0xfc, 0x0, 0x2, 0x44,
    0x44, 0x20,

    /* U+F0F3 "" */
    0x0, 0x1, 0x90, 0x0, 0x0, 0x2, 0xaf, 0x81,
    0x0, 0x2, 0xff, 0xff, 0xd0, 0x0, 0x9f, 0xff,
    0xff, 0x50, 0xc, 0xff, 0xff, 0xf8, 0x0, 0xef,
    0xff, 0xff, 0xa0, 0x3f, 0xff, 0xff, 0xfe, 0xd,
    0xff, 0xff, 0xff, 0xf9, 0x46, 0x66, 0x66, 0x66,
    0x20, 0x0, 0xbf, 0x70, 0x0, 0x0, 0x0, 0x30,
    0x0, 0x0,

    /* U+F11C "" */
    0x24, 0x44, 0x44, 0x44, 0x44, 0x30, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf3, 0xf4, 0xa0, 0xa0, 0xb0,
    0xb0, 0xf4, 0xff, 0xbe, 0xae, 0xae, 0xaf, 0xf4,
    0xff, 0x3a, 0xa, 0xa, 0xf, 0xf4, 0xfb, 0xea,
    0xaa, 0xaa, 0xea, 0xf4, 0xf7, 0xb4, 0x44, 0x44,
    0xc4, 0xf4, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xe1,

    /* U+F124 "" */
    0x0, 0x0, 0x0, 0x0, 0x29, 0x70, 0x0, 0x0,
    0x0, 0x3a, 0xff, 0xe0, 0x0, 0x0, 0x4b, 0xff,
    0xff, 0x70, 0x0, 0x5d, 0xff, 0xff, 0xff, 0x10,
    0xc, 0xff, 0xff, 0xff, 0xf9, 0x0, 0xa, 0xee,
    0xef, 0xff, 0xf2, 0x0, 0x0, 0x0, 0xa, 0xff,
    0xa0, 0x0, 0x0, 0x0, 0xa, 0xff, 0x30, 0x0,
    0x0, 0x0, 0xa, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x20,
    0x0, 0x0,

    /* U+F15B "" */
    0xef, 0xff, 0x5b, 0x0, 0xff, 0xff, 0x6f, 0xb0,
    0xff, 0xff, 0x68, 0x83, 0xff, 0xff, 0xfd, 0xd6,
    0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xf7,
    0x0, 0x0, 0x0, 0x0,

    /* U+F1EB "" */
    0x0, 0x0, 0x1, 0x10, 0x0, 0x0, 0x0, 0x5,
    0xbf, 0xff, 0xfd, 0x81, 0x0, 0x2c, 0xfe, 0xa8,
    0x78, 0xcf, 0xf7, 0xd, 0xf7, 0x0, 0x0, 0x0,
    0x3c, 0xf5, 0x22, 0x5, 0xbe, 0xfd, 0x81, 0x5,
    0x0, 0x9, 0xfe, 0xa9, 0xcf, 0xe2, 0x0, 0x0,
    0x37, 0x0, 0x0, 0x38, 0x0, 0x0, 0x0, 0x0,
    0x8c, 0x20, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x58, 0x10, 0x0,
    0x0,

    /* U+F240 "" */
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xf9, 0xf, 0x75,
    0x55, 0x55, 0x55, 0x5a, 0xf2, 0xf6, 0xff, 0xff,
    0xff, 0xfd, 0x4f, 0x5f, 0x6f, 0xff, 0xff, 0xff,
    0xd1, 0xf5, 0xf5, 0x77, 0x77, 0x77, 0x76, 0x8f,
    0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd0, 0x24,
    0x44, 0x44, 0x44, 0x44, 0x41, 0x0,

    /* U+F241 "" */
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xf9, 0xf, 0x75,
    0x55, 0x55, 0x54, 0x4a, 0xf2, 0xf6, 0xff, 0xff,
    0xff, 0x0, 0x4f, 0x5f, 0x6f, 0xff, 0xff, 0xf0,
    0x1, 0xf5, 0xf5, 0x77, 0x77, 0x77, 0x0, 0x8f,
    0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd0, 0x24,
    0x44, 0x44, 0x44, 0x44, 0x41, 0x0,

    /* U+F242 "" */
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xf9, 0xf, 0x75,
    0x55, 0x54, 0x44, 0x4a, 0xf2, 0xf6, 0xff, 0xff,
    0x20, 0x0, 0x4f, 0x5f, 0x6f, 0xff, 0xf2, 0x0,
    0x1, 0xf5, 0xf5, 0x77, 0x77, 0x10, 0x0, 0x8f,
    0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd0, 0x24,
    0x44, 0x44, 0x44, 0x44, 0x41, 0x0,

    /* U+F243 "" */
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xf9, 0xf, 0x75,
    0x54, 0x44, 0x44, 0x4a, 0xf2, 0xf6, 0xff, 0x50,
    0x0, 0x0, 0x4f, 0x5f, 0x6f, 0xf5, 0x0, 0x0,
    0x1, 0xf5, 0xf5, 0x77, 0x20, 0x0, 0x0, 0x8f,
    0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd0, 0x24,
    0x44, 0x44, 0x44, 0x44, 0x41, 0x0,

    /* U+F244 "" */
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xfa, 0xf, 0x74,
    0x44, 0x44, 0x44, 0x4a, 0xf2, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0x5f, 0x40, 0x0, 0x0, 0x0,
    0x1, 0xf5, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd0, 0x24,
    0x44, 0x44, 0x44, 0x44, 0x41, 0x0,

    /* U+F287 "" */
    0x0, 0x0, 0x0, 0x5b, 0x10, 0x0, 0x0, 0x0,
    0x3, 0xbd, 0xf4, 0x0, 0x0, 0x0, 0x0, 0xa1,
    0x3, 0x0, 0x0, 0xa, 0xf7, 0x39, 0x0, 0x0,
    0x7, 0x60, 0xff, 0xea, 0xbf, 0xaa, 0xaa, 0xdf,
    0x45, 0xa3, 0x0, 0x93, 0x0, 0x4, 0x10, 0x0,
    0x0, 0x1, 0xb8, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x2, 0x9f, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x10, 0x0, 0x0,

    /* U+F293 "" */
    0x0, 0x6b, 0xca, 0x40, 0x0, 0x9f, 0xf6, 0xff,
    0x40, 0x1f, 0xff, 0x26, 0xfb, 0x4, 0xf6, 0xb4,
    0x6b, 0xf0, 0x6f, 0xf4, 0x6, 0xff, 0x6, 0xff,
    0x90, 0xbf, 0xf0, 0x5f, 0x95, 0x34, 0xcf, 0x2,
    0xfb, 0xf3, 0x4d, 0xc0, 0xc, 0xff, 0x3d, 0xf7,
    0x0, 0x1b, 0xfe, 0xf9, 0x0, 0x0, 0x0, 0x10,
    0x0, 0x0,

    /* U+F2ED "" */
    0x12, 0x3b, 0xca, 0x22, 0x1f, 0xff, 0xff, 0xff,
    0xfb, 0x36, 0x66, 0x66, 0x66, 0x16, 0xff, 0xff,
    0xff, 0xf2, 0x6f, 0x6f, 0x6f, 0x7f, 0x26, 0xf6,
    0xf6, 0xf7, 0xf2, 0x6f, 0x6f, 0x6f, 0x7f, 0x26,
    0xf6, 0xf6, 0xf7, 0xf2, 0x6f, 0x6f, 0x6f, 0x7f,
    0x24, 0xff, 0xff, 0xff, 0xf1, 0x3, 0x44, 0x44,
    0x42, 0x0,

    /* U+F304 "" */
    0x0, 0x0, 0x0, 0x0, 0x97, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xf8, 0x0, 0x0, 0x0, 0xa5, 0xef,
    0xe0, 0x0, 0x0, 0xbf, 0xe5, 0xd4, 0x0, 0x0,
    0xbf, 0xff, 0xe0, 0x0, 0x0, 0xbf, 0xff, 0xf4,
    0x0, 0x0, 0xbf, 0xff, 0xf4, 0x0, 0x0, 0xaf,
    0xff, 0xf4, 0x0, 0x0, 0xd, 0xff, 0xf4, 0x0,
    0x0, 0x0, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x2,
    0x20, 0x0, 0x0, 0x0, 0x0,

    /* U+F55A "" */
    0x0, 0x5, 0x88, 0x88, 0x88, 0x86, 0x0, 0x8,
    0xff, 0xff, 0xff, 0xff, 0xf6, 0x8, 0xff, 0xf9,
    0x6f, 0x69, 0xff, 0x88, 0xff, 0xff, 0xc1, 0x21,
    0xcf, 0xf8, 0xdf, 0xff, 0xff, 0x50, 0x5f, 0xff,
    0x82, 0xef, 0xff, 0x71, 0x91, 0x7f, 0xf8, 0x2,
    0xef, 0xfe, 0xdf, 0xde, 0xff, 0x70, 0x2, 0xdf,
    0xff, 0xff, 0xff, 0xe3,

    /* U+F7C2 "" */
    0x1, 0xdf, 0xff, 0xe5, 0x1d, 0x6c, 0x5a, 0xab,
    0xdf, 0x3b, 0x18, 0x8b, 0xff, 0xdf, 0xde, 0xeb,
    0xff, 0xff, 0xff, 0xfb, 0xff, 0xff, 0xff, 0xfb,
    0xff, 0xff, 0xff, 0xfb, 0xff, 0xff, 0xff, 0xfb,
    0xff, 0xff, 0xff, 0xfb, 0xbf, 0xff, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0x0,

    /* U+F8A2 "" */
    0x0, 0x0, 0x0, 0x0, 0xa, 0x0, 0x8, 0x10,
    0x0, 0x7, 0xf0, 0xb, 0xf2, 0x0, 0x0, 0x8f,
    0xb, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x7f, 0xfa,
    0x99, 0x99, 0x99, 0x0, 0x6f, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x30, 0x0, 0x0, 0x0, 0x0
};

/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 43, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 0, .adv_w = 43, .box_w = 2, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7, .adv_w = 63, .box_w = 4, .box_h = 3, .ofs_x = 0, .ofs_y = 4},
    {.bitmap_index = 13, .adv_w = 112, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 38, .adv_w = 99, .box_w = 6, .box_h = 9, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 65, .adv_w = 135, .box_w = 9, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 97, .adv_w = 110, .box_w = 7, .box_h = 8, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 125, .adv_w = 34, .box_w = 2, .box_h = 3, .ofs_x = 0, .ofs_y = 4},
    {.bitmap_index = 128, .adv_w = 54, .box_w = 4, .box_h = 9, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 146, .adv_w = 54, .box_w = 3, .box_h = 9, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 160, .adv_w = 64, .box_w = 4, .box_h = 4, .ofs_x = 0, .ofs_y = 3},
    {.bitmap_index = 168, .adv_w = 93, .box_w = 6, .box_h = 5, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 183, .adv_w = 36, .box_w = 2, .box_h = 3, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 186, .adv_w = 61, .box_w = 4, .box_h = 1, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 188, .adv_w = 36, .box_w = 2, .box_h = 2, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 190, .adv_w = 56, .box_w = 5, .box_h = 9, .ofs_x = -1, .ofs_y = -1},
    {.bitmap_index = 213, .adv_w = 107, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 238, .adv_w = 59, .box_w = 3, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 249, .adv_w = 92, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 270, .adv_w = 92, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 291, .adv_w = 107, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 316, .adv_w = 92, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 337, .adv_w = 99, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 358, .adv_w = 96, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 379, .adv_w = 103, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 400, .adv_w = 99, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 421, .adv_w = 36, .box_w = 2, .box_h = 5, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 426, .adv_w = 36, .box_w = 2, .box_h = 7, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 433, .adv_w = 93, .box_w = 6, .box_h = 5, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 448, .adv_w = 93, .box_w = 6, .box_h = 3, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 457, .adv_w = 93, .box_w = 6, .box_h = 5, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 472, .adv_w = 92, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 493, .adv_w = 165, .box_w = 10, .box_h = 9, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 538, .adv_w = 117, .box_w = 9, .box_h = 7, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 570, .adv_w = 121, .box_w = 7, .box_h = 7, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 595, .adv_w = 116, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 620, .adv_w = 132, .box_w = 7, .box_h = 7, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 645, .adv_w = 107, .box_w = 6, .box_h = 7, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 666, .adv_w = 102, .box_w = 5, .box_h = 7, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 684, .adv_w = 124, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 709, .adv_w = 130, .box_w = 7, .box_h = 7, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 734, .adv_w = 50, .box_w = 2, .box_h = 7, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 741, .adv_w = 82, .box_w = 6, .box_h = 7, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 762, .adv_w = 115, .box_w = 7, .box_h = 7, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 787, .adv_w = 95, .box_w = 5, .box_h = 7, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 805, .adv_w = 153, .box_w = 8, .box_h = 7, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 833, .adv_w = 130, .box_w = 7, .box_h = 7, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 858, .adv_w = 134, .box_w = 8, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 886, .adv_w = 116, .box_w = 6, .box_h = 7, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 907, .adv_w = 134, .box_w = 9, .box_h = 8, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 943, .adv_w = 116, .box_w = 6, .box_h = 7, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 964, .adv_w = 99, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 985, .adv_w = 94, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1006, .adv_w = 127, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1031, .adv_w = 114, .box_w = 9, .box_h = 7, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 1063, .adv_w = 180, .box_w = 11, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1102, .adv_w = 108, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1127, .adv_w = 104, .box_w = 8, .box_h = 7, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 1155, .adv_w = 105, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1180, .adv_w = 53, .box_w = 3, .box_h = 9, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 1194, .adv_w = 56, .box_w = 5, .box_h = 9, .ofs_x = -1, .ofs_y = -1},
    {.bitmap_index = 1217, .adv_w = 53, .box_w = 3, .box_h = 9, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 1231, .adv_w = 93, .box_w = 6, .box_h = 4, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 1243, .adv_w = 80, .box_w = 5, .box_h = 1, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 1246, .adv_w = 96, .box_w = 3, .box_h = 1, .ofs_x = 1, .ofs_y = 6},
    {.bitmap_index = 1248, .adv_w = 96, .box_w = 6, .box_h = 5, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1263, .adv_w = 109, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1288, .adv_w = 91, .box_w = 6, .box_h = 5, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1303, .adv_w = 109, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1324, .adv_w = 98, .box_w = 6, .box_h = 5, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1339, .adv_w = 56, .box_w = 4, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1353, .adv_w = 110, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 1374, .adv_w = 109, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1395, .adv_w = 45, .box_w = 3, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1406, .adv_w = 45, .box_w = 4, .box_h = 9, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 1424, .adv_w = 99, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1449, .adv_w = 45, .box_w = 2, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1456, .adv_w = 169, .box_w = 10, .box_h = 5, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1481, .adv_w = 109, .box_w = 6, .box_h = 5, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1496, .adv_w = 102, .box_w = 6, .box_h = 5, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1511, .adv_w = 109, .box_w = 7, .box_h = 7, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 1536, .adv_w = 109, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 1557, .adv_w = 66, .box_w = 4, .box_h = 5, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1567, .adv_w = 80, .box_w = 5, .box_h = 5, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1580, .adv_w = 66, .box_w = 4, .box_h = 6, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1592, .adv_w = 108, .box_w = 6, .box_h = 5, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1607, .adv_w = 89, .box_w = 7, .box_h = 5, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 1625, .adv_w = 144, .box_w = 9, .box_h = 5, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1648, .adv_w = 88, .box_w = 6, .box_h = 5, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1663, .adv_w = 89, .box_w = 7, .box_h = 7, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 1688, .adv_w = 83, .box_w = 5, .box_h = 5, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1701, .adv_w = 56, .box_w = 4, .box_h = 9, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 1719, .adv_w = 48, .box_w = 1, .box_h = 9, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 1724, .adv_w = 56, .box_w = 3, .box_h = 9, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 1738, .adv_w = 93, .box_w = 6, .box_h = 2, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 1744, .adv_w = 67, .box_w = 4, .box_h = 4, .ofs_x = 0, .ofs_y = 3},
    {.bitmap_index = 1752, .adv_w = 50, .box_w = 3, .box_h = 2, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 1755, .adv_w = 160, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 1816, .adv_w = 160, .box_w = 10, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1856, .adv_w = 160, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 1906, .adv_w = 160, .box_w = 10, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1946, .adv_w = 110, .box_w = 7, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1974, .adv_w = 160, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2029, .adv_w = 160, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2084, .adv_w = 180, .box_w = 12, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 2144, .adv_w = 160, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2199, .adv_w = 180, .box_w = 12, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2247, .adv_w = 160, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2302, .adv_w = 80, .box_w = 5, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2322, .adv_w = 120, .box_w = 8, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2354, .adv_w = 180, .box_w = 12, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 2414, .adv_w = 160, .box_w = 10, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2454, .adv_w = 110, .box_w = 7, .box_h = 11, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2493, .adv_w = 140, .box_w = 7, .box_h = 10, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 2528, .adv_w = 140, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2578, .adv_w = 140, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 2623, .adv_w = 140, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 2668, .adv_w = 140, .box_w = 7, .box_h = 10, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 2703, .adv_w = 140, .box_w = 10, .box_h = 10, .ofs_x = -1, .ofs_y = -1},
    {.bitmap_index = 2753, .adv_w = 100, .box_w = 6, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 2783, .adv_w = 100, .box_w = 6, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 2813, .adv_w = 140, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 2858, .adv_w = 140, .box_w = 9, .box_h = 3, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 2872, .adv_w = 180, .box_w = 12, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2920, .adv_w = 200, .box_w = 13, .box_h = 11, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2992, .adv_w = 180, .box_w = 13, .box_h = 11, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 3064, .adv_w = 160, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3114, .adv_w = 140, .box_w = 9, .box_h = 6, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 3141, .adv_w = 140, .box_w = 9, .box_h = 6, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 3168, .adv_w = 200, .box_w = 13, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3220, .adv_w = 160, .box_w = 10, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3260, .adv_w = 160, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3315, .adv_w = 160, .box_w = 11, .box_h = 11, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 3376, .adv_w = 140, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3421, .adv_w = 140, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3471, .adv_w = 140, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3516, .adv_w = 140, .box_w = 9, .box_h = 9, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3557, .adv_w = 160, .box_w = 10, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3597, .adv_w = 100, .box_w = 8, .box_h = 11, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 3641, .adv_w = 140, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3691, .adv_w = 140, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3741, .adv_w = 180, .box_w = 12, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3789, .adv_w = 160, .box_w = 12, .box_h = 11, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 3855, .adv_w = 120, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3899, .adv_w = 200, .box_w = 13, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3964, .adv_w = 200, .box_w = 13, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4010, .adv_w = 200, .box_w = 13, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4056, .adv_w = 200, .box_w = 13, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4102, .adv_w = 200, .box_w = 13, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4148, .adv_w = 200, .box_w = 13, .box_h = 7, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4194, .adv_w = 200, .box_w = 13, .box_h = 9, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4253, .adv_w = 140, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4303, .adv_w = 140, .box_w = 9, .box_h = 11, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4353, .adv_w = 160, .box_w = 11, .box_h = 11, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 4414, .adv_w = 200, .box_w = 13, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4466, .adv_w = 120, .box_w = 8, .box_h = 11, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4510, .adv_w = 161, .box_w = 11, .box_h = 7, .ofs_x = 0, .ofs_y = 0}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/

static const uint16_t unicode_list_1[] = {
    0x0, 0x1f72, 0xef51, 0xef58, 0xef5b, 0xef5c, 0xef5d, 0xef61,
    0xef63, 0xef65, 0xef69, 0xef6c, 0xef71, 0xef76, 0xef77, 0xef78,
    0xef8e, 0xef93, 0xef98, 0xef9b, 0xef9c, 0xef9d, 0xefa1, 0xefa2,
    0xefa3, 0xefa4, 0xefb7, 0xefb8, 0xefbe, 0xefc0, 0xefc1, 0xefc4,
    0xefc7, 0xefc8, 0xefc9, 0xefcb, 0xefe3, 0xefe5, 0xf014, 0xf015,
    0xf017, 0xf019, 0xf030, 0xf037, 0xf03a, 0xf043, 0xf06c, 0xf074,
    0xf0ab, 0xf13b, 0xf190, 0xf191, 0xf192, 0xf193, 0xf194, 0xf1d7,
    0xf1e3, 0xf23d, 0xf254, 0xf4aa, 0xf712, 0xf7f2
};

/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] = {
    {
        .range_start = 32, .range_length = 95, .glyph_id_start = 1,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    },
    {
        .range_start = 176, .range_length = 63475, .glyph_id_start = 96,
        .unicode_list = unicode_list_1, .glyph_id_ofs_list = NULL, .list_length = 62, .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY
    }
};

/*-----------------
 *    KERNING
 *----------------*/

/*Map glyph_ids to kern left classes*/
static const uint8_t kern_left_class_mapping[] = {
    0, 0, 1, 2, 0, 3, 4, 5,
    2, 6, 7, 8, 9, 10, 9, 10,
    11, 12, 0, 13, 14, 15, 16, 17,
    18, 19, 12, 20, 20, 0, 0, 0,
    21, 22, 23, 24, 25, 22, 26, 27,
    28, 29, 29, 30, 31, 32, 29, 29,
    22, 33, 34, 35, 3, 36, 30, 37,
    37, 38, 39, 40, 41, 42, 43, 0,
    44, 0, 45, 46, 47, 48, 49, 50,
    51, 45, 52, 52, 53, 48, 45, 45,
    46, 46, 54, 55, 56, 57, 51, 58,
    58, 59, 58, 60, 41, 0, 0, 9,
    61, 9, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0
};

/*Map glyph_ids to kern right classes*/
static const uint8_t kern_right_class_mapping[] = {
    0, 0, 1, 2, 0, 3, 4, 5,
    2, 6, 7, 8, 9, 10, 9, 10,
    11, 12, 13, 14, 15, 16, 17, 12,
    18, 19, 20, 21, 21, 0, 0, 0,
    22, 23, 24, 25, 23, 25, 25, 25,
    23, 25, 25, 26, 25, 25, 25, 25,
    23, 25, 23, 25, 3, 27, 28, 29,
    29, 30, 31, 32, 33, 34, 35, 0,
    36, 0, 37, 38, 39, 39, 39, 0,
    39, 38, 40, 41, 38, 38, 42, 42,
    39, 42, 39, 42, 43, 44, 45, 46,
    46, 47, 46, 48, 0, 0, 35, 9,
    49, 9, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0
};

/*Kern values between classes*/
static const int8_t kern_class_values[] = {
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 2, 0, 0, 0,
    0, 1, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 7, 0, 4, -4, 0, 0,
    0, 0, -9, -10, 1, 8, 4, 3,
    -6, 1, 8, 0, 7, 2, 5, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 10, 1, -1, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 3, 0, -5, 0, 0, 0, 0,
    0, -3, 3, 3, 0, 0, -2, 0,
    -1, 2, 0, -2, 0, -2, -1, -3,
    0, 0, 0, 0, -2, 0, 0, -2,
    -2, 0, 0, -2, 0, -3, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -2,
    -2, 0, -2, 0, -4, 0, -19, 0,
    0, -3, 0, 3, 5, 0, 0, -3,
    2, 2, 5, 3, -3, 3, 0, 0,
    -9, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -6, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -4, -2, -8, 0, -6,
    -1, 0, 0, 0, 0, 0, 6, 0,
    -5, -1, 0, 0, 0, -3, 0, 0,
    -1, -12, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -13, -1, 6,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -7, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 5,
    0, 2, 0, 0, -3, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 6, 1,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -6, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 1,
    3, 2, 5, -2, 0, 0, 3, -2,
    -5, -22, 1, 4, 3, 0, -2, 0,
    6, 0, 5, 0, 5, 0, -15, 0,
    -2, 5, 0, 5, -2, 3, 2, 0,
    0, 0, -2, 0, 0, -3, 13, 0,
    13, 0, 5, 0, 7, 2, 3, 5,
    0, 0, 0, -6, 0, 0, 0, 0,
    0, -1, 0, 1, -3, -2, -3, 1,
    0, -2, 0, 0, 0, -6, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -10, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -9, 0, -10, 0, 0, 0,
    0, -1, 0, 16, -2, -2, 2, 2,
    -1, 0, -2, 2, 0, 0, -8, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -16, 0, 2, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -10, 0, 10, 0, 0, -6, 0,
    5, 0, -11, -16, -11, -3, 5, 0,
    0, -11, 0, 2, -4, 0, -2, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 4, 5, -20, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 8, 0, 1, 0, 0, 0,
    0, 0, 1, 1, -2, -3, 0, 0,
    0, -2, 0, 0, -1, 0, 0, 0,
    -3, 0, -1, 0, -4, -3, 0, -4,
    -5, -5, -3, 0, -3, 0, -3, 0,
    0, 0, 0, -1, 0, 0, 2, 0,
    1, -2, 0, 0, 0, 0, 0, 2,
    -1, 0, 0, 0, -1, 2, 2, 0,
    0, 0, 0, -3, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 2, -1, 0,
    -2, 0, -3, 0, 0, -1, 0, 5,
    0, 0, -2, 0, 0, 0, 0, 0,
    0, 0, -1, -1, 0, 0, -2, 0,
    -2, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -1, -1, 0, -2, -2, 0,
    0, 0, 0, 0, 0, 0, 0, -1,
    0, -2, -2, -2, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -1, 0, 0,
    0, 0, -1, -2, 0, -2, 0, -5,
    -1, -5, 3, 0, 0, -3, 2, 3,
    4, 0, -4, 0, -2, 0, 0, -8,
    2, -1, 1, -8, 2, 0, 0, 0,
    -8, 0, -8, -1, -14, -1, 0, -8,
    0, 3, 4, 0, 2, 0, 0, 0,
    0, 0, 0, -3, -2, 0, -5, 0,
    0, 0, -2, 0, 0, 0, -2, 0,
    0, 0, 0, 0, -1, -1, 0, -1,
    -2, 0, 0, 0, 0, 0, 0, 0,
    -2, -2, 0, -1, -2, -1, 0, 0,
    -2, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -1, -1, 0, -2,
    0, -1, 0, -3, 2, 0, 0, -2,
    1, 2, 2, 0, 0, 0, 0, 0,
    0, -1, 0, 0, 0, 0, 0, 1,
    0, 0, -2, 0, -2, -1, -2, 0,
    0, 0, 0, 0, 0, 0, 1, 0,
    -1, 0, 0, 0, 0, -2, -2, 0,
    -3, 0, 5, -1, 0, -5, 0, 0,
    4, -8, -8, -7, -3, 2, 0, -1,
    -10, -3, 0, -3, 0, -3, 2, -3,
    -10, 0, -4, 0, 0, 1, 0, 1,
    -1, 0, 2, 0, -5, -6, 0, -8,
    -4, -3, -4, -5, -2, -4, 0, -3,
    -4, 1, 0, 0, 0, -2, 0, 0,
    0, 1, 0, 2, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -2,
    0, -1, 0, 0, -2, 0, -3, -4,
    -4, 0, 0, -5, 0, 0, 0, 0,
    0, 0, -1, 0, 0, 0, 0, 1,
    -1, 0, 0, 0, 2, 0, 0, 0,
    0, 0, 0, 0, 0, 8, 0, 0,
    0, 0, 0, 0, 1, 0, 0, 0,
    -2, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -3, 0, 2, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -1, 0, 0, 0,
    -3, 0, 0, 0, 0, -8, -5, 0,
    0, 0, -2, -8, 0, 0, -2, 2,
    0, -4, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -3, 0, 0, -3,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 2, 0, -3, 0,
    0, 0, 0, 2, 0, 1, -3, -3,
    0, -2, -2, -2, 0, 0, 0, 0,
    0, 0, -5, 0, -2, 0, -2, -2,
    0, -4, -4, -5, -1, 0, -3, 0,
    -5, 0, 0, 0, 0, 13, 0, 0,
    1, 0, 0, -2, 0, 2, 0, -7,
    0, 0, 0, 0, 0, -15, -3, 5,
    5, -1, -7, 0, 2, -2, 0, -8,
    -1, -2, 2, -11, -2, 2, 0, 2,
    -6, -2, -6, -5, -7, 0, 0, -10,
    0, 9, 0, 0, -1, 0, 0, 0,
    -1, -1, -2, -4, -5, 0, -15, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -2, 0, -1, -2, -2, 0, 0,
    -3, 0, -2, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -3, 0, 0, 3,
    0, 2, 0, -4, 2, -1, 0, -4,
    -2, 0, -2, -2, -1, 0, -2, -3,
    0, 0, -1, 0, -1, -3, -2, 0,
    0, -2, 0, 2, -1, 0, -4, 0,
    0, 0, -3, 0, -3, 0, -3, -3,
    2, 0, 0, 0, 0, 0, 0, 0,
    0, -3, 2, 0, -2, 0, -1, -2,
    -5, -1, -1, -1, 0, -1, -2, 0,
    0, 0, 0, 0, 0, -2, -1, -1,
    0, 0, 0, 0, 2, -1, 0, -1,
    0, 0, 0, -1, -2, -1, -1, -2,
    -1, 0, 1, 6, 0, 0, -4, 0,
    -1, 3, 0, -2, -7, -2, 2, 0,
    0, -8, -3, 2, -3, 1, 0, -1,
    -1, -5, 0, -2, 1, 0, 0, -3,
    0, 0, 0, 2, 2, -3, -3, 0,
    -3, -2, -2, -2, -2, 0, -3, 1,
    -3, -3, 5, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 2, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -3, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -1, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -1, -2,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -2, 0, 0, -2,
    0, 0, -2, -2, 0, 0, 0, 0,
    -2, 0, 0, 0, 0, -1, 0, 0,
    0, 0, 0, -1, 0, 0, 0, 0,
    -2, 0, -3, 0, 0, 0, -5, 0,
    1, -4, 3, 0, -1, -8, 0, 0,
    -4, -2, 0, -6, -4, -4, 0, 0,
    -7, -2, -6, -6, -8, 0, -4, 0,
    1, 11, -2, 0, -4, -2, 0, -2,
    -3, -4, -3, -6, -7, -4, -2, 0,
    0, -1, 0, 0, 0, 0, -11, -1,
    5, 4, -4, -6, 0, 0, -5, 0,
    -8, -1, -2, 3, -15, -2, 0, 0,
    0, -10, -2, -8, -2, -12, 0, 0,
    -11, 0, 9, 0, 0, -1, 0, 0,
    0, 0, -1, -1, -6, -1, 0, -10,
    0, 0, 0, 0, -5, 0, -1, 0,
    0, -4, -8, 0, 0, -1, -2, -5,
    -2, 0, -1, 0, 0, 0, 0, -7,
    -2, -5, -5, -1, -3, -4, -2, -3,
    0, -3, -1, -5, -2, 0, -2, -3,
    -2, -3, 0, 1, 0, -1, -5, 0,
    3, 0, -3, 0, 0, 0, 0, 2,
    0, 1, -3, 7, 0, -2, -2, -2,
    0, 0, 0, 0, 0, 0, -5, 0,
    -2, 0, -2, -2, 0, -4, -4, -5,
    -1, 0, -3, 1, 6, 0, 0, 0,
    0, 13, 0, 0, 1, 0, 0, -2,
    0, 2, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -1, -3, 0, 0, 0, 0, 0, -1,
    0, 0, 0, -2, -2, 0, 0, -3,
    -2, 0, 0, -3, 0, 3, -1, 0,
    0, 0, 0, 0, 0, 1, 0, 0,
    0, 0, 2, 3, 1, -1, 0, -5,
    -3, 0, 5, -5, -5, -3, -3, 6,
    3, 2, -14, -1, 3, -2, 0, -2,
    2, -2, -6, 0, -2, 2, -2, -1,
    -5, -1, 0, 0, 5, 3, 0, -4,
    0, -9, -2, 5, -2, -6, 0, -2,
    -5, -5, -2, 6, 2, 0, -2, 0,
    -4, 0, 1, 5, -4, -6, -6, -4,
    5, 0, 0, -12, -1, 2, -3, -1,
    -4, 0, -4, -6, -2, -2, -1, 0,
    0, -4, -3, -2, 0, 5, 4, -2,
    -9, 0, -9, -2, 0, -6, -9, 0,
    -5, -3, -5, -4, 4, 0, 0, -2,
    0, -3, -1, 0, -2, -3, 0, 3,
    -5, 2, 0, 0, -8, 0, -2, -4,
    -3, -1, -5, -4, -5, -4, 0, -5,
    -2, -4, -3, -5, -2, 0, 0, 0,
    8, -3, 0, -5, -2, 0, -2, -3,
    -4, -4, -4, -6, -2, -3, 3, 0,
    -2, 0, -8, -2, 1, 3, -5, -6,
    -3, -5, 5, -2, 1, -15, -3, 3,
    -4, -3, -6, 0, -5, -7, -2, -2,
    -1, -2, -3, -5, 0, 0, 0, 5,
    4, -1, -10, 0, -10, -4, 4, -6,
    -11, -3, -6, -7, -8, -5, 3, 0,
    0, 0, 0, -2, 0, 0, 2, -2,
    3, 1, -3, 3, 0, 0, -5, 0,
    0, 0, 0, 0, 0, -1, 0, 0,
    0, 0, 0, 0, -2, 0, 0, 0,
    0, 1, 5, 0, 0, -2, 0, 0,
    0, 0, -1, -1, -2, 0, 0, 0,
    0, 1, 0, 0, 0, 0, 1, 0,
    -1, 0, 6, 0, 3, 0, 0, -2,
    0, 3, 0, 0, 0, 1, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 5, 0, 4, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -10, 0, -2, 3, 0, 5,
    0, 0, 16, 2, -3, -3, 2, 2,
    -1, 0, -8, 0, 0, 8, -10, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -11, 6, 22, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -10, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -3, 0, 0, -3,
    -1, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -1, 0, -4, 0,
    0, 0, 0, 0, 2, 21, -3, -1,
    5, 4, -4, 2, 0, 0, 2, 2,
    -2, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -21, 4, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -4,
    0, 0, 0, -4, 0, 0, 0, 0,
    -4, -1, 0, 0, 0, -4, 0, -2,
    0, -8, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -11, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -2, 0, 0, -3, 0, -2, 0,
    -4, 0, 0, 0, -3, 2, -2, 0,
    0, -4, -2, -4, 0, 0, -4, 0,
    -2, 0, -8, 0, -2, 0, 0, -13,
    -3, -6, -2, -6, 0, 0, -11, 0,
    -4, -1, 0, 0, 0, 0, 0, 0,
    0, 0, -2, -3, -1, -3, 0, 0,
    0, 0, -4, 0, -4, 2, -2, 3,
    0, -1, -4, -1, -3, -3, 0, -2,
    -1, -1, 1, -4, 0, 0, 0, 0,
    -14, -1, -2, 0, -4, 0, -1, -8,
    -1, 0, 0, -1, -1, 0, 0, 0,
    0, 1, 0, -1, -3, -1, 3, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 2, 0, 0, 0, 0, 0,
    0, -4, 0, -1, 0, 0, 0, -3,
    2, 0, 0, 0, -4, -2, -3, 0,
    0, -4, 0, -2, 0, -8, 0, 0,
    0, 0, -16, 0, -3, -6, -8, 0,
    0, -11, 0, -1, -2, 0, 0, 0,
    0, 0, 0, 0, 0, -2, -2, -1,
    -2, 0, 0, 0, 3, -2, 0, 5,
    8, -2, -2, -5, 2, 8, 3, 4,
    -4, 2, 7, 2, 5, 4, 4, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 10, 8, -3, -2, 0, -1,
    13, 7, 13, 0, 0, 0, 2, 0,
    0, 6, 0, 0, -3, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -1, 0,
    0, 0, 0, 0, 0, 0, 0, 2,
    0, 0, 0, 0, -13, -2, -1, -7,
    -8, 0, 0, -11, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -3, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -1,
    0, 0, 0, 0, 0, 0, 0, 0,
    2, 0, 0, 0, 0, -13, -2, -1,
    -7, -8, 0, 0, -6, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -1, 0, 0, 0, -4, 2, 0, -2,
    1, 3, 2, -5, 0, 0, -1, 2,
    0, 1, 0, 0, 0, 0, -4, 0,
    -1, -1, -3, 0, -1, -6, 0, 10,
    -2, 0, -4, -1, 0, -1, -3, 0,
    -2, -4, -3, -2, 0, 0, 0, -3,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -1, 0, 0, 0, 0, 0, 0,
    0, 0, 2, 0, 0, 0, 0, -13,
    -2, -1, -7, -8, 0, 0, -11, 0,
    0, 0, 0, 0, 0, 8, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -3, 0, -5, -2, -1, 5, -1, -2,
    -6, 0, -1, 0, -1, -4, 0, 4,
    0, 1, 0, 1, -4, -6, -2, 0,
    -6, -3, -4, -7, -6, 0, -3, -3,
    -2, -2, -1, -1, -2, -1, 0, -1,
    0, 2, 0, 2, -1, 0, 5, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -1, -2, -2, 0, 0,
    -4, 0, -1, 0, -3, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -10, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -2, -2, 0, -2,
    0, 0, 0, 0, -1, 0, 0, -3,
    -2, 2, 0, -3, -3, -1, 0, -5,
    -1, -4, -1, -2, 0, -3, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -11, 0, 5, 0, 0, -3, 0,
    0, 0, 0, -2, 0, -2, 0, 0,
    -1, 0, 0, -1, 0, -4, 0, 0,
    7, -2, -5, -5, 1, 2, 2, 0,
    -4, 1, 2, 1, 5, 1, 5, -1,
    -4, 0, 0, -6, 0, 0, -5, -4,
    0, 0, -3, 0, -2, -3, 0, -2,
    0, -2, 0, -1, 2, 0, -1, -5,
    -2, 6, 0, 0, -1, 0, -3, 0,
    0, 2, -4, 0, 2, -2, 1, 0,
    0, -5, 0, -1, 0, 0, -2, 2,
    -1, 0, 0, 0, -7, -2, -4, 0,
    -5, 0, 0, -8, 0, 6, -2, 0,
    -3, 0, 1, 0, -2, 0, -2, -5,
    0, -2, 2, 0, 0, 0, 0, -1,
    0, 0, 2, -2, 0, 0, 0, -2,
    -1, 0, -2, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -10, 0, 4, 0,
    0, -1, 0, 0, 0, 0, 0, 0,
    -2, -2, 0, 0, 0, 3, 0, 4,
    0, 0, 0, 0, 0, -10, -9, 0,
    7, 5, 3, -6, 1, 7, 0, 6,
    0, 3, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 8, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0
};

/*Collect the kern class' data in one place*/
static const lv_font_fmt_txt_kern_classes_t kern_classes = {
    .class_pair_values   = kern_class_values,
    .left_class_mapping  = kern_left_class_mapping,
    .right_class_mapping = kern_right_class_mapping,
    .left_class_cnt      = 61,
    .right_class_cnt     = 49,
};

/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LV_VERSION_CHECK(8, 0, 0)
/*Store all the custom data of the font*/
static  lv_font_fmt_txt_glyph_cache_t cache;
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = &kern_classes,
    .kern_scale = 16,
    .cmap_num = 2,
    .bpp = 4,
    .kern_classes = 1,
    .bitmap_format = 0,
#if LV_VERSION_CHECK(8, 0, 0)
    .cache = &cache
#endif
};

/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LV_VERSION_CHECK(8, 0, 0)
const lv_font_t lv_font_montserrat_10 = {
#else
lv_font_t lv_font_montserrat_10 = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 11,          /*The maximum line height required by the font*/
    .base_line = 2,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = -1,
    .underline_thickness = 1,
#endif
    .dsc = &font_dsc           /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
};

#endif /*#if LV_FONT_MONTSERRAT_10*/
