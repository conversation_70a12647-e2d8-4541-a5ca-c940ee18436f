#MicroXplorer Configuration settings - do not modify
ADC1.Channel-0\#ChannelRegularConversion=ADC_CHANNEL_5
ADC1.Channel-1\#ChannelRegularConversion=ADC_CHANNEL_VREFINT
ADC1.Channel-2\#ChannelRegularConversion=ADC_CHANNEL_TEMPSENSOR
ADC1.ClockPrescaler=ADC_CLOCK_SYNC_PCLK_DIV4
ADC1.ContinuousConvMode=ENABLE
ADC1.DMAContinuousRequests=ENABLE
ADC1.EOCSelection=ADC_EOC_SEQ_CONV
ADC1.IPParameters=Rank-0\#ChannelRegularConversion,Channel-0\#ChannelRegularConversion,SamplingTime-0\#ChannelRegularConversion,OffsetNumber-0\#ChannelRegularConversion,MonitoredBy-0\#ChannelRegularConversion,NbrOfConversionFlag,master,Rank-1\#ChannelRegularConversion,Channel-1\#ChannelRegularConversion,SamplingTime-1\#ChannelRegularConversion,OffsetNumber-1\#ChannelRegularConversion,MonitoredBy-1\#ChannelRegularConversion,Rank-2\#ChannelRegularConversion,Channel-2\#ChannelRegularConversion,SamplingTime-2\#ChannelRegularConversion,OffsetNumber-2\#ChannelRegularConversion,MonitoredBy-2\#ChannelRegularConversion,NbrOfConversion,ContinuousConvMode,DMAContinuousRequests,OversamplingMode,Overrun,EOCSelection,RightBitShift,Ratio,ClockPrescaler
ADC1.MonitoredBy-0\#ChannelRegularConversion=__NULL
ADC1.MonitoredBy-1\#ChannelRegularConversion=__NULL
ADC1.MonitoredBy-2\#ChannelRegularConversion=__NULL
ADC1.NbrOfConversion=3
ADC1.NbrOfConversionFlag=1
ADC1.OffsetNumber-0\#ChannelRegularConversion=ADC_OFFSET_NONE
ADC1.OffsetNumber-1\#ChannelRegularConversion=ADC_OFFSET_NONE
ADC1.OffsetNumber-2\#ChannelRegularConversion=ADC_OFFSET_NONE
ADC1.Overrun=ADC_OVR_DATA_OVERWRITTEN
ADC1.OversamplingMode=ENABLE
ADC1.Rank-0\#ChannelRegularConversion=1
ADC1.Rank-1\#ChannelRegularConversion=2
ADC1.Rank-2\#ChannelRegularConversion=3
ADC1.Ratio=ADC_OVERSAMPLING_RATIO_16
ADC1.RightBitShift=ADC_RIGHTBITSHIFT_4
ADC1.SamplingTime-0\#ChannelRegularConversion=ADC_SAMPLETIME_247CYCLES_5
ADC1.SamplingTime-1\#ChannelRegularConversion=ADC_SAMPLETIME_247CYCLES_5
ADC1.SamplingTime-2\#ChannelRegularConversion=ADC_SAMPLETIME_247CYCLES_5
ADC1.master=1
BOOTPATH.BootPathName=LEGACY
BOOTPATH.IPParameters=BootPathName
BOOTPATH.UserSelectedBootPath=LEGACY
CAD.formats=
CAD.pinconfig=
CAD.provider=
CORTEX_M33_NS.userName=CORTEX_M33
File.Version=6
GPDMA1.CIRCULARMODE_GPDMACH1=ENABLE
GPDMA1.DATAEXCHANGE_GPDMACH2-SIMPLEREQUEST_GPDMACH2=DMA_EXCHANGE_DEST_BYTE|DMA_EXCHANGE_DEST_HALFWORD|DMA_EXCHANGE_SRC_BYTE
GPDMA1.DESTDATAWIDTH_GPDMACH0=DMA_DEST_DATAWIDTH_BYTE
GPDMA1.DESTDATAWIDTH_GPDMACH1=DMA_DEST_DATAWIDTH_HALFWORD
GPDMA1.DESTINC_GPDMACH1=DMA_DINC_INCREMENTED
GPDMA1.DESTINC_GPDMACH2=DMA_DINC_INCREMENTED
GPDMA1.DIRECTION_GPDMACH0=DMA_MEMORY_TO_PERIPH
GPDMA1.DIRECTION_GPDMACH3=DMA_MEMORY_TO_PERIPH
GPDMA1.DMAHANDLE_GPDMACH0=hdmatx
GPDMA1.IPHANDLE_GPDMACH0-SIMPLEREQUEST_GPDMACH0=__NULL
GPDMA1.IPHANDLE_GPDMACH1-SIMPLEREQUEST_GPDMACH1=__NULL
GPDMA1.IPHANDLE_GPDMACH2-SIMPLEREQUEST_GPDMACH2=__NULL
GPDMA1.IPHANDLE_GPDMACH3-SIMPLEREQUEST_GPDMACH3=__NULL
GPDMA1.IPParameters=REQUEST_GPDMACH0,DMAHANDLE_GPDMACH0,DIRECTION_GPDMACH0,SRCINC_GPDMACH0,SRCDATAWIDTH_GPDMACH0,DESTDATAWIDTH_GPDMACH0,REQUEST_GPDMACH1,SRCDATAWIDTH_GPDMACH1,DESTDATAWIDTH_GPDMACH1,DESTINC_GPDMACH1,CIRCULARMODE_GPDMACH1,PRIORITY_LL_CIRCULAR_GPDMACH1,IPHANDLE_GPDMACH1-SIMPLEREQUEST_GPDMACH1,IPHANDLE_GPDMACH0-SIMPLEREQUEST_GPDMACH0,IPHANDLE_GPDMACH3-SIMPLEREQUEST_GPDMACH3,REQUEST_GPDMACH3,PRIORITY_GPDMACH3,DIRECTION_GPDMACH3,SRCINC_GPDMACH3,IPHANDLE_GPDMACH2-SIMPLEREQUEST_GPDMACH2,REQUEST_GPDMACH2,PRIORITY_GPDMACH2,DESTINC_GPDMACH2,DATAEXCHANGE_GPDMACH2-SIMPLEREQUEST_GPDMACH2
GPDMA1.PRIORITY_GPDMACH2=DMA_LOW_PRIORITY_HIGH_WEIGHT
GPDMA1.PRIORITY_GPDMACH3=DMA_LOW_PRIORITY_HIGH_WEIGHT
GPDMA1.PRIORITY_LL_CIRCULAR_GPDMACH1=DMA_LOW_PRIORITY_HIGH_WEIGHT
GPDMA1.REQUEST_GPDMACH0=GPDMA1_REQUEST_OCTOSPI1
GPDMA1.REQUEST_GPDMACH1=GPDMA1_REQUEST_ADC1
GPDMA1.REQUEST_GPDMACH2=GPDMA1_REQUEST_USART3_RX
GPDMA1.REQUEST_GPDMACH3=GPDMA1_REQUEST_USART3_TX
GPDMA1.SRCDATAWIDTH_GPDMACH0=DMA_SRC_DATAWIDTH_BYTE
GPDMA1.SRCDATAWIDTH_GPDMACH1=DMA_SRC_DATAWIDTH_HALFWORD
GPDMA1.SRCINC_GPDMACH0=DMA_SINC_INCREMENTED
GPDMA1.SRCINC_GPDMACH3=DMA_SINC_INCREMENTED
GPIO.groupedBy=Group By Peripherals
I2C1.I2C_Fall_Time=4
I2C1.I2C_Rise_Time=46
I2C1.I2C_Speed_Mode=I2C_Fast
I2C1.IPParameters=Timing,I2C_Speed_Mode,I2C_Rise_Time,I2C_Fall_Time
I2C1.Timing=0x3090216F
KeepUserPlacement=false
LPTIM1.ClockPrescaler=LPTIM_PRESCALER_DIV128
LPTIM1.IPParameters=ClockPrescaler
LPTIM2.ClockPrescaler=LPTIM_PRESCALER_DIV128
LPTIM2.IPParameters=ClockPrescaler
MMTAppReg1.MEMORYMAP.AP=RW_priv_only
MMTAppReg1.MEMORYMAP.AppRegionName=RAM
MMTAppReg1.MEMORYMAP.ContextName=CortexM33
MMTAppReg1.MEMORYMAP.CoreName=ARM Cortex-M33
MMTAppReg1.MEMORYMAP.DefaultDataRegion=true
MMTAppReg1.MEMORYMAP.IPParameters=StartAddress,Size,CoreName,DefaultDataRegion,ContextName,Name,AP
MMTAppReg1.MEMORYMAP.Name=RAM
MMTAppReg1.MEMORYMAP.Size=655360
MMTAppReg1.MEMORYMAP.StartAddress=0x20000000
MMTAppReg2.MEMORYMAP.AppRegionName=RAM Reserved Alias Region
MMTAppReg2.MEMORYMAP.CoreName=ARM Cortex-M33
MMTAppReg2.MEMORYMAP.DefaultDataRegion=false
MMTAppReg2.MEMORYMAP.IPParameters=StartAddress,Size,CoreName,DefaultDataRegion,ReservedRegion,Name
MMTAppReg2.MEMORYMAP.Name=RAM Reserved Alias Region
MMTAppReg2.MEMORYMAP.ReservedRegion=true
MMTAppReg2.MEMORYMAP.Size=655360
MMTAppReg2.MEMORYMAP.StartAddress=0x0A000000
MMTAppReg3.MEMORYMAP.AP=RO_priv_only
MMTAppReg3.MEMORYMAP.AppRegionName=FLASH
MMTAppReg3.MEMORYMAP.Cacheability=WTRA
MMTAppReg3.MEMORYMAP.ContextName=CortexM33
MMTAppReg3.MEMORYMAP.CoreName=ARM Cortex-M33
MMTAppReg3.MEMORYMAP.DefaultCodeRegion=true
MMTAppReg3.MEMORYMAP.DefaultDataRegion=false
MMTAppReg3.MEMORYMAP.IPParameters=StartAddress,Size,CoreName,DefaultDataRegion,MemType,ContextName,Name,AP,Cacheability,DefaultCodeRegion
MMTAppReg3.MEMORYMAP.MemType=ROM
MMTAppReg3.MEMORYMAP.Name=FLASH
MMTAppReg3.MEMORYMAP.Size=1048576
MMTAppReg3.MEMORYMAP.StartAddress=0x08000000
MMTAppRegionsCount=3
MMTConfigApplied=false
Mcu.CPN=STM32H562RIV6
Mcu.ContextProject=TrustZoneDisabled
Mcu.Family=STM32H5
Mcu.IP0=ADC1
Mcu.IP1=BOOTPATH
Mcu.IP10=MEMORYMAP
Mcu.IP11=NVIC
Mcu.IP12=OCTOSPI1
Mcu.IP13=PWR
Mcu.IP14=RCC
Mcu.IP15=SPI1
Mcu.IP16=SYS
Mcu.IP17=TIM6
Mcu.IP18=USART3
Mcu.IP2=CORTEX_M33_NS
Mcu.IP3=DCACHE1
Mcu.IP4=DEBUG
Mcu.IP5=GPDMA1
Mcu.IP6=I2C1
Mcu.IP7=ICACHE
Mcu.IP8=LPTIM1
Mcu.IP9=LPTIM2
Mcu.IPNb=19
Mcu.Name=STM32H562RIVx
Mcu.Package=VFQFPN68
Mcu.Pin0=PC13
Mcu.Pin1=PC14-OSC32_IN(OSC32_IN)
Mcu.Pin10=PA1
Mcu.Pin11=PA2
Mcu.Pin12=PA3
Mcu.Pin13=PA4
Mcu.Pin14=PA5
Mcu.Pin15=PA6
Mcu.Pin16=PA7
Mcu.Pin17=PC4
Mcu.Pin18=PC5
Mcu.Pin19=PB0
Mcu.Pin2=PC15-OSC32_OUT(OSC32_OUT)
Mcu.Pin20=PB1
Mcu.Pin21=PB2
Mcu.Pin22=PB10
Mcu.Pin23=PB11
Mcu.Pin24=PB12
Mcu.Pin25=PB13
Mcu.Pin26=PB14
Mcu.Pin27=PB15
Mcu.Pin28=PD11
Mcu.Pin29=PA8
Mcu.Pin3=PH0-OSC_IN(PH0)
Mcu.Pin30=PA9
Mcu.Pin31=PA10
Mcu.Pin32=PA13(JTMS/SWDIO)
Mcu.Pin33=PA14(JTCK/SWCLK)
Mcu.Pin34=PA15(JTDI)
Mcu.Pin35=PC10
Mcu.Pin36=PC11
Mcu.Pin37=PC12
Mcu.Pin38=PB3(JTDO/TRACESWO)
Mcu.Pin39=PB4(NJTRST)
Mcu.Pin4=PH1-OSC_OUT(PH1)
Mcu.Pin40=PB5
Mcu.Pin41=PB6
Mcu.Pin42=PB7
Mcu.Pin43=PB8
Mcu.Pin44=PB9
Mcu.Pin45=PE0
Mcu.Pin46=VP_ADC1_TempSens_Input
Mcu.Pin47=VP_ADC1_Vref_Input
Mcu.Pin48=VP_CORTEX_M33_NS_VS_Hclk
Mcu.Pin49=VP_DCACHE1_VS_DCACHE
Mcu.Pin5=PC0
Mcu.Pin50=VP_GPDMA1_VS_GPDMACH0
Mcu.Pin51=VP_GPDMA1_VS_GPDMACH1
Mcu.Pin52=VP_GPDMA1_VS_GPDMACH2
Mcu.Pin53=VP_GPDMA1_VS_GPDMACH3
Mcu.Pin54=VP_ICACHE_VS_ICACHE
Mcu.Pin55=VP_LPTIM1_VS_LPTIM_counterModeInternalClock
Mcu.Pin56=VP_LPTIM2_VS_LPTIM_counterModeInternalClock
Mcu.Pin57=VP_PWR_VS_SECSignals
Mcu.Pin58=VP_PWR_VS_LPOM
Mcu.Pin59=VP_PWR_VS_DBSignals
Mcu.Pin6=PC1
Mcu.Pin60=VP_SYS_VS_Systick
Mcu.Pin61=VP_TIM6_VS_ClockSourceINT
Mcu.Pin62=VP_BOOTPATH_VS_BOOTPATH
Mcu.Pin63=VP_MEMORYMAP_VS_MEMORYMAP
Mcu.Pin7=PC2
Mcu.Pin8=PC3
Mcu.Pin9=PA0
Mcu.PinsNb=64
Mcu.ThirdPartyNb=0
Mcu.UserConstants=
Mcu.UserName=STM32H562RIVx
MxCube.Version=6.15.0
MxDb.Version=DB.6.0.150
NVIC.BusFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.DebugMonitor_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.EXTI0_IRQn=true\:0\:0\:true\:false\:true\:true\:true\:true
NVIC.EXTI11_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.EXTI13_IRQn=true\:15\:0\:true\:false\:true\:true\:true\:true
NVIC.EXTI14_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.EXTI15_IRQn=true\:14\:0\:true\:false\:true\:true\:true\:true
NVIC.EXTI1_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.EXTI4_IRQn=true\:13\:0\:true\:false\:true\:true\:true\:true
NVIC.EXTI6_IRQn=true\:0\:0\:true\:false\:true\:true\:true\:true
NVIC.EXTI8_IRQn=true\:10\:0\:true\:false\:true\:true\:true\:true
NVIC.ForceEnableDMAVector=true
NVIC.GPDMA1_Channel0_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.GPDMA1_Channel1_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.GPDMA1_Channel2_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.GPDMA1_Channel3_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.HardFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.LPTIM1_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.LPTIM2_IRQn=true\:15\:0\:true\:false\:true\:true\:true\:true
NVIC.MemoryManagement_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.NonMaskableInt_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.OCTOSPI1_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.PendSV_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PriorityGroup=NVIC_PRIORITYGROUP_4
NVIC.SVCall_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.SysTick_IRQn=true\:15\:0\:false\:false\:true\:false\:true\:false
NVIC.USART3_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.UsageFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
OCTOSPI1.ChipSelectHighTimeCycle=1
OCTOSPI1.ClockMode=HAL_XSPI_CLOCK_MODE_3
OCTOSPI1.ClockPrescaler=4
OCTOSPI1.FifoThresholdByte=4
OCTOSPI1.IPParameters=FifoThresholdByte,MemorySize,ChipSelectHighTimeCycle,ClockMode,ClockPrescaler,SampleShifting
OCTOSPI1.MemorySize=HAL_XSPI_SIZE_8MB
OCTOSPI1.SampleShifting=HAL_XSPI_SAMPLE_SHIFT_NONE
PA0.GPIOParameters=GPIO_PuPd,GPIO_Label,GPIO_ModeDefaultEXTI
PA0.GPIO_Label=WKUP_EXTI0
PA0.GPIO_ModeDefaultEXTI=GPIO_MODE_IT_RISING_FALLING
PA0.GPIO_PuPd=GPIO_NOPULL
PA0.Locked=true
PA0.Signal=GPXTI0
PA1.Locked=true
PA1.Mode=OCTOSPI1_IO[3\:0]
PA1.Signal=OCTOSPI1_IO3
PA10.GPIOParameters=PinState,GPIO_Label
PA10.GPIO_Label=BOOST_EN
PA10.Locked=true
PA10.PinState=GPIO_PIN_RESET
PA10.Signal=GPIO_Output
PA13(JTMS/SWDIO).Mode=Serial_Wire
PA13(JTMS/SWDIO).Signal=DEBUG_JTMS-SWDIO
PA14(JTCK/SWCLK).Mode=Serial_Wire
PA14(JTCK/SWCLK).Signal=DEBUG_JTCK-SWCLK
PA15(JTDI).GPIOParameters=GPIO_Label
PA15(JTDI).GPIO_Label=BT_EN
PA15(JTDI).Locked=true
PA15(JTDI).Signal=GPIO_Output
PA2.GPIOParameters=GPIO_PuPd,GPIO_Label
PA2.GPIO_Label=BL_NTF
PA2.GPIO_PuPd=GPIO_PULLUP
PA2.Locked=true
PA2.Signal=GPIO_Input
PA3.Locked=true
PA3.Mode=quad_mode
PA3.Signal=OCTOSPI1_CLK
PA4.GPIOParameters=GPIO_Label
PA4.GPIO_Label=RF_CSN
PA4.Locked=true
PA4.Signal=GPIO_Output
PA5.GPIOParameters=GPIO_PuPd,GPIO_Label
PA5.GPIO_Label=RF_SCK
PA5.GPIO_PuPd=GPIO_NOPULL
PA5.Locked=true
PA5.Mode=Full_Duplex_Master
PA5.Signal=SPI1_SCK
PA6.GPIOParameters=GPIO_Label
PA6.GPIO_Label=RF_MISO
PA6.Locked=true
PA6.Mode=Full_Duplex_Master
PA6.Signal=SPI1_MISO
PA7.GPIOParameters=GPIO_Label
PA7.GPIO_Label=RF_MOSI
PA7.Locked=true
PA7.Mode=Full_Duplex_Master
PA7.Signal=SPI1_MOSI
PA8.GPIOParameters=GPIO_PuPd,GPIO_Label,GPIO_ModeDefaultEXTI
PA8.GPIO_Label=ENCODE_A
PA8.GPIO_ModeDefaultEXTI=GPIO_MODE_IT_RISING_FALLING
PA8.GPIO_PuPd=GPIO_PULLUP
PA8.Locked=true
PA8.Signal=GPXTI8
PA9.GPIOParameters=GPIO_PuPd,GPIO_Label
PA9.GPIO_Label=ENCODE_B
PA9.GPIO_PuPd=GPIO_PULLUP
PA9.Locked=true
PA9.Signal=GPIO_Input
PB0.Locked=true
PB0.Mode=OCTOSPI1_IO[3\:0]
PB0.Signal=OCTOSPI1_IO1
PB1.Locked=true
PB1.Signal=ADCx_INP5
PB10.Mode=quad_mode
PB10.Signal=OCTOSPI1_NCS
PB11.GPIOParameters=GPIO_Label
PB11.GPIO_Label=TEST_1
PB11.Locked=true
PB11.Signal=GPIO_Output
PB12.GPIOParameters=GPIO_Label
PB12.GPIO_Label=NIKON_SCS
PB12.Locked=true
PB12.Signal=GPIO_Output
PB13.GPIOParameters=PinState,GPIO_Label
PB13.GPIO_Label=NIKON_SCK
PB13.Locked=true
PB13.PinState=GPIO_PIN_SET
PB13.Signal=GPIO_Output
PB14.GPIOParameters=GPIO_PuPd,GPIO_Label,GPIO_ModeDefaultEXTI
PB14.GPIO_Label=ENABLE_EXTI14
PB14.GPIO_ModeDefaultEXTI=GPIO_MODE_IT_FALLING
PB14.GPIO_PuPd=GPIO_PULLUP
PB14.Locked=true
PB14.Signal=GPXTI14
PB15.GPIOParameters=GPIO_PuPd,GPIO_Label,GPIO_ModeDefaultEXTI
PB15.GPIO_Label=NIKON_MOSI
PB15.GPIO_ModeDefaultEXTI=GPIO_MODE_IT_FALLING
PB15.GPIO_PuPd=GPIO_NOPULL
PB15.Locked=true
PB15.Signal=GPXTI15
PB2.GPIOParameters=PinState,GPIO_Label
PB2.GPIO_Label=CAM_COMM
PB2.Locked=true
PB2.PinState=GPIO_PIN_RESET
PB2.Signal=GPIO_Output
PB3(JTDO/TRACESWO).GPIOParameters=GPIO_Label
PB3(JTDO/TRACESWO).GPIO_Label=RF_PA_RXEN
PB3(JTDO/TRACESWO).Locked=true
PB3(JTDO/TRACESWO).Signal=GPIO_Output
PB4(NJTRST).GPIOParameters=GPIO_Label
PB4(NJTRST).GPIO_Label=RF_PA_TXEN
PB4(NJTRST).Locked=true
PB4(NJTRST).Signal=GPIO_Output
PB5.GPIOParameters=GPIO_PuPd,GPIO_Label
PB5.GPIO_Label=RF_IRQ
PB5.GPIO_PuPd=GPIO_PULLDOWN
PB5.Locked=true
PB5.Signal=GPIO_Input
PB6.GPIOParameters=GPIO_PuPd,GPIO_Label,GPIO_ModeDefaultEXTI
PB6.GPIO_Label=AMOLED_TE
PB6.GPIO_ModeDefaultEXTI=GPIO_MODE_IT_FALLING
PB6.GPIO_PuPd=GPIO_PULLDOWN
PB6.Locked=true
PB6.Signal=GPXTI6
PB7.GPIOParameters=GPIO_Label
PB7.GPIO_Label=AMOLED_RST
PB7.Locked=true
PB7.Signal=GPIO_Output
PB8.GPIOParameters=GPIO_Speed,GPIO_Label,GPIO_Pu
PB8.GPIO_Label=TP_SCL
PB8.GPIO_Pu=GPIO_PULLUP
PB8.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PB8.Locked=true
PB8.Mode=I2C
PB8.Signal=I2C1_SCL
PB9.GPIOParameters=GPIO_Speed,GPIO_Label,GPIO_Pu
PB9.GPIO_Label=TP_SDA
PB9.GPIO_Pu=GPIO_PULLUP
PB9.GPIO_Speed=GPIO_SPEED_FREQ_VERY_HIGH
PB9.Locked=true
PB9.Mode=I2C
PB9.Signal=I2C1_SDA
PC0.GPIOParameters=GPIO_PuPd,GPIO_Label
PC0.GPIO_Label=KEY_FLASH
PC0.GPIO_PuPd=GPIO_PULLUP
PC0.Locked=true
PC0.Signal=GPIO_Input
PC1.GPIOParameters=GPIO_PuPd,GPIO_Label,GPIO_ModeDefaultEXTI
PC1.GPIO_Label=FLASH_TRI
PC1.GPIO_ModeDefaultEXTI=GPIO_MODE_IT_FALLING
PC1.GPIO_PuPd=GPIO_PULLUP
PC1.Locked=true
PC1.Signal=GPXTI1
PC10.Mode=Asynchronous
PC10.Signal=USART3_TX
PC11.Mode=Asynchronous
PC11.Signal=USART3_RX
PC12.GPIOParameters=GPIO_Label
PC12.GPIO_Label=BT_RST
PC12.Locked=true
PC12.Signal=GPIO_Output
PC13.GPIOParameters=GPIO_PuPd,GPIO_Label,GPIO_ModeDefaultEXTI
PC13.GPIO_Label=TP_INT_EXTI13
PC13.GPIO_ModeDefaultEXTI=GPIO_MODE_IT_RISING
PC13.GPIO_PuPd=GPIO_PULLUP
PC13.Locked=true
PC13.Signal=GPXTI13
PC14-OSC32_IN(OSC32_IN).GPIOParameters=GPIO_PuPd,GPIO_Label
PC14-OSC32_IN(OSC32_IN).GPIO_Label=KEY_MODE
PC14-OSC32_IN(OSC32_IN).GPIO_PuPd=GPIO_PULLUP
PC14-OSC32_IN(OSC32_IN).Locked=true
PC14-OSC32_IN(OSC32_IN).Signal=GPIO_Input
PC15-OSC32_OUT(OSC32_OUT).GPIOParameters=GPIO_PuPd,GPIO_Label
PC15-OSC32_OUT(OSC32_OUT).GPIO_Label=KEY_ENTER
PC15-OSC32_OUT(OSC32_OUT).GPIO_PuPd=GPIO_PULLUP
PC15-OSC32_OUT(OSC32_OUT).Locked=true
PC15-OSC32_OUT(OSC32_OUT).Signal=GPIO_Input
PC2.Locked=true
PC2.Mode=OCTOSPI1_IO[3\:0]
PC2.Signal=OCTOSPI1_IO2
PC3.Locked=true
PC3.Mode=OCTOSPI1_IO[3\:0]
PC3.Signal=OCTOSPI1_IO0
PC4.GPIOParameters=GPIO_PuPd,GPIO_Label,GPIO_ModeDefaultEXTI
PC4.GPIO_Label=CHG_OK_EXTI4
PC4.GPIO_ModeDefaultEXTI=GPIO_MODE_IT_RISING_FALLING
PC4.GPIO_PuPd=GPIO_NOPULL
PC4.Locked=true
PC4.Signal=GPXTI4
PC5.GPIOParameters=PinState,GPIO_Label
PC5.GPIO_Label=LED_RED
PC5.Locked=true
PC5.PinState=GPIO_PIN_RESET
PC5.Signal=GPIO_Output
PD11.GPIOParameters=GPIO_PuPd,GPIO_Label,GPIO_ModeDefaultEXTI
PD11.GPIO_Label=MAIN_SWITCH
PD11.GPIO_ModeDefaultEXTI=GPIO_MODE_IT_FALLING
PD11.GPIO_PuPd=GPIO_PULLUP
PD11.Locked=true
PD11.Signal=GPXTI11
PE0.GPIOParameters=GPIO_Label
PE0.GPIO_Label=TP_RST
PE0.Locked=true
PE0.Signal=GPIO_Output
PH0-OSC_IN(PH0).Mode=HSE-External-Oscillator
PH0-OSC_IN(PH0).Signal=RCC_OSC_IN
PH1-OSC_OUT(PH1).Mode=HSE-External-Oscillator
PH1-OSC_OUT(PH1).Signal=RCC_OSC_OUT
PinOutPanel.RotationAngle=0
ProjectManager.AskForMigrate=true
ProjectManager.BackupPrevious=false
ProjectManager.CompilerLinker=GCC
ProjectManager.CompilerOptimize=6
ProjectManager.ComputerToolchain=false
ProjectManager.CoupleFile=false
ProjectManager.CustomerFirmwarePackage=
ProjectManager.DefaultFWLocation=true
ProjectManager.DeletePrevious=true
ProjectManager.DeviceId=STM32H562RIVx
ProjectManager.FirmwarePackage=STM32Cube FW_H5 V1.5.0
ProjectManager.FreePins=true
ProjectManager.HalAssertFull=false
ProjectManager.HeapSize=0x3000
ProjectManager.KeepUserCode=true
ProjectManager.LastFirmware=true
ProjectManager.LibraryCopy=1
ProjectManager.MainLocation=Core/Src
ProjectManager.NoMain=false
ProjectManager.PreviousToolchain=STM32CubeIDE
ProjectManager.ProjectBuild=false
ProjectManager.ProjectFileName=QZ-N.ioc
ProjectManager.ProjectName=QZ-N
ProjectManager.ProjectStructure=
ProjectManager.RegisterCallBack=
ProjectManager.StackSize=0x3000
ProjectManager.TargetToolchain=MDK-ARM V5.37
ProjectManager.ToolChainLocation=
ProjectManager.UAScriptAfterPath=
ProjectManager.UAScriptBeforePath=
ProjectManager.UnderRoot=false
ProjectManager.functionlistsort=1-SystemClock_Config-RCC-false-HAL-false,2-MX_GPIO_Init-GPIO-false-HAL-false,3-MX_GPDMA1_Init-GPDMA1-false-HAL-false,4-MX_OCTOSPI1_Init-OCTOSPI1-false-HAL-false,5-MX_ICACHE_Init-ICACHE-false-HAL-false,6-MX_TIM6_Init-TIM6-false-HAL-false,7-MX_I2C1_Init-I2C1-false-HAL-false,8-MX_DCACHE1_Init-DCACHE1-false-HAL-false,9-MX_ADC1_Init-ADC1-false-HAL-false,10-MX_SPI1_Init-SPI1-false-HAL-false,11-MX_LPTIM1_Init-LPTIM1-false-HAL-true,12-MX_LPTIM2_Init-LPTIM2-false-HAL-true,13-MX_USART3_UART_Init-USART3-false-HAL-false,0-MX_CORTEX_M33_NS_Init-CORTEX_M33_NS-false-HAL-true,0-MX_PWR_Init-PWR-false-HAL-true
RCC.ADCCLockSelection=RCC_ADCDACCLKSOURCE_HSE
RCC.ADCFreq_Value=25000000
RCC.AHBFreq_Value=*********
RCC.APB1Freq_Value=*********
RCC.APB1TimFreq_Value=*********
RCC.APB2Freq_Value=*********
RCC.APB2TimFreq_Value=*********
RCC.APB3Freq_Value=*********
RCC.CECFreq_Value=32000
RCC.CKPERFreq_Value=32000000
RCC.CRSFreq_Value=48000000
RCC.CSI_VALUE=4000000
RCC.CortexFreq_Value=*********
RCC.DACFreq_Value=32768
RCC.EPOD_VALUE=25000000
RCC.FCLKCortexFreq_Value=*********
RCC.FDCANFreq_Value=25000000
RCC.FamilyName=M
RCC.HCLKFreq_Value=*********
RCC.HSE_VALUE=25000000
RCC.HSI48_VALUE=48000000
RCC.HSI_VALUE=64000000
RCC.I2C1Freq_Value=*********
RCC.I2C2Freq_Value=*********
RCC.I2C3Freq_Value=*********
RCC.I2C4Freq_Value=*********
RCC.I3C1Freq_Value=*********
RCC.IPParameters=ADCCLockSelection,ADCFreq_Value,AHBFreq_Value,APB1Freq_Value,APB1TimFreq_Value,APB2Freq_Value,APB2TimFreq_Value,APB3Freq_Value,CECFreq_Value,CKPERFreq_Value,CRSFreq_Value,CSI_VALUE,CortexFreq_Value,DACFreq_Value,EPOD_VALUE,FCLKCortexFreq_Value,FDCANFreq_Value,FamilyName,HCLKFreq_Value,HSE_VALUE,HSI48_VALUE,HSI_VALUE,I2C1Freq_Value,I2C2Freq_Value,I2C3Freq_Value,I2C4Freq_Value,I3C1Freq_Value,LPTIM1CLockSelection,LPTIM1Freq_Value,LPTIM2Freq_Value,LPTIM3Freq_Value,LPTIM4Freq_Value,LPTIM5Freq_Value,LPTIM6Freq_Value,LPUART1Freq_Value,LSCOPinFreq_Value,LSE_VALUE,LSIRC_VALUE,MCO1PinFreq_Value,MCO2PinFreq_Value,OCTOSPIMFreq_Value,PLL2FRACN,PLL2N,PLL2PoutputFreq_Value,PLL2QoutputFreq_Value,PLL2RoutputFreq_Value,PLL3FRACN,PLL3PoutputFreq_Value,PLL3QoutputFreq_Value,PLL3RoutputFreq_Value,PLLFRACN,PLLM,PLLN,PLLPoutputFreq_Value,PLLQoutputFreq_Value,PLLSourceVirtual,PWRFreq_Value,RNGFreq_Value,SAI1Freq_Value,SAI2Freq_Value,SDMMC1Freq_Value,SPI1CLockSelection,SPI1Freq_Value,SPI2Freq_Value,SPI3Freq_Value,SPI6Freq_Value,SYSCLKFreq_VALUE,SYSCLKSource,UART4Freq_Value,UART5Freq_Value,UART7Freq_Value,UCPD1outputFreq_Value,USART11Freq_Value,USART1Freq_Value,USART2Freq_Value,USART3Freq_Value,USART6Freq_Value,USBFreq_Value,VCOInput2Freq_Value,VCOInput3Freq_Value,VCOInputFreq_Value,VCOOutputFreq_Value,VCOPLL2OutputFreq_Value,VCOPLL3OutputFreq_Value,VDD_VALUE
RCC.LPTIM1CLockSelection=RCC_LPTIM1CLKSOURCE_LSI
RCC.LPTIM1Freq_Value=32000
RCC.LPTIM2Freq_Value=*********
RCC.LPTIM3Freq_Value=*********
RCC.LPTIM4Freq_Value=*********
RCC.LPTIM5Freq_Value=*********
RCC.LPTIM6Freq_Value=*********
RCC.LPUART1Freq_Value=*********
RCC.LSCOPinFreq_Value=32000
RCC.LSE_VALUE=32768
RCC.LSIRC_VALUE=32000
RCC.MCO1PinFreq_Value=32000000
RCC.MCO2PinFreq_Value=*********
RCC.OCTOSPIMFreq_Value=*********
RCC.PLL2FRACN=0
RCC.PLL2N=32
RCC.PLL2PoutputFreq_Value=64000000
RCC.PLL2QoutputFreq_Value=64000000
RCC.PLL2RoutputFreq_Value=64000000
RCC.PLL3FRACN=0
RCC.PLL3PoutputFreq_Value=*********
RCC.PLL3QoutputFreq_Value=*********
RCC.PLL3RoutputFreq_Value=*********
RCC.PLLFRACN=0
RCC.PLLM=2
RCC.PLLN=40
RCC.PLLPoutputFreq_Value=*********
RCC.PLLQoutputFreq_Value=*********
RCC.PLLSourceVirtual=RCC_PLL1_SOURCE_HSE
RCC.PWRFreq_Value=*********
RCC.RNGFreq_Value=48000000
RCC.SAI1Freq_Value=64000000
RCC.SAI2Freq_Value=64000000
RCC.SDMMC1Freq_Value=*********
RCC.SPI1CLockSelection=RCC_SPI1CLKSOURCE_PLL2P
RCC.SPI1Freq_Value=64000000
RCC.SPI2Freq_Value=*********
RCC.SPI3Freq_Value=*********
RCC.SPI6Freq_Value=*********
RCC.SYSCLKFreq_VALUE=*********
RCC.SYSCLKSource=RCC_SYSCLKSOURCE_PLLCLK
RCC.UART4Freq_Value=*********
RCC.UART5Freq_Value=*********
RCC.UART7Freq_Value=*********
RCC.UCPD1outputFreq_Value=8000000
RCC.USART11Freq_Value=*********
RCC.USART1Freq_Value=*********
RCC.USART2Freq_Value=*********
RCC.USART3Freq_Value=*********
RCC.USART6Freq_Value=*********
RCC.USBFreq_Value=48000000
RCC.VCOInput2Freq_Value=4000000
RCC.VCOInput3Freq_Value=4000000
RCC.VCOInputFreq_Value=12500000
RCC.VCOOutputFreq_Value=*********
RCC.VCOPLL2OutputFreq_Value=*********
RCC.VCOPLL3OutputFreq_Value=516000000
RCC.VDD_VALUE=2.7
SH.ADCx_INP5.0=ADC1_INP5,IN5-Single-Ended
SH.ADCx_INP5.ConfNb=1
SH.GPXTI0.0=GPIO_EXTI0
SH.GPXTI0.ConfNb=1
SH.GPXTI1.0=GPIO_EXTI1
SH.GPXTI1.ConfNb=1
SH.GPXTI11.0=GPIO_EXTI11
SH.GPXTI11.ConfNb=1
SH.GPXTI13.0=GPIO_EXTI13
SH.GPXTI13.ConfNb=1
SH.GPXTI14.0=GPIO_EXTI14
SH.GPXTI14.ConfNb=1
SH.GPXTI15.0=GPIO_EXTI15
SH.GPXTI15.ConfNb=1
SH.GPXTI4.0=GPIO_EXTI4
SH.GPXTI4.ConfNb=1
SH.GPXTI6.0=GPIO_EXTI6
SH.GPXTI6.ConfNb=1
SH.GPXTI8.0=GPIO_EXTI8
SH.GPXTI8.ConfNb=1
SPI1.BaudRatePrescaler=SPI_BAUDRATEPRESCALER_16
SPI1.CalculateBaudRate=4.0 MBits/s
SPI1.DataSize=SPI_DATASIZE_8BIT
SPI1.Direction=SPI_DIRECTION_2LINES
SPI1.IPParameters=VirtualType,Mode,Direction,CalculateBaudRate,BaudRatePrescaler,DataSize,MasterInterDataIdleness
SPI1.MasterInterDataIdleness=SPI_MASTER_INTERDATA_IDLENESS_05CYCLE
SPI1.Mode=SPI_MODE_MASTER
SPI1.VirtualType=VM_MASTER
TIM6.IPParameters=Prescaler
TIM6.Prescaler=(250-1)
USART3.BaudRate=1000000
USART3.IPParameters=VirtualMode-Asynchronous,BaudRate
USART3.VirtualMode-Asynchronous=VM_ASYNC
VP_ADC1_TempSens_Input.Mode=IN-TempSens
VP_ADC1_TempSens_Input.Signal=ADC1_TempSens_Input
VP_ADC1_Vref_Input.Mode=IN-Vrefint
VP_ADC1_Vref_Input.Signal=ADC1_Vref_Input
VP_BOOTPATH_VS_BOOTPATH.Mode=BP_Activate
VP_BOOTPATH_VS_BOOTPATH.Signal=BOOTPATH_VS_BOOTPATH
VP_CORTEX_M33_NS_VS_Hclk.Mode=Hclk_Mode
VP_CORTEX_M33_NS_VS_Hclk.Signal=CORTEX_M33_NS_VS_Hclk
VP_DCACHE1_VS_DCACHE.Mode=DCACHE_Activate
VP_DCACHE1_VS_DCACHE.Signal=DCACHE1_VS_DCACHE
VP_GPDMA1_VS_GPDMACH0.Mode=SIMPLEREQUEST_GPDMACH0
VP_GPDMA1_VS_GPDMACH0.Signal=GPDMA1_VS_GPDMACH0
VP_GPDMA1_VS_GPDMACH1.Mode=SIMPLEREQUEST_GPDMACH1
VP_GPDMA1_VS_GPDMACH1.Signal=GPDMA1_VS_GPDMACH1
VP_GPDMA1_VS_GPDMACH2.Mode=SIMPLEREQUEST_GPDMACH2
VP_GPDMA1_VS_GPDMACH2.Signal=GPDMA1_VS_GPDMACH2
VP_GPDMA1_VS_GPDMACH3.Mode=SIMPLEREQUEST_GPDMACH3
VP_GPDMA1_VS_GPDMACH3.Signal=GPDMA1_VS_GPDMACH3
VP_ICACHE_VS_ICACHE.Mode=DefaultMode
VP_ICACHE_VS_ICACHE.Signal=ICACHE_VS_ICACHE
VP_LPTIM1_VS_LPTIM_counterModeInternalClock.Mode=Counts__internal_clock_event_00
VP_LPTIM1_VS_LPTIM_counterModeInternalClock.Signal=LPTIM1_VS_LPTIM_counterModeInternalClock
VP_LPTIM2_VS_LPTIM_counterModeInternalClock.Mode=Counts__internal_clock_event_00
VP_LPTIM2_VS_LPTIM_counterModeInternalClock.Signal=LPTIM2_VS_LPTIM_counterModeInternalClock
VP_MEMORYMAP_VS_MEMORYMAP.Mode=CurAppReg
VP_MEMORYMAP_VS_MEMORYMAP.Signal=MEMORYMAP_VS_MEMORYMAP
VP_PWR_VS_DBSignals.Mode=DisableDeadBatterySignals
VP_PWR_VS_DBSignals.Signal=PWR_VS_DBSignals
VP_PWR_VS_LPOM.Mode=PowerOptimisation
VP_PWR_VS_LPOM.Signal=PWR_VS_LPOM
VP_PWR_VS_SECSignals.Mode=Security/Privilege
VP_PWR_VS_SECSignals.Signal=PWR_VS_SECSignals
VP_SYS_VS_Systick.Mode=SysTick
VP_SYS_VS_Systick.Signal=SYS_VS_Systick
VP_TIM6_VS_ClockSourceINT.Mode=Enable_Timer
VP_TIM6_VS_ClockSourceINT.Signal=TIM6_VS_ClockSourceINT
board=custom
